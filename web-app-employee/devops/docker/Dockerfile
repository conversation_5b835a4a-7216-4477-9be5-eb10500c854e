FROM europe-docker.pkg.dev/metro-markets-cps-artifacts/docker-base/mpdev/monorepo-docker-base/nodejs:14

WORKDIR $WORK_DIR

ENV WORK_DIR=/usr/src/web-app \
    LOG_LEVEL=info \
    APP_SVC_PORT=8000 \
    APP_SVC_MOUNT_POINT=/api \
    APP_SVC_MODE=normal \
    NODE_ENV=production \
    WATCH_MODE=false \
    SSR=false

RUN set -eux \
    && mkdir -p /usr/src/web-app

COPY version $WORK_DIR/
COPY dist/ $WORK_DIR/dist

RUN set -eux \
    && chown -R www-data:www-data ${WORK_DIR}

ARG SENTRY_RELEASE=v0.0.0-dev0
ENV SENTRY_RELEASE=${SENTRY_RELEASE}

USER www-data

ENTRYPOINT ["node", "/usr/src/web-app/dist/app-svc/bundle.js"]
