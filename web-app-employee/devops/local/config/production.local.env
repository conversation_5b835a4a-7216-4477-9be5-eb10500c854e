# prod local launch-only config
LOG_LEVEL=info

APP_SVC_PORT=4010
APP_SVC_MOUNT_POINT=/api
APP_SVC_MODE=normal
NODE_ENV=production
WATCH_MODE=false

CDN_BASE_URL=https://fe-cdn.infra.metro-markets.cloud

SVC_IMS_BASE_URL=http://localhost:8401
SVC_SELLER_OFFICE_BASE_URL=http://localhost:8091
SVC_SELLER_PIM_BASE_URL=http://localhost:8093
SVC_SELLER_INVENTORY_BASE_URL=http://localhost:8094
SVC_USER_ACCOUNT_BASE_URL=http://localhost:9011
SVC_CHECKOUT_BASE_URL=http://localhost:9012
SVC_STOREFRONT_BASE_URL=http://localhost:9021
SVC_SEARCH_BASE_URL=http://localhost:9022
SVC_ORDER_MANAGEMENT_BASE_URL=http://localhost:9013
SVC_EMPLOYEE_BASE_URL=http://localhost:9087
SVC_CATEGORY_BASE_URL=http://localhost:8299
SVC_DISCOUNT_BASE_URL=http://localhost:8404
APP_BUYER_ACCOUNT_BASE_URL=http://localhost:9018
SVC_FINANCIAL_REPORTS_BASE_URL=http://localhost:8405
SVC_DISPUTES_SERVICE_BASE_URL=http://localhost:8405
SVC_FRAUD_BASE_URL=http://localhost:8405
SVC_MESSAGE_CENTER_SERVICE_BASE_URL=http://localhost:8091


APPLICATION_ID=web-app-employee
LAUNCH_ENVIRONMENT=workstation
GCLOUD_PROJECT_ID=metro-markets-dev
GOOGLE_APPLICATION_CREDENTIALS=''
ENABLE_STACK_DRIVER_LOGGING=true
ENABLE_STACK_DRIVER_ERROR_REPORTING=true

IDAM_ADMIN_CLIENT_ID=MM_ADMIN_MASK
IDAM_API_HOST=https://idam-pp.metrosystems.net
IDAM_REALM_ID=MARKETS_RLM

SENTRY_ENABLED=true
SENTRY_DSN=''
SENTRY_RELEASE=''

FF_TEMP_USE_NEW_PAYMENT_SERVICE=0
DISCOUNT_CODE_SELLER_ID=118ede85-fd10-42aa-8ee5-6fbc2553de02

MM_URL_DE_MAIN=https://www.metro.de/marktplatz
MM_URL_ES_MAIN=https://www.makro.es/marketplace
MM_URL_IT_MAIN=https://www.metro.it/marketplace
MM_URL_PT_MAIN=https://www.makro.pt/marketplace
MM_URL_NL_MAIN=https://www.makro.nl/marketplace
MM_URL_FR_MAIN=https://www.metro.fr/marketplace
