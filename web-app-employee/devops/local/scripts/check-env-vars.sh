#!/usr/bin/env bash

BLUE='\033[0;34m'
LBLUE='\033[1;36m'
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW=$(tput setaf 3)
NC='\033[0m' # No Color

function checkVar () {
    printf "$1 ";

    eval value='$'$1

    if [ -z "$value" ]
    then
        printf "${RED}[NOT FOUND]${NC}\n";
        return 1
    else
        printf "${GREEN}[OK]${NC}\n";
        return 0
    fi
}

printf "${LBLUE}Gonna check env vars...${NC}\n";

checkVar APP_SVC_PORT
checkVar APP_SVC_MOUNT_POINT
checkVar NODE_ENV
checkVar WATCH_MODE

checkVar CDN_BASE_URL
checkVar SVC_SELLER_OFFICE_BASE_URL
checkVar SVC_SELLER_PIM_BASE_URL
checkVar SVC_SELLER_INVENTORY_BASE_URL
checkVar SVC_USER_ACCOUNT_BASE_URL
checkVar SVC_CHECKOUT_BASE_URL
checkVar SVC_STOREFRONT_BASE_URL
checkVar SVC_SEARCH_BASE_URL
checkVar SVC_ORDER_MANAGEMENT_BASE_URL
checkVar SVC_EMPLOYEE_BASE_URL
checkVar SVC_CATEGORY_BASE_URL
checkVar CONFIGCAT_SDK_KEY
checkVar DISCOUNT_CODE_SELLER_ID
checkVar MM_URL_DE_MAIN
checkVar MM_URL_ES_MAIN
checkVar MM_URL_IT_MAIN
checkVar MM_URL_PT_MAIN
checkVar MM_URL_NL_MAIN
checkVar MM_URL_FR_MAIN

printf "${LBLUE}Check completed${NC}\n";
