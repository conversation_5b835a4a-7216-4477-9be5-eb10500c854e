import { Component } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { catchError, map, switchMap } from 'rxjs/operators';
import { AccountTypesEnum } from '@buyer-account-app/enums/account-types.enum';
import { UpdateBuyerAccountModel } from '@buyer-account-app/models/update.buyer.account.model';
import { ACCOUNTS_ROUTE, CONSUMERS_ROUTE } from '~shared/constants';
import { ChipTypes } from '~shared/modules/chip';
import { AccountListService } from '@buyer-account-app/modules/accounts/components/account-list/account-list.service';
import { combineLatest, EMPTY } from 'rxjs';
import { isEmpty } from 'lodash';
import { FeatureFlagService } from '@root/core/modules/feature-flag';
import { BuyerAccountDetailsResponse } from '@buyer-account-app/models/buyer-account-details-response.model';
import {BUSINESS_ACCOUNT_API_DETAILS, CONSUMER_ACCOUNT_API_DETAILS, UPDATE_BUYER_ACCOUNT_API} from "@buyer-account-app/modules/accounts/constants/api";

@Component({
  selector: 'app-account-details',
  templateUrl: './account-details.component.html',
  styleUrls: ['./account-details.component.scss'],
})
export class AccountDetailsComponent {
  public backUrlPath = ``;
  public customerStatus: string;
  public isSubsidiaryEnabled: boolean;
  public tenant = '';

  public readonly isConsumer$ = this.activatedRoute.data.pipe(
    map(({ type }) => {
      this.backUrlPath =
        type === AccountTypesEnum.CONSUMER ? `buyer-support/${CONSUMERS_ROUTE}` : `buyer-support/${ACCOUNTS_ROUTE}`;
      return type === AccountTypesEnum.CONSUMER;
    }),
  );

  public accountDetails;
  disableEdit = false;
  protected readonly ChipTypes = ChipTypes;

  constructor(
    private httpClient: HttpClient,
    private activatedRoute: ActivatedRoute,
    private accountListService: AccountListService,
    private featureFlagService: FeatureFlagService,
  ) {
    this.tenant = localStorage.getItem('buyerSupportCountry');
    this.readAccountDetails();
    this.featureFlagService
      .isFeatureEnabled('FF_CCS_BO_SUBSIDIARY')
      .subscribe(status => (this.isSubsidiaryEnabled = status));
  }

  readAccountDetails() {
    combineLatest([this.activatedRoute.data, this.activatedRoute.params])
      .pipe(
        switchMap(([data, params]) => {
          const { type } = data;
          const { id } = params;
          const selectedCountry = this.activatedRoute.snapshot.queryParamMap.get('selectedCountry');
          if (!isEmpty(selectedCountry)) {
            this.tenant = selectedCountry;
            localStorage.setItem('buyerSupportCountry', selectedCountry);
          }
          let urlTemplate =
            type === AccountTypesEnum.CONSUMER
              ? `${CONSUMER_ACCOUNT_API_DETAILS}`
              : `${BUSINESS_ACCOUNT_API_DETAILS}`;
          const url = urlTemplate.replace('{id}', id);

          const headers = new HttpHeaders({
            'Country-Code': this.tenant,
          });

          return this.httpClient.get<BuyerAccountDetailsResponse>(url, { headers });
        }),
      )
      .subscribe(accountDetails => {
        this.accountDetails = accountDetails;
        if (this.isSubsidiaryEnabled && this.accountDetails?.email) {
          this.accountListService.getCustomerStatus(this.accountDetails.email).subscribe(res => {
            this.customerStatus = res;
          });
        }
      });
  }

  saveAccountInfo({ status, blockingReason, email, firstName, lastName, onSuccess, onError }: UpdateBuyerAccountModel) {

    const headers = { 'Country-Code': localStorage.getItem('buyerSupportCountry') };

    return this.activatedRoute.params
      .pipe(
        switchMap(({ id }) =>
            this.httpClient.put(
              UPDATE_BUYER_ACCOUNT_API.replace('{id}', id),
              {
                "firstName": firstName,
                "lastName": lastName,
                "email": email,
                "status": status,
                "blockingReason": blockingReason
              },
              {
                headers
              }
            )
            .pipe(
              catchError(err => {
                onError(err)
                return EMPTY;
              }),
              map(res => {
                return onSuccess(res)
              }),
            ),

        ),
      )
      .subscribe(() => this.readAccountDetails());
  }
}
