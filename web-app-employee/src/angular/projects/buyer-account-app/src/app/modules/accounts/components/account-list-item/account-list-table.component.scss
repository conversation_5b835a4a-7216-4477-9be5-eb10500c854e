@import 'node_modules/@metromarkets/components-17/src/theme';

.mm-table {
  border: 1px solid map_get($baseColors, metro-blue);

  .mm-cell {
    border-bottom-color: map_get($baseColors, metro-blue);
  }
}

.mm-field {
  display: inline-block;
  width: 40%;

  &::ng-deep {
    .ng-select-container {
      background-color: map_get($baseColors, metro-blue);
      color: white;
      font-size: $font-size-regular;
      font-weight: $font-weight-bold;

      &::ng-deep {
        .ng-arrow {
          border-color: white transparent transparent;
        }
      }

      &::ng-deep {
        .ng-placeholder {
          color: white;
          font-size: $font-size-regular;
          font-weight: $font-weight-bold;
        }
      }
    }
  }
}
