import { Component, EventEmitter, Input, Output } from '@angular/core';
import { isFieldValueChanged } from '@buyer-account-app/utils/common.util';

@Component({
  selector: 'app-account-details-edit-field',
  templateUrl: './account-details-edit-field.component.html',
  styleUrls: ['./account-details-edit-field.component.scss'],
})
export class AccountDetailsEditFieldComponent {
  @Input()
  fieldName: string;

  @Input()
  newFieldValue: string;

  @Input()
  cellName: string;

  @Input()
  oldFieldValue: string;

  @Output()
  fieldValueChanged = new EventEmitter();

  isFieldEditable = false;

  onFieldValueChange(event) {
    this.newFieldValue = event.target.value;
    this.fieldValueChanged.emit({ fieldName: this.fieldName, fieldValue: this.newFieldValue });
  }

  onEnableEditFieldValue() {
    this.newFieldValue = this.newFieldValue || this.oldFieldValue;
    this.isFieldEditable = true;
  }

  onCancelFieldValueChanges() {
    this.newFieldValue = null;
    this.isFieldEditable = false;
  }

  onSaveFieldValueChanges() {
    this.isFieldEditable = false;
    this.fieldValueChanged.emit({ fieldName: this.fieldName, fieldValue: this.newFieldValue });
  }

  isFieldValueChanged(): boolean {
    return isFieldValueChanged(this.newFieldValue, this.oldFieldValue);
  }
}
