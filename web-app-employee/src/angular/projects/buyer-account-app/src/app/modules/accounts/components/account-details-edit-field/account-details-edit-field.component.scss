@import 'node_modules/@metromarkets/components-17/src/theme/settings';

.account-details-edit-field {
  border: 1px solid #383838;
  border-bottom: none;
  overflow: auto;
  width: 100%;
  margin-bottom: 40px;

  &__cell {
    width: 30% !important;
    border-bottom: 1px solid #383838;
    border-right: 1px solid #383838;
    color: #002866;
    padding: 25px 40px 23px 45px;
  }

  &__label {
    text-align: left;
    font-size: $font-size-regular;
    font-color: #002866;
    font-weight: $font-weight-bold;
  }

  &__value {
    margin-left: 5px;
    font-size: $font-size-regular;
    font-color: #002866;
  }

  select {
    color: #002866;
    width: 60%;
    margin-left: 10px;
    border: none;
  }

  &__actions {
    margin-left: auto;
  }

  &__save {
    margin-top: 20px;
    float: right;

    &_top {
      top: -65px;
      margin-top: 0;
      margin-bottom: -40px;
      float: right;
    }
  }

  &__data-row {
    display: flex;
    align-items: center;

    input {
      width: 50%;
      margin-left: 10px;
      max-width: 380px;
      height: 26px;
    }

    a {
      padding: 0 10px;
      cursor: pointer;
    }

    &__close-icon,
    &__edit-icon,
    &__check-icon {
      vertical-align: middle;
      width: 17px;
      height: 17px;
    }
  }
}
