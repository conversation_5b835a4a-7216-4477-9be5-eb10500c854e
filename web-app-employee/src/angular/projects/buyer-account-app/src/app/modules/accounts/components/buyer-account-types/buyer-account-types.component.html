<div fxLayout="row nowrap" fxLayoutAlign="space-between center">
  <div class="buyer-account-types__header">
    <h4 class="buyer-account-types__filters">Filters:</h4>
  </div>

  <ul class="chips-list">
    <!-- Types of Buyer Account -->
    <li class="chips-list__item">
      <mm-chip
        class="chips-list__chip"
        [ngClass]="{ 'chips-list__active': selectedAccountType === 'CONSUMER' }"
        (click)="filterBasedOnAccountType('CONSUMER')"
      >
        Consumer
      </mm-chip>
    </li>

    <li class="chips-list__item">
      <mm-chip
        class="chips-list__chip"
        [ngClass]="{ 'chips-list__active': selectedAccountType === 'BUSINESS' }"
        (click)="filterBasedOnAccountType('BUSINESS')"
      >
        Business
      </mm-chip>
    </li>
  </ul>
</div>
