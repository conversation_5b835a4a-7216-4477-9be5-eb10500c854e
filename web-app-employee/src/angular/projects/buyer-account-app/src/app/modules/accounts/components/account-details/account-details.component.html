<div *ngIf="accountDetails" class="account-details">
  <div class="">
    <h2 class="account-details__header">{{ (isConsumer$ | async) ? 'Consumers' : 'Business accounts' }}</h2>
  </div>

  <div class="account-details__sub-header" fxFlex="auto" fxLayout="row">
    <a class="account-details__nav-link" href="{{ backUrlPath }}">
      <mm-icon class="account-details__nav-icon" name="arrow-left"></mm-icon>
    </a>

    <h3 class="account-details__sub-header-content">Account Number: {{ accountDetails?.accountNumber }}</h3>
  </div>
  <ng-container *ngIf="isSubsidiaryEnabled">
  <div style='margin-bottom: 10px;'>
    <ng-container *ngIf='customerStatus?.length > 0'>
      <app-chip [type]='ChipTypes.yellow'>
        {{ customerStatus }}
      </app-chip>
    </ng-container>
  </div>
  </ng-container>
  <app-account-details-table
    (saveInfo)="saveAccountInfo($event)"
    [accountDetails]="accountDetails"
    [disableEdit]="disableEdit"
    [isConsumer]="isConsumer$ | async"
    [selectedCountry]="tenant"
  >
  </app-account-details-table>
</div>
