@import 'node_modules/@metromarkets/components-17/src/theme';

.buyer-accounts {
  &__container {
    padding: 60px;
  }

  &__searchBox {
    margin-bottom: 21px;
  }

  &__countryDropdown {
    margin-right: 20px;
    margin-bottom: 20px;
  }

  &__export {
    margin-top: -18px;
  }

  &__accountsTable {
    min-width: 100%;

    &__loader {
      position: relative;
      left: calc(50% - 16px);
    }
  }

  &__paginator {
    margin: 0 auto;

    ::ng-deep {
      /* TODO(mdc-migration): The following rule targets internal classes of paginator that may no longer apply for the MDC version. */
      .mat-paginator-page-size-select {
        width: 80px;
      }
    }
  }
}

.mm-table {
  border: 1px solid map_get($baseColors, metro-blue);

  .mm-cell {
    border-bottom-color: map_get($baseColors, metro-blue);
  }
}

.mm-field {
  display: inline-block;
  width: 40%;

  &::ng-deep {
    .ng-select-container {
      background-color: map_get($baseColors, metro-blue);
      color: white;
      font-size: $font-size-regular;
      font-weight: $font-weight-bold;

      &::ng-deep {
        .ng-arrow {
          border-color: white transparent transparent;
        }
      }

      &::ng-deep {
        .ng-placeholder {
          color: white;
          font-size: $font-size-regular;
          font-weight: $font-weight-bold;
        }
      }
    }
  }
}

.paginator {
  &--hidden {
    display: none;
  }
}


