@import 'node_modules/@metromarkets/components-17/src/theme/settings';

.preview-modal {
  background-color: white;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  border-color: map-get($baseColors, metro-blue);
  border-style: solid;
  border-width: 32px 500px;

  display: flex;
  flex-flow: column nowrap;

  &__header {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    align-items: center;
    font-size: $font-size-regular;
    font-weight: $font-weight-regular;
    color: map-get($baseColors, blue-shade-60);
    padding: 30px 10px 40px 60px;
  }

  &__footer {
    display: flex;
    flex-flow: column nowrap;
    justify-content: space-between;
    align-items: center;
    font-size: 1.14rem;
    font-weight: 700;
    color: #002866;
    padding: 10px 10px;

    &__add_address {
      width: 85%;
      margin-bottom: 60px;
    }
  }

  &__close-button {
    padding: 0 0;
  }

  &__content {
    padding: 0 32px 32px;
    display: flex;
    flex-flow: column nowrap;
    align-items: center;
    overflow: auto;
  }
}

.address-form {
  &__container {
    width: 90%;
  }

  &__row-check {
    width: 90%;
    margin-bottom: 25px;

    &__billing {
      float: left;
    }

    &__shipping {
      float: left;
      margin-left: 20px;
    }
  }

  &__row {
    ::ng-deep mm-field {
      margin-bottom: 25px;
    }

    &--hint {
      color: #667ea3;
      font-size: 14px;
      font-weight: $font-weight-light;
    }
  }

  &__field {
    width: 100%;
  }
}

.address-create__field {
  margin-bottom: 24px;
}

.mm-error {
  font-size: 14px;
}
