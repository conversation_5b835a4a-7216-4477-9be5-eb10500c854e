import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AccountsRoutingModule } from './accounts-routing.module';
import { AccountListComponent } from './components/account-list/account-list.component';
import {
  MmButtonModule,
  MmCheckboxModule,
  MmChipModule,
  MmFlexLayoutModule,
  MmFormFieldModule,
  MmIconModule,
  MmSelectModule,
  MmTableModule,
  MmTooltipModule,
} from '@metromarkets/components-17';
import { MatPaginatorModule } from '@angular/material/paginator';
import { QaLocatorsModule } from '@metromarkets/sdk-17';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { BuyerAccountTypesComponent } from './components/buyer-account-types/buyer-account-types.component';
import { AccountDetailsComponent } from './components/account-details/account-details.component';
import { AccountDetailsTableComponent } from './components/account-details-table/account-details-table.component';
import { AddressDetailsTableComponent } from './components/address-details-table/address-details-table.component';
import { AccountListService } from './components/account-list/account-list.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { AccountListTableComponent } from './components/account-list-item/account-list-table.component';
import { MatButtonModule } from '@angular/material/button';
import { SearchFieldModule } from '~shared/modules/search-field/search-field.module';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { AccountDetailsEditFieldComponent } from './components/account-details-edit-field/account-details-edit-field.component';
import { AddressEditComponent } from '@buyer-account-app/modules/accounts/components/address-edit/address-edit.component';
import { MmPopoverModule } from '~shared/modules/popover/popover.module';
import { CountryIconModule } from '~shared/modules/country-icon/country-icon.module';
import { ChipModule } from '~shared/modules/chip';

@NgModule({
  declarations: [
    AccountListComponent,
    BuyerAccountTypesComponent,
    AddressEditComponent,
    AccountDetailsComponent,
    AccountDetailsTableComponent,
    AddressDetailsTableComponent,
    AccountListTableComponent,
    AccountDetailsEditFieldComponent,
  ],
  imports: [
    CommonModule,
    QaLocatorsModule,
    MatPaginatorModule,
    MmTableModule,
    MatButtonModule,
    MmSelectModule,
    ReactiveFormsModule,
    MmFormFieldModule,
    SearchFieldModule,
    MatIconModule,
    MmIconModule,
    MmChipModule,
    AccountsRoutingModule,
    MmTooltipModule,
    MmPopoverModule,
    NgSelectModule,
    FormsModule,
    MatSnackBarModule,
    MmFlexLayoutModule,
    MmButtonModule,
    CountryIconModule,
    MmCheckboxModule,
    ChipModule,
  ],
  providers: [AccountListService],
})
export class AccountsModule {}
