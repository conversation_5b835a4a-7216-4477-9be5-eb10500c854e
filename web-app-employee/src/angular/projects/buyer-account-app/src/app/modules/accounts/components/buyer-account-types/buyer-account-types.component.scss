@import 'node_modules/@metromarkets/components-17/src/theme';

.buyer-account-types {
  &__header {
    display: inline-block;
  }

  &__filters {
    margin-right: 20px;
    margin-top: -20px;
    font-size: $font-size-regular;
    font-weight: $font-weight-bold;
    color: map_get($baseColors, metro-blue);
  }
}

.chips-list {
  display: -webkit-inline-box;
  flex-wrap: wrap;
  padding: 0;
  margin: 0 0 10px 0;
  list-style: none;

  &__active {
    background-color: #cce0ff !important;
  }

  &__item {
    margin: 0 15px 10px 0;

    &--clear-all {
      display: none;
    }

    &:last-child {
      margin: 0 0 10px 0;
    }
  }

  &__chip {
    background-color: #f2f3f5;
    cursor: pointer;

    & ::ng-deep .mm-chip {
      border: solid 2px transparent;

      &__content {
        font-size: $font-size-small;
        font-weight: $font-weight-light;
        line-height: $line-height-small;
        color: #001432;
      }
    }

    &--clear-all {
      background-color: rgba(255, 255, 255, 0.2);
      cursor: pointer;

      & ::ng-deep .mm-chip {
        border: solid 2px map_get($baseColors, blue-tint-80);

        &__content {
          font-weight: $font-weight-bold;
          font-size: $font-size-small;
          line-height: $line-height-small;
        }
      }
    }
  }
}
