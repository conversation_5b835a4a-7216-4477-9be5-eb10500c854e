<div class="buyer-accounts">
  <div class="buyer-accounts__container" *ngIf="buyerAccounts$ | async as buyerAccounts">
    <div class="search-box__group" fxLayout="row nowrap" fxLayoutAlign="space-between center">
      <div class="buyer-accounts__countryDropdown" fxFlex="20">
        <mm-field fxFlex="100">
          <mm-label>Country: </mm-label>
          <ng-select
            [items]="countries"
            [ngModel]="getSelectedCountry(selectedCountryCode)"
            (change)="selectCountry($event)"
            [disabled]="countries.length < 2"
            [clearable]="false"
            [mmTestTarget]="'agent-countries'"
          >
            Country
          </ng-select>
        </mm-field>
      </div>

      <div class="buyer-accounts__searchBox" fxFlex="65">
        <mm-field fxFlex="100">
          <mm-label>Search query: </mm-label>
          <employee-app-search-field
            [query]="searchQuery$ | async"
            (cleared)="setSearchQuery('')"
            (searched)="setSearchQuery($event)"
            [showSearchButton]="false"
          >
          </employee-app-search-field>
        </mm-field>
      </div>

      <mm-field class="buyer-accounts__export" fxFlex="10">
        <mm-label>Export: </mm-label>
        <ng-select
          [items]="exportFormats"
          class="buyer-accounts__download"
          [disabled]="!buyerAccounts?.totalCount"
          [ngModel]="exportDataFormat$ | async"
          [mmTestTarget]="'filter-by-status'"
          (change)="exportDataFormat$.next($event)"
          placeholder="Export"
          >Export
        </ng-select>
      </mm-field>
    </div>

    <div fxLayout="row">
      <app-buyer-account-types [selectedAccountType]="accountType$ | async"> </app-buyer-account-types>
    </div>

    <div class="buyer-accounts__tableContainer" fxLayout="row">
      <mm-table-loader class="buyer-accounts__accountsTable__loader" *ngIf="buyerAccounts.loading"> </mm-table-loader>

      <app-account-list-table
        class="buyer-accounts__accountsTable"
        [selectedAccountType]="accountType$ | async"
        [buyerAccounts]="buyerAccounts.buyerAccounts"
        [columnNames]="columnNames$ | async"
        [selectedCountry]="selectedCountryCode"
      ></app-account-list-table>
    </div>

    <div fxLayout="row" fxFlexAlign="center" class="paginator-container">
      <mat-paginator
        class="buyer-accounts__paginator"
        [length]="buyerAccounts?.totalCount"
        [pageSize]="(pagination$ | async).limit"
        [pageIndex]="(pagination$ | async).offset"
        [pageSizeOptions]="pageSizeOptions"
        (page)="goToNextPage($event)"
      >
      </mat-paginator>
    </div>
  </div>
</div>
