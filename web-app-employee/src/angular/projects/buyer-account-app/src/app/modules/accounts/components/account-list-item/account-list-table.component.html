<table mm-table class="buyer-accounts__accountsTable" [dataSource]="buyerAccounts">
  <!--Account Number-->
  <ng-container mmColumnDef="accountNumber">
    <th mmHeaderCell *mmHeaderCellDef>Account Number</th>
    <td mmCell [mmTooltip]="accountNumberToolTip" *mmCellDef="let element">
      <a
        [routerLink]="['./', element.cdmId]"
        [queryParams]="{ selectedCountry: selectedCountry}"
      > {{ element.accountNumber }}</a>
      <ng-template #accountNumberToolTip>{{ element.accountNumber }}</ng-template>
    </td>
  </ng-container>

  <!--Full Name-->
  <ng-container *ngIf="selectedAccountType !== 'BUSINESS'" mmColumnDef="fullName">
    <th mmHeaderCell *mmHeaderCellDef>Full Name</th>
    <td
      mmCell
      [mmTooltip]="fullNameToolTip"
      [mmTooltipDisabled]="!element.firstName && !element.lastName"
      *mmCellDef="let element"
    >
      {{ element.firstName }} {{ element.lastName }}
      <ng-template #fullNameToolTip>{{ element.firstName }} {{ element.lastName }}</ng-template>
    </td>
  </ng-container>

  <!--Email Id-->
  <ng-container mmColumnDef="email">
    <th mmHeaderCell *mmHeaderCellDef>Email</th>
    <td mmCell [mmTooltip]="emailToolTip" [mmTooltipDisabled]="!element?.email" *mmCellDef="let element">
      {{ element?.email }}
      <ng-template #emailToolTip>{{ element.email }}</ng-template>
    </td>
  </ng-container>

  <!--Company Name-->
  <ng-container *ngIf="selectedAccountType === 'BUSINESS'" mmColumnDef="companyName">
    <th mmHeaderCell *mmHeaderCellDef>Company Name</th>
    <td mmCell [mmTooltip]="companyNameToolTip" [mmTooltipDisabled]="!element.companyName" *mmCellDef="let element">
      {{ element.companyName }}
      <ng-template #companyNameToolTip>{{ element.companyName }}</ng-template>
    </td>
  </ng-container>

  <!--Role-->
  <ng-container *ngIf="selectedAccountType === 'BUSINESS'" mmColumnDef="role">
    <th mmHeaderCell *mmHeaderCellDef>Role</th>
    <td mmCell *mmCellDef="let element">{{ element.role }}</td>
  </ng-container>

  <!--Status-->
  <ng-container mmColumnDef="status">
    <th mmHeaderCell *mmHeaderCellDef>Status</th>
    <td mmCell *mmCellDef="let element">{{ element.status }}</td>
  </ng-container>

  <!--Blocking Reason-->
  <ng-container *ngIf="selectedAccountType === 'CONSUMER'" mmColumnDef="blockingReason">
    <th mmHeaderCell [mmTooltip]="blockingReasonHeaderToolTip" *mmHeaderCellDef>
      Blocking Reason
      <ng-template #blockingReasonHeaderToolTip>Blocking Reason</ng-template>
    </th>
    <td
      mmCell
      [mmTooltip]="blockingReasonToolTip"
      [mmTooltipDisabled]="!element?.blockingReason"
      *mmCellDef="let element"
    >
      {{ element.blockingReason }}
      <ng-template #blockingReasonToolTip>{{ element.blockingReason }}</ng-template>
    </td>
  </ng-container>

  <!--METRO Card Number-->
  <ng-container mmColumnDef="metroCardNumber">
    <th mmHeaderCell *mmHeaderCellDef>METRO Card</th>
    <td mmCell [mmTooltip]="metroCardNumberToolTip" *mmCellDef="let element">
      {{ element.metroCardNumber }}
      <ng-template #metroCardNumberToolTip>{{ element.metroCardNumber }}</ng-template>
    </td>
  </ng-container>

  <!--TAX Number-->
  <ng-container *ngIf="selectedAccountType === 'BUSINESS'" mmColumnDef="taxNumber">
    <th mmHeaderCell *mmHeaderCellDef>TAX Number</th>
    <td mmCell *mmCellDef="let element">{{ element.taxNumber }}</td>
  </ng-container>

  <!--VAT Number-->
  <ng-container *ngIf="selectedAccountType === 'BUSINESS'" mmColumnDef="vatNumber">
    <th mmHeaderCell *mmHeaderCellDef>VAT Number</th>
    <td mmCell *mmCellDef="let element">{{ element.vatNumber }}</td>
  </ng-container>

  <!--Legal Form-->
  <ng-container *ngIf="selectedAccountType === 'BUSINESS'" mmColumnDef="legalForm">
    <th mmHeaderCell *mmHeaderCellDef>Legal Form</th>
    <td mmCell *mmCellDef="let element">{{ element.legalForm }}</td>
  </ng-container>

  <!--Registered Type-->
  <ng-container mmColumnDef="registrationForm">
    <th mmHeaderCell *mmHeaderCellDef>Registered Type</th>
    <td mmCell *mmCellDef="let element">{{ element.registrationForm }}</td>
  </ng-container>

  <!--Account type-->
  <ng-container mmColumnDef="tier">
    <th mmHeaderCell *mmHeaderCellDef>Account type</th>
    <td mmCell [mmTooltip]="tierToolTip" *mmCellDef="let element">
      {{ element.tier }}
      <ng-template #tierToolTip>{{ element.tier }}</ng-template>
    </td>
  </ng-container>

  <ng-container mmColumnDef="createdAt">
    <th mmHeaderCell [mmTooltip]="createdHeaderToolTip" *mmHeaderCellDef>
      Created
      <ng-template #createdHeaderToolTip>Created</ng-template>
    </th>
    <td mmCell [mmTooltip]="createdToolTip" [mmTooltipDisabled]="!element?.createdAt" *mmCellDef="let element">
      {{ element.createdAt | date }}
      <ng-template #createdToolTip>{{ element.createdAt | date }}</ng-template>
    </td>
  </ng-container>

  <tr mm-header-row *mmHeaderRowDef="columnNames; sticky: true"></tr>
  <tr mm-row *mmRowDef="let row; columns: columnNames"></tr>
</table>
