import { waitForAsync, ComponentFixture, TestBed } from '@angular/core/testing';

import { BuyerAccountTypesComponent } from './buyer-account-types.component';

xdescribe('BuyerAccountTypesComponent', () => {
  let component: BuyerAccountTypesComponent;
  let fixture: ComponentFixture<BuyerAccountTypesComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [BuyerAccountTypesComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BuyerAccountTypesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
