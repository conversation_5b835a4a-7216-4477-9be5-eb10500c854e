import { environment } from '~env/environment';

export const SEARCH_CONSUMER_ACCOUNTS_API = `${environment.appBuyerAccountBaseUrl}/private/api/v1/buyer-accounts/list/consumer`;

export const SEARCH_BUSINESS_ACCOUNTS_API = `${environment.appBuyerAccountBaseUrl}/private/api/v1/buyer-accounts/list/business`;

export const UPDATE_BUYER_ACCOUNT_API = `${environment.appBuyerAccountBaseUrl}/private/api/v1/buyer-accounts/{id}`;

export const UPDATE_BUYER_ADDRESS_API = `${environment.appBuyerAccountBaseUrl}/private/api/v1/buyer-accounts/consumer/{id}/address/{addressId}`;

export const UPDATE_BUISNESS_ADDRESS_API = `${environment.appBuyerAccountBaseUrl}/private/api/v1/buyer-accounts/business/{id}/address/{addressId}`;

export const CONSUMER_ACCOUNT_API_DETAILS = `${environment.appBuyerAccountBaseUrl}/private/api/v1/buyer-accounts/consumer/{id}`;

export const BUSINESS_ACCOUNT_API_DETAILS = `${environment.appBuyerAccountBaseUrl}/private/api/v1/buyer-accounts/business/{id}`;
