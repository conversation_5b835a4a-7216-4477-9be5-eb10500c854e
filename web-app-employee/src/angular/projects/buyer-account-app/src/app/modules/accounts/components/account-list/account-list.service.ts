import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import * as JsonToXML from 'js2xmlparser';
import { convertToCSV, convertToXLS, selectExportFormat } from '@buyer-account-app/utils/export-data.util';
import { ExportDataEnum, ExportDataExtensionEnum } from '@buyer-account-app/enums/export-data.enum';
import { Observable } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { FeatureFlagService } from '~core/modules/feature-flag';
import { OAuthService } from 'angular-oauth2-oidc';
import { map } from 'rxjs/operators';
import { AFTERSALES_ADMIN_API } from '@sales-orders-app/modules/orders/constants';

@Injectable({
  providedIn: 'root',
})
export class AccountListService {

  constructor(
    private httpClient: HttpClient,
    private translateService: TranslateService,
    private oAuthService: OAuthService,
  ) {}

  exportData(dataFormat, buyerAccountsSnapshot) {
    switch (dataFormat) {
      case 'JSON':
        selectExportFormat([JSON.stringify(buyerAccountsSnapshot)], ExportDataEnum.JSON, ExportDataExtensionEnum.JSON);
        break;

      case 'XML':
        selectExportFormat(
          [JsonToXML.parse('buyerAccounts', buyerAccountsSnapshot)],
          ExportDataEnum.XML,
          ExportDataExtensionEnum.XML,
        );
        break;

      case 'CSV':
        selectExportFormat([convertToCSV(buyerAccountsSnapshot)], ExportDataEnum.CSV, '.csv');
        break;

      case 'XLS':
        selectExportFormat([convertToXLS(buyerAccountsSnapshot)], ExportDataEnum.XLSX, ExportDataExtensionEnum.XLSX);
        break;

      default:
        break;
    }
  }

  getCustomerStatus(email: string): Observable<string> {
    const api_endpoint = `${AFTERSALES_ADMIN_API}`;
    const headers = this.requestHeader(`Bearer ${this.oAuthService.getAccessToken()}`);
    return this.httpClient.get(`${api_endpoint}/buyers?filter[email]=${email}`, {headers}).pipe(
      map(response => {
        const {isSubsidiary, tier, vip} = response && response['items'] && response['items'][0];
        let customerStatus = '';

        if (vip) {
          customerStatus = `${this.translateService.instant('BUYER_SUPPORT.CUSTOMER_STATUS.VIP')} - ${tier}`;

          if (isSubsidiary) {
            customerStatus = `${this.translateService.instant('BUYER_SUPPORT.CUSTOMER_STATUS.KEY_ACCOUNT')} - ${customerStatus}`;
          }
        }

        return customerStatus;
      }),
    );
  }

  private requestHeader(auth: string = ''): HttpHeaders {
    return new HttpHeaders({
      Authorization: auth,
      'Content-Type': 'application/json',
      Accept: 'application/json',
    });
  }
}
