import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import { ACCOUNT_STATUSES, STATUS_BLOCKED, STATUS_VALIDATED } from '../../../../constants/account-status';
import { BLOCKING_REASONS } from '../../../../constants/blocking-reasons';
import { NotificationService } from '@buyer-account-app/service/notification.service';
import { isFieldValueChanged } from '@buyer-account-app/utils/common.util';
import {BuyerAccountDetails} from "@buyer-account-app/models/buyer-account-details.model";
import { UpdateBuyerAccountModel } from '@buyer-account-app/models/update.buyer.account.model';

@Component({
  selector: 'app-account-details-table',
  templateUrl: './account-details-table.component.html',
  styleUrls: ['./account-details-table.component.scss'],
})
export class AccountDetailsTableComponent implements OnInit{
  @Input()
  disableEdit = false;

  @Input()
  accountDetails: BuyerAccountDetails;

  @Input()
  isConsumer;

  @Output()
  saveInfo = new EventEmitter();

  @Input()
  selectedCountry;

  accountStatuses = ACCOUNT_STATUSES;
  blockingReasons = BLOCKING_REASONS;

  loading: boolean;

  public selectedBlockingReason: any = null;
  public selectedStatus: any = null;
  public newEmail: string;
  public newFirstName: string;
  public newLastName: string;
  public statusValidatedId = STATUS_VALIDATED;

  constructor(private notificationService: NotificationService) {}

  ngOnInit(): void {
    this.selectedStatus = this.accountStatus;
    this.selectedBlockingReason = this.accountBlockingReason;
  }

  get accountStatus() {
    const accountStatus = this.accountDetails?.status;
    return ACCOUNT_STATUSES.find(({ label }) => label.toLowerCase() === (accountStatus || '').toLowerCase());
  }

  get accountBlockingReason() {
    const accountBlockingReason = this.accountDetails?.blockingReason;
    return BLOCKING_REASONS.find(({ label }) => label.toLowerCase() === (accountBlockingReason || '').toLowerCase());
  }

  get status() {
    return this.selectedStatus || this.accountStatus;
  }

  get blockingReason() {
    return this.selectedBlockingReason || this.accountBlockingReason;
  }

  selectStatus(event) {
    this.selectedStatus = event;
    this.selectedBlockingReason = this.blockingReasons.find(reason=>reason.id===null);
  }

  selectBlockingReason(event) {
    this.selectedBlockingReason = event;
  }

  updateFieldValue({ fieldName, fieldValue }) {
    switch (fieldName) {
      case 'firstName':
        this.newFirstName = fieldValue;
        break;
      case 'lastName':
        this.newLastName = fieldValue;
        break;
      case 'email':
        this.newEmail = fieldValue;
        break;
      default:
        break;
    }
  }

  saveAccountInfo() {
    if (!this.isValidBlockingReason()) {
      return;
    }
    this.checkIfUserIsBlocked();

    this.loading = true;

    const updatedAccountDetails = {
      status: (this.status && this.status.id) || null,

      blockingReason: (this.blockingReason && this.blockingReason.id) || null,

      email: isFieldValueChanged(this.newEmail, this.accountDetails?.email) ? this.newEmail : null,

      firstName: isFieldValueChanged(this.newFirstName, this.accountDetails?.firstName)
        ? this.newFirstName
        : this.accountDetails?.firstName,

      lastName: isFieldValueChanged(this.newLastName, this.accountDetails?.lastName)
        ? this.newLastName
        : this.accountDetails?.lastName,
    };

    this.saveInfo.emit({
      ...updatedAccountDetails,
      onSuccess: ( data:UpdateBuyerAccountModel ) => this.onSavingSuccess(data),
      onError: err => this.onSavingError(err),
    });
  }

  private onSavingSuccess(updatedAccountFields:UpdateBuyerAccountModel) {
    if (updatedAccountFields?.email && !updatedAccountFields?.email.success) {
      this.notificationService.showError('Email is not updated:', `${updatedAccountFields?.email.errors.join(' - ')}`);
    }

    if (updatedAccountFields?.personalName && !updatedAccountFields?.personalName.success) {
      this.notificationService.showError(
        'Personal Name is not updated:',
        `${updatedAccountFields?.personalName.errors.join(' - ')}`,
      );
    }

    if (updatedAccountFields?.accountStatus && !updatedAccountFields?.accountStatus) {
      this.notificationService.showError('Status / Blocking Reason has not been saved.');
    }

    this.notificationService.showNotification('Account has been updated!');
    this.loading = false;
  }

  private onSavingError(err) {
    if (err?.networkError?.error?.errors?.length) {
      err.networkError.error.errors.forEach(e => {
        this.notificationService.showError(e.message);
      });
    }
    this.notificationService.showError('Could not save changes!');
    this.loading = false;
  }

  private isValidBlockingReason(): boolean {
    if (this.status?.id === STATUS_BLOCKED && !this.blockingReason?.id) {
      this.notificationService.showError('Please select blocking reason');
      return false;
    }
    return true;
  }

  private checkIfUserIsBlocked() {
    if (this.status?.id !== STATUS_BLOCKED && this.blockingReason?.id) {
      this.notificationService.showError("Blocking reason can't be selected when user is not blocked");
    }
  }
}
