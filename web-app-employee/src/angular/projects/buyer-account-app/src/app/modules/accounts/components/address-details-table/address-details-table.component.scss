@import 'node_modules/@metromarkets/components-17/src/theme';

.address-details-container {
  h4 {
    margin-top: 40px;
    margin-bottom: 20px;
  }

  &__actions {
    margin-left: 15px;
  }

  &__edit-icon {
    vertical-align: middle;
    width: 17px;
    height: 17px;
  }

  table {
    border-collapse: collapse;
  }

  .address-details-table {
    border: 1px solid #383838;
    border-bottom: none;
    display: block;
    overflow: auto;
    width: auto;

    tr {
      display: block;
    }

    td {
      border-bottom: 1px solid #383838;
      border-right: 1px solid #383838;
      color: #002866;
      padding: 25px 40px 23px 45px;
      width: 50%;
      float: left;
    }

    &__companyName {
      width: 100% !important;
    }

    &__label {
      text-align: left;
      font-size: $font-size-regular;
      font-color: #002866;
      font-weight: $font-weight-bold;
    }

    &__value {
      margin-left: 5px;
      font-size: $font-size-regular;
      font-color: #002866;
    }
  }
}
