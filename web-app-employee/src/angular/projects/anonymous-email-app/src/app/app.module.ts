import { NgModule } from '@angular/core';
import { AnonymousEmailRoutingModule } from './app-routing.module';
import { AnonymousEmailModule } from './modules/anonymous-email/anonymous-email.module';
import { CommonModule } from '@angular/common';
import { MessageCenterModule } from '@anonymous-email-app/modules/message-center/message-center.module';
import { MessageCenterGuard } from '@anonymous-email-app/guards/message-center.guard';

@NgModule({
  declarations: [],
  imports: [CommonModule, AnonymousEmailRoutingModule, AnonymousEmailModule, MessageCenterModule],
  providers: [MessageCenterGuard],
  bootstrap: [],
})
export class AnonymousEmailAppModule {}
