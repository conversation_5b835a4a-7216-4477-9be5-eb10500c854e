import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { FeatureFlagService } from '~core/modules/feature-flag';
import { FeatureToggleEnum } from '~shared/constants';
import { map } from 'rxjs/operators';

@Injectable()
export class MessageCenterGuard {
  constructor(public router: Router, public featureFlagService: FeatureFlagService) {}

  canActivate() {
    return this.featureFlagService
      .isFeatureEnabled(FeatureToggleEnum.FF_CCS_MESSAGE_CENTER_BO, false)
      .pipe(
        map((isEnabled) => {
          if (!isEnabled) {
            return this.router.parseUrl('/');
          }
          return isEnabled;
        })
      );
  }
}
