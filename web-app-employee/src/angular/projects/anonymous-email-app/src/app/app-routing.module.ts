import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { EmailListComponent } from './modules/anonymous-email/components/email-list/email-list.component';
import { EMAILS_ROUTE, MESSAGE_CENTER_ROUTE } from '~shared/constants';
import { MessageCenterGuard } from '@anonymous-email-app/guards/message-center.guard';

const routes: Routes = [
  { path: '', redirectTo: EMAILS_ROUTE, pathMatch: 'full' },
  {
    path: EMAILS_ROUTE,
    component: EmailListComponent,
  },
  {
    path: MESSAGE_CENTER_ROUTE,
    loadChildren: () => import('./modules/message-center/message-center.module').then(m => m.MessageCenterModule),
    canActivate: [MessageCenterGuard]
  },
  {
    path: `${MESSAGE_CENTER_ROUTE}/:chatId`,
    loadChildren: () => import('./modules/message-center/message-center.module').then(m => m.MessageCenterModule),
    canActivate: [MessageCenterGuard]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AnonymousEmailRoutingModule {
}
