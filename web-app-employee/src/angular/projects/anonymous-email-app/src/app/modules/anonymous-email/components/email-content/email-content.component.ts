import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { AnonymousEmail } from '../../models/anonymous-email';
import { AnonymousEmailWithAttachments } from '@anonymous-email-app/modules/anonymous-email/models/anonymous-email-with-attachments';
import { AnonymousEmailContent } from '@anonymous-email-app/modules/anonymous-email/models/anonymous-email-content';
import {ORDER_STATUSES} from "@anonymous-email-app/modules/anonymous-email/constants/order-statuses";
import {SECONDARY_COLUMN_MAX_LENGTH} from "@anonymous-email-app/modules/anonymous-email/constants/table-columns";

@Component({
  selector: 'app-email-content',
  templateUrl: './email-content.component.html',
  styleUrls: ['./email-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EmailContentComponent {
  @Input()
  email: AnonymousEmail;
  @Input()
  emailsWithAttachments: Array<AnonymousEmailWithAttachments>;
  @Input()
  loadingAttachments: boolean;
  STATUS_DATA = ORDER_STATUSES;

  emailHaveAttachments(email: AnonymousEmailContent): boolean {
    return (
      this.emailsWithAttachments !== null &&
      this.emailsWithAttachments.find(emailWithAttachments => emailWithAttachments.emailId === email.id) !== undefined
    );
  }
  getStatusData() {
    if (this.STATUS_DATA[this.email.status] !== undefined) {
      return this.STATUS_DATA[this.email.status];
    }
    return { title: 'N/A', color: '#d6f2e6' };
  }

  protected readonly secondaryColumnMaxLengths = SECONDARY_COLUMN_MAX_LENGTH;
}
