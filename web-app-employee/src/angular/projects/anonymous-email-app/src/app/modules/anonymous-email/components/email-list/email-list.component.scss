.emails {
  display: flex;
  flex-direction: column;
  padding: 12px 64px;
  .emails__no-email-wrapper {
    width: 100%;
    padding: 20px;
    text-align: center;
  }
  .emails___no-email-text {
    font-size: 18px;
  }
}

.emails__table-wrapper {
  padding-top: 20px;
  padding-bottom: 20px;
  flex: 1;
  display: flex;
  flex-direction: row;
  width: 100%;
  max-height: calc(100vh - 215px);

  .mm-table .mm-header-cell {
    width: auto;
  }

  .emails__main-table {
    overflow: scroll;
    overflow-x: visible;

    .mm-row {
      &.late-response {
        background: rgba(255, 0, 0, 0.12);
      }
    }

    &.emails__main-table--flex {
      flex: 1;
      flex-basis: auto;
      display: flex;
    }

    &.emails__main-table--static {
      flex: 0 0 600px;
    }

    .main-table__thread-cell {
      td:nth-child(2) {
        padding-left: 0 !important;
        padding-right: 0 !important;
      }
    }
    .main-table__secondary-column-headers {
      text-align: center;
    }
    .main-table_border {
      border-right: 2px solid #E5E7EA;
    }
  }

  .emails__secondary-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: scroll;
    border: 1px solid #E5E7EA;
    position: relative;

    .emails__emails-table {
      .mm-row {
        min-height: unset;
      }

      .mm-header-cell {
        text-align: center;
      }
    }
  }
}

.emails__emails-table {
  table-layout: fixed;
  width: 100%;
  background-color: #ffffff;
  position: sticky;
  top: 0;

  td:first-child,
  th:first-child {
    padding-left: 10px;
  }

  td:last-child,
  th:last-child {
    padding-right: 10px;
  }

  td {
    border-bottom: 1px solid #E5E7EA;
    justify-content: center;
    flex-direction: column;
    padding: 16px 10px;
    vertical-align: middle;
    text-align: center;
  }

  th {
    padding: 16px 10px;
  }
  .highlight {
    background: var(--backgrounds-hover, linear-gradient(0deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.95) 100%), #0059E4);
  }
  .clickable {
    cursor: pointer;
  }
  .date-row {
    background-color: grey;
    height: 32px !important;
    flex-direction: row;
    justify-content: flex-start;
    padding: 4px;
  }

  .thread-info-row {
    background-color: #e6e8eb;
    height: 79px !important;
    flex-direction: row;
    justify-content: flex-start;
    padding: 16px;
  }
}

.align-left {
  text-align: left;
}

.close-icon {
  width: 20px;
  height: 20px;
  color: #0064FE;
  position: absolute;
  right: 16px;
  top: 16px;
  cursor: pointer;
}
