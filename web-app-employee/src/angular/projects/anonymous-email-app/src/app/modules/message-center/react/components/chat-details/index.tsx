import {
  Chat,
  Message, MessageHistory, MessageHistoryHeader,
} from '@metromarkets/message-center-sdk';

import { JSX, useEffect, useRef, useState } from 'react';
import { getMessageHistory } from '@anonymous-email-app/modules/message-center/react/services/message-center.service';

interface Props {
  selectedChat?: Chat;
}

export const ChatDetails = ({ selectedChat }: Props): JSX.Element => {
  const [messages, setMessages] = useState<Message[]>([])

  useEffect(() => {
    if (selectedChat) {
      fetchMessageHistory();
    }
  }, [selectedChat?.id]);

  const fetchMessageHistory = async () => {
    try {
      if (selectedChat) {
        const { data } = await getMessageHistory(selectedChat)
        setMessages(data);
      }
    } catch (e) {
      console.error('something went wrong', e)
    }
  }


  return (
    <div className='details'>
      <div className='details__chat-history'>
        {selectedChat && <MessageHistory messages={messages} chat={selectedChat}/>}
      </div>
    </div>
  );
};
