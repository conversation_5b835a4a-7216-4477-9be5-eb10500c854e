import 'jest';
import { AnonymousEmailAttachmentService } from './anonymous-email-attachment.service';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed, inject } from '@angular/core/testing';
import { ANONYMOUS_EMAILS_ATTACHMENTS_API, ANONYMOUS_EMAILS_ATTACHMENT_URL_API } from '../constants/api';

describe('Anonymous Email Attachment Service', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [AnonymousEmailAttachmentService],
      imports: [HttpClientTestingModule],
    });
  });
  it('Load Anonymous Email Attachments', inject(
    [HttpTestingController, AnonymousEmailAttachmentService],
    (httpMock: HttpTestingController, service: AnonymousEmailAttachmentService) => {
      service.loadAnonymousEmailAttachments({}).subscribe(data => {
        expect(data.length).toBe(1);
        expect(data[0].emailId).toBe('123');
      });
      const req = httpMock.expectOne(ANONYMOUS_EMAILS_ATTACHMENTS_API);
      expect(req.request.method).toEqual('GET');
      req.flush([
        {
          emailId: '123',
          attachments: [],
        },
      ]);
    },
  ));

  it('Get Anonymous Email AttachmentUrl', inject(
    [HttpTestingController, AnonymousEmailAttachmentService],
    (httpMock: HttpTestingController, service: AnonymousEmailAttachmentService) => {
      service.getAnonymousEmailAttachmentUrl({}).subscribe(data => {
        expect(data.url).toBe('test-url');
      });
      const req = httpMock.expectOne(ANONYMOUS_EMAILS_ATTACHMENT_URL_API);
      expect(req.request.method).toEqual('GET');
      req.flush({ url: 'test-url' });
    },
  ));
});
