import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { AppVariant, Settings } from '@metromarkets/message-center-sdk';
import { ANONYMOUS_EMAIL_ROUTE, LANGUAGE_PICKER, MESSAGE_CENTER_ROUTE } from '~shared/constants';
import { JSX } from 'react';
import {
  MessageCenterContainer
} from '@anonymous-email-app/modules/message-center/react/components/message-center-container';

const MessageCenterRoutes = (): JSX.Element => {
  const defaultLanguage = 'en';
  const language: string = JSON.parse(localStorage.getItem(LANGUAGE_PICKER)) || defaultLanguage;

  const settings: Partial<Settings> = {
    appVariant: AppVariant.Employee,
    language,
    maxFileSize: '10MB',
    maxFiles: 10
  };
  const path = `:lang?/${ANONYMOUS_EMAIL_ROUTE}/${MESSAGE_CENTER_ROUTE}`;

  return (
    <BrowserRouter>
      <Routes>
        <Route path={`${path}/`} element={<MessageCenterContainer settings={settings}/>}/>
        <Route path={`${path}/:chatId`} element={<MessageCenterContainer settings={settings}/>}/>
      </Routes>
    </BrowserRouter>
  );
};

export default MessageCenterRoutes;
