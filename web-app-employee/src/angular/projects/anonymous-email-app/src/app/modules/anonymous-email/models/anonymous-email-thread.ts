import { Field, Model, ArraySerializer, Type } from 'serialize-ts/dist';
import { AnonymousEmailContent } from './anonymous-email-content';
import { ModelMetadataSerializer } from 'serialize-ts/dist/serializers/model-metadata.serializer';
import { Seller } from './seller';

@Model()
export class AnonymousEmailThread {
  @Field()
  threadNumber: string;

  @Field()
  seller: Seller;

  @Field()
  initiatedByBuyer: boolean;

  @Field()
  firstResponseHours: number;

  @Field()
  @Type(new ArraySerializer(new ModelMetadataSerializer(AnonymousEmailContent)))
  emails: AnonymousEmailContent[];
}
