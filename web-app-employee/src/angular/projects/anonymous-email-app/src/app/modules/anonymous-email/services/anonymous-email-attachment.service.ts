import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { queryBuilder } from '@metromarkets/sdk-17';
import { ANONYMOUS_EMAILS_ATTACHMENT_URL_API, ANONYMOUS_EMAILS_ATTACHMENTS_API } from '../constants/api';
import { AnonymousEmailWithAttachments } from '@anonymous-email-app/modules/anonymous-email/models/anonymous-email-with-attachments';
import { AnonymousEmailAttachmentUrl } from '@anonymous-email-app/modules/anonymous-email/models/anonymous-email-attachment-url';

@Injectable({
  providedIn: 'root',
})
export class AnonymousEmailAttachmentService {
  constructor(private httpClient: HttpClient) {}

  loadAnonymousEmailAttachments(requestParams): Observable<Array<AnonymousEmailWithAttachments>> {
    const params = queryBuilder.toParams(requestParams) as HttpParams;
    return this.httpClient.get<AnonymousEmailWithAttachments[]>(ANONYMOUS_EMAILS_ATTACHMENTS_API, { params }).pipe();
  }

  getAnonymousEmailAttachmentUrl(requestParams): Observable<AnonymousEmailAttachmentUrl> {
    const params = queryBuilder.toParams(requestParams) as HttpParams;
    return this.httpClient.get<AnonymousEmailAttachmentUrl>(ANONYMOUS_EMAILS_ATTACHMENT_URL_API, { params }).pipe();
  }
}
