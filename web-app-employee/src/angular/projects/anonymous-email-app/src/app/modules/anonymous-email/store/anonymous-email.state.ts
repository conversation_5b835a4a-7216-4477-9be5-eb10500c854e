import { PaginatedResponse } from '~shared/model';
import { ErrorResponse } from '~shared/model/error-response.model';
import { AnonymousEmail } from '../models/anonymous-email';
import { AnonymousEmailWithAttachments } from '@anonymous-email-app/modules/anonymous-email/models/anonymous-email-with-attachments';

export interface AnonymousEmailState {
  payload: PaginatedResponse<AnonymousEmail>;
  loadingAttachments: boolean;
  threadAttachments: Array<AnonymousEmailWithAttachments>;
  loading: boolean;
  shouldLoadMore: boolean;
  error: ErrorResponse;
}

export const anonymousEmailsInitialState: AnonymousEmailState = {
  payload: {
    items: [],
    totalCount: 0,
  },
  loadingAttachments: false,
  threadAttachments: null,
  shouldLoadMore: true,
  loading: false,
  error: null,
};
