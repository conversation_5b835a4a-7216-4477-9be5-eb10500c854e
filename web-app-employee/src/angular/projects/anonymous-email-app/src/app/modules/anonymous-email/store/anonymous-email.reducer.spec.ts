import 'jest';
import * as AEStore from './index';
import { EMAILS_MOCK } from '../mocks/emails.mock';
import { ErrorResponse } from '~shared/model/error-response.model';
import { PaginatedResponse } from '@root/shared/model';
import { AnonymousEmail } from '../models/anonymous-email';
import { AnonymousEmailWithAttachments } from '../models/anonymous-email-with-attachments';
import { anonymousEmailsInitialState } from './anonymous-email.state';
describe('Anonymous Email Reducer Tests', () => {
  const initialState = anonymousEmailsInitialState;
  describe('Load Anonymous Email Actions', () => {
    it('Load Anonymous Email', () => {
      const action = new AEStore.LoadAnonymousEmail({
        params: {},
        shouldConcat: false,
      });
      const newState = AEStore.anonymousEmailReducer(initialState, action);
      expect(newState.loading).toBe(true);
    });
    it('Load Anonymous Email Success', () => {
      const action = new AEStore.LoadAnonymousEmailSuccess({
        emails: { items: EMAILS_MOCK, totalCount: 90 } as PaginatedResponse<AnonymousEmail>,
        shouldConcat: false,
      });
      const newState = AEStore.anonymousEmailReducer(initialState, action);
      expect(newState.loading).toBe(false);
      expect(newState.payload.totalCount).toBe(EMAILS_MOCK.length);
      expect(newState.error).toBe(null);
    });
    it('Load Anonymous Email Failure', () => {
      const action = new AEStore.LoadAnonymousEmailFailure({
        error: { status: 404 } as ErrorResponse,
      });
      const errorCode = 404;
      const newState = AEStore.anonymousEmailReducer(initialState, action);
      expect(newState.loading).toBe(false);
      expect(newState.payload.totalCount).toBe(0);
      expect(newState.error.status).toBe(errorCode);
    });
  });
  describe('Load Anonymous Email Attachments Actions', () => {
    it('Load Anonymous Email Attachments', () => {
      const action = new AEStore.LoadAnonymousEmailAttachments({
        email: {
          orderNumber: 'O20-1234567',
          threadNumber: '12345',
        },
      });
      const newState = AEStore.anonymousEmailReducer(initialState, action);
      expect(newState.loadingAttachments).toBe(true);
    });
    it('Load Anonymous Email Attachments Success', () => {
      const action = new AEStore.LoadAnonymousEmailAttachmentSuccess({
        attachments: [new AnonymousEmailWithAttachments()],
      });
      const newState = AEStore.anonymousEmailReducer(initialState, action);
      expect(newState.loadingAttachments).toBe(false);
      expect(newState.threadAttachments.length).toBe(1);
      expect(newState.error).toBe(null);
    });
    it('Load Anonymous Email Attachments Failure', () => {
      const action = new AEStore.LoadAnonymousEmailAttachmentFailure({
        error: { status: 404 } as ErrorResponse,
      });
      const newState = AEStore.anonymousEmailReducer(initialState, action);
      expect(newState.loadingAttachments).toBe(false);
      expect(newState.payload.totalCount).toBe(0);
    });
  });
  describe('Downloading Anonymous Email Attachment', () => {
    it('Downloading Anonymous Email Attachment', () => {
      const action = new AEStore.DownloadingAnonymousEmailAttachment({
        name: 'filename',
      });
      const newState = AEStore.anonymousEmailReducer(initialState, action);
      expect(newState.loading).toBe(true);
    });
    it('Downloading Anonymous Email Attachment Success', () => {
      const action = new AEStore.DownloadingAnonymousEmailAttachmentSuccess({
        url: 'fileurl',
      });
      const newState = AEStore.anonymousEmailReducer(initialState, action);
      expect(newState.loading).toBe(false);
      expect(newState.error).toBe(null);
    });
    it('Downloading Anonymous Email Attachment Failure', () => {
      const action = new AEStore.DownloadingAnonymousEmailAttachmentFailure({
        error: { status: 404 } as ErrorResponse,
      });
      const newState = AEStore.anonymousEmailReducer(initialState, action);
      expect(newState.loading).toBe(false);
    });
  });
});
