import { Field, Model, Type, ArraySerializer } from 'serialize-ts/dist';
import { AnonymousEmailAttachment } from '@anonymous-email-app/modules/anonymous-email/models/anonymous-email-attachment';
import { ModelMetadataSerializer } from 'serialize-ts/dist/serializers/model-metadata.serializer';

@Model()
export class AnonymousEmailWithAttachments {
  @Field()
  emailId: string;

  @Field()
  @Type(new ArraySerializer(new ModelMetadataSerializer(AnonymousEmailAttachment)))
  attachments: Array<AnonymousEmailAttachment>;
}
