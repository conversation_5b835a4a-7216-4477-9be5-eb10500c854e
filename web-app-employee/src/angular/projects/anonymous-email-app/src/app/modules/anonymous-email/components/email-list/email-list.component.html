<mat-progress-bar *ngIf="isEmailsLoading" mode="indeterminate"></mat-progress-bar>

<div class="emails">
  <h2>Anonymous Emails</h2>

  <app-emails-search (resetEmitter)="reset()" [searchQuery]="searchQuery"></app-emails-search>
  <div *ngIf="emails.length === 0 && isEmailsLoading === false" class="emails__no-email-wrapper">
    <span class="emails___no-email-text">No emails were found.</span>
  </div>
  <div *ngIf="emails.length > 0" class="emails__table-wrapper">
    <div
      (scroll)="onScroll($event)"
      [ngClass]="{
        'emails__main-table--static': currentThread,
        'emails__main-table--flex': !currentThread
      }"
      class="emails__main-table"
    >
      <table (scroll)="onScroll($event)" [dataSource]="emails" class="table emails__emails-table" mm-table>
        <ng-container mmColumnDef="orderNumber">
          <th *mmHeaderCellDef class="main-table__order-number-border" mmHeaderCell>Order Number</th>
          <td
            (click)="openThread(element)"
            *mmCellDef="let element"
            [mmTextAlign]="center"
            [ngClass]="{
              highlight: isSelected(element)
            }"
            class="clickable main-table__order-number-border"
            mmCell
          >
            <app-order-number [email]="element"></app-order-number>
          </td>
        </ng-container>
        <ng-container mmColumnDef="email">
          <th *mmHeaderCellDef class="main-table__thread-cell" mmHeaderCell>Email</th>
          <td
            (click)="openThread(element)"
            *mmCellDef="let element"
            [ngClass]="{
              'main-table_border': currentThread,
              highlight: isSelected(element)
            }"
            class="main-table__thread-cell clickable"
            mmCell
          >
            <div class="main-table__thread-cell-wrapper">
              <app-thread [thread]="getLatestContent(element.emails)"></app-thread>
            </div>
          </td>
        </ng-container>
        <ng-container *ngFor="let col of secondaryColumns; let colIndex = index" mmColumnDef="{{ col }}">
          <th *mmHeaderCellDef class="main-table__secondary-column-headers" mmHeaderCell>
            <ng-container *ngIf="['sellerName', 'buyerName' ].indexOf(col)>-1; else normalCol">
              <ng-container *ngIf="col==='sellerName'">
                <div class="align-left">
                  Seller Name
                </div>
                <div class="align-left">
                  Seller ID
                </div>
              </ng-container>
              <ng-container *ngIf="col==='buyerName'">
                <div class="align-left">
                  Buyer Name
                </div>
                <div class="align-left">
                  Buyer Type
                </div>
              </ng-container>
            </ng-container>
            <ng-template #normalCol>
              {{ secondaryColumnTitles[col] }}
            </ng-template>
          </th>
          <td *mmCellDef="let element" mmCell>

              <ng-container *ngIf="['sellerName', 'buyerName' ].indexOf(col)>-1; else normalCol">
                <ng-container *ngIf="col==='sellerName'">
                  <div class="align-left">
                    <app-email-data-format [text]="getData(element, col)" [column]="col"></app-email-data-format>
                  </div>
                  <div class="align-left">
                    <app-email-data-format [text]="getData(element, 'sellerId')" column="sellerId"></app-email-data-format>
                  </div>
              </ng-container>
                <ng-container *ngIf="col === 'buyerName'">
                  <div class="align-left">
                    <app-email-data-format [text]="getData(element, col)" [column]="col"></app-email-data-format>
                  </div>
                  <div class="align-left">
                    <app-email-data-format [text]="getData(element, 'accountType')" column="accountType"></app-email-data-format>
                  </div>
                </ng-container>
              </ng-container>
            <ng-template #normalCol>
              <app-email-data-format [text]="getData(element, col)" [column]="col"></app-email-data-format>
            </ng-template>
          </td>
        </ng-container>

        <tr *mmHeaderRowDef="getColumns(); sticky: true" mm-header-row></tr>
        <tr *mmRowDef="let row; columns: getColumns()" [class.late-response]="checkLateResponse(row)" mm-row></tr>
      </table>
    </div>
    <div *ngIf="currentThread !== null" class="emails__secondary-table">
      <mm-table-loader *ngIf="dataLoading"></mm-table-loader>
      <mm-icon name="close" class="close-icon" (click)="closeThread()"></mm-icon>
      <div>
        <app-email-content
          [email]="currentThread"
          [emailsWithAttachments]="emailsWithAttachments$ | async"
          [loadingAttachments]="loadingAttachments$ | async"
        ></app-email-content>
      </div>
    </div>
  </div>
</div>
