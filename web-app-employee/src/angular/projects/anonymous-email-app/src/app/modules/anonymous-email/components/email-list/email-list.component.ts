import { Component, OnInit } from '@angular/core';
import {
  MAIN_COLUMNS,
  SECONDARY_COLUMNS,
  MAIN_COLUMN_TITLES,
  SECONDARY_COLUMN_TITLES,
  SECONDARY_COLUMN_MAX_LENGTH,
} from '../../constants/table-columns';
import { MAX_TABLE_TEXT_LENGTH, SCROLL_PERCENTAGE, LATE_RESPONSE_HOURS } from '../../constants/default-constants';
import { AnonymousEmail } from '../../models/anonymous-email';
import { AnonymousEmailThread } from '../../models/anonymous-email-thread';
import * as fromAnonymousEmails from '../../store';
import { select, Store } from '@ngrx/store';
import { AnonymousEmailContent } from '../../models/anonymous-email-content';
import { PAGE_SIZE } from '../../constants/api';
import { Observable } from 'rxjs';
import { AnonymousEmailWithAttachments } from '@anonymous-email-app/modules/anonymous-email/models/anonymous-email-with-attachments';
import { Params } from '@angular/router';
import { DatePipe } from '@angular/common';
@Component({
  selector: 'app-email-list',
  templateUrl: './email-list.component.html',
  styleUrls: ['./email-list.component.scss'],
  providers: [DatePipe]
})
export class EmailListComponent implements OnInit {
  emails: any = [];
  isEmailsLoading: boolean;
  currentThread: AnonymousEmailThread = null;
  currentEmail: AnonymousEmail = null;
  currentPage = 0;
  loadingAttachments$: Observable<boolean>;
  emailsWithAttachments$: Observable<Array<AnonymousEmailWithAttachments>>;
  searchQuery: Params;
  shouldLoadMore: boolean;

  mainColumns = MAIN_COLUMNS;
  mainColumnTitles = MAIN_COLUMN_TITLES;
  secondaryColumns = SECONDARY_COLUMNS;
  secondaryColumnTitles = SECONDARY_COLUMN_TITLES;
  secondaryColumnMaxLengths = SECONDARY_COLUMN_MAX_LENGTH;
  MAX_THREAD_TEXT_LENGTH = MAX_TABLE_TEXT_LENGTH;
  lateResponseHours = LATE_RESPONSE_HOURS;

  constructor(private store: Store<fromAnonymousEmails.AnonymousEmailState>,
              private datePipe: DatePipe) {
    this.isEmailsLoading = false;
    this.shouldLoadMore = true;
    this.setEmailSubscriptions();

    this.store.pipe(select(fromAnonymousEmails.getFilterQuery)).subscribe(val => {
      this.searchQuery = val;
    });
  }
  reset() {
    this.currentPage = 0;
    this.currentThread = null;
    this.currentEmail = null;
    this.store.dispatch(new fromAnonymousEmails.ResetAnonymousEmail());
  }
  ngOnInit(): void {
    this.loadingAttachments$ = this.store.pipe(select(fromAnonymousEmails.loadingAttachments));
    this.emailsWithAttachments$ = this.store.pipe(select(fromAnonymousEmails.getThreadAttachments));
  }
  shorten(str: string, maxValue = this.MAX_THREAD_TEXT_LENGTH): string {
    return `${str.substr(0, maxValue)}..`;
  }
  isSelected(thread): boolean {
    if (
      this.currentThread &&
      thread.threadNumber === this.currentThread.threadNumber &&
      this.currentEmail.orderNumber === thread.orderNumber
    ) {
      return true;
    }
    return false;
  }
  setEmailSubscriptions(): void {
    this.store.pipe(select(fromAnonymousEmails.getAnonymousEmails)).subscribe(data => {
      this.emails = this.getSpreadedEmails(data);
    });
    this.store.pipe(select(fromAnonymousEmails.isAnonymousEmailLoading)).subscribe(isLoading => {
      this.isEmailsLoading = isLoading;
    });
    this.store.pipe(select(fromAnonymousEmails.getShouldLoad)).subscribe(shouldLoadMore => {
      this.shouldLoadMore = shouldLoadMore;
    });
  }
  loadMoreEmails() {
    if (this.shouldLoadMore) {
      this.store.dispatch(
        new fromAnonymousEmails.LoadAnonymousEmail({
          params: {
            offset: PAGE_SIZE * ++this.currentPage,
            limit: PAGE_SIZE,
            ...this.searchQuery,
          },
          shouldConcat: true,
        }),
      );
    }
  }
  getLatestContent(threads: AnonymousEmailContent[]): AnonymousEmailContent {
    return threads[0];
  }
  onScroll(event) {
    if (
      (event.target.offsetHeight + event.target.scrollTop) / event.target.scrollHeight > SCROLL_PERCENTAGE &&
      this.isEmailsLoading === false
    ) {
      this.loadMoreEmails();
    }
  }
  getData(email: any, key: string): string {
    switch (key) {
      case 'orderlines':
        return email.orderLinesNumber;
      case 'accountType':
        return email.buyer.type;
      case 'orderDate':
        return this.datePipe.transform(new Date(email.createdAt).toDateString(), 'dd.MM.yyyy');
      case 'sellerName':
        return email.seller.organisationName;
      case 'sellerId':
        return email.seller.id;
      case 'buyerName':
        return `${email.buyer.firstName} ${email.buyer.lastName}`;
      case 'initiatedBy':
        return email.initiatedByBuyer ? 'Buyer' : 'Seller';
      case 'firstResponse':
        return `${email.firstResponseHours}h`;
      default:
        return 'N/A';
    }
  }
  getColumns(): string[] {
    if (this.currentThread !== null) {
      return this.mainColumns;
    } else {
      return [...this.mainColumns, ...this.secondaryColumns];
    }
  }
  openThread(email): void {
    this.currentThread = email;
    this.currentEmail = email;
    this.loadAttachments(email);
  }
  loadAttachments(email) {
    this.store.dispatch(new fromAnonymousEmails.LoadAnonymousEmailAttachments({ email }));
  }
  getSpreadedEmails(emails: AnonymousEmail[]): Object[] {
    const spreadedEmails = [];
    emails.forEach(email => {
      email.threads.forEach(thread => {
        const spreadedEmail = {
          ...email,
          ...thread,
          threads: undefined,
        };
        spreadedEmails.push(spreadedEmail);
      });
    });
    return spreadedEmails;
  }
  checkLateResponse(email: any): boolean {
    return email.initiatedByBuyer && email.firstResponseHours > this.lateResponseHours;
  }

  closeThread(): void {
    this.currentEmail = null;
    this.currentThread = null;
  }
}
