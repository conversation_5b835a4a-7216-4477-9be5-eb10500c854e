<div *ngIf="email" class="email-content-wrapper">
  <div class="heading" fxLayout="row" fxLayoutAlign="start center">
    <span class="order-number">Order # </span>
    <span class="order-heading">{{ email.orderNumber }}</span>
    <div [ngStyle]="{ 'background-color': getStatusData().color }" class="order-status-chip">
      <span [ngStyle]="{ color: getStatusData().textColor ? getStatusData().textColor : '' }">
        {{ getStatusData().title }}
      </span>
    </div>
  </div>
  <div fxLayout="row" fxLayoutAlign="start stretch" class="email-data">
    <div fxLayout="row" fxLayoutAlign="start stretch" fxFlex="50">
      <label fxFlex="50">Initiated By</label> <span fxFlex="50">{{email.initiatedByBuyer ? 'Buyer' : 'Seller'}}</span>
    </div>
    <div fxLayout="row" fxLayoutAlign="start stretch" fxFlex="50">
      <label fxFlex="50">Buyer Name</label> <span fxFlex="50">{{email.buyer.firstName}} {{email.buyer.lastName}}</span>
    </div>
  </div>
  <div fxLayout="row" fxLayoutAlign="start stretch"  class="email-data">
    <div fxLayout="row" fxLayoutAlign="start stretch"  fxFlex="50">
      <label fxFlex="50">First Response</label> <span fxFlex="50">{{email.firstResponseHours}}h</span>
    </div>
    <div fxLayout="row" fxLayoutAlign="start stretch" class="email-data"  fxFlex="50">
      <label fxFlex="50">Buyer Type</label> <span fxFlex="50">{{ email.buyer.type}}</span>
    </div>
  </div>
  <div fxLayout="row" fxLayoutAlign="start stretch" class="email-data">
    <div fxLayout="row" fxLayoutAlign="start stretch"  fxFlex="50">
      <label fxFlex="50">Order Date</label> <span fxFlex="50">{{email.createdAt | date : 'dd/MM/yyyy' }}</span>
    </div>
    <div fxLayout="row" fxLayoutAlign="start stretch"  fxFlex="50">
      <label fxFlex="50">Seller Name</label> <span fxFlex="50">{{email.seller.organisationName}}</span>
    </div>
  </div>
  <div fxLayout="row" fxLayoutAlign="start stretch" class="email-data" >
    <div fxLayout="row" fxLayoutAlign="start stretch"  fxFlex="50">
      <label fxFlex="50">Order Lines</label> <span fxFlex="50">{{email.orderLinesNumber}}</span>
    </div>
    <div fxLayout="row" fxLayoutAlign="start stretch" fxFlex="50" >

      <label fxFlex="50">Seller ID</label>

      <span class="seller-id"
            fxFlex="50"
            [mmTooltipHideDelay]="400"
            [mmTooltipShowDelay]="100"
            [mmTooltip]="email.seller.id"
            mmTooltipColor="blue"
            mmTooltipPlacement="bottom">{{email.seller.id}}</span>
    </div>
  </div>
  <hr class="divider" />
  <ng-template ngFor let-item [ngForOf]="email.emails">
    <div class="email-content-wrapper__content">
      <div class="content__email-headers">
        <div>
          <span class="email-headers__title">From: </span>
          <span class="email-headers__text">{{ item.fromEmail }}</span>
        </div>
        <div>
          <span class="email-headers__title">To: </span>
          <span class="email-headers__text">{{ item.recipientEmail }}</span>
        </div>
        <div>
          <span class="email-headers__title">Date: </span>
          <span class="email-headers__text">{{ item.emailDate | date: 'dd.MM.yyyy HH:mm:ss' }}</span>
        </div>
        <div>
          <mat-progress-bar *ngIf="loadingAttachments" mode="indeterminate"></mat-progress-bar>
          <div *ngIf="emailHaveAttachments(item)">
            <span class="email-attachments__title">Attachments:</span>
            <ng-template ngFor let-emailWithAttachments [ngForOf]="emailsWithAttachments">
              <span *ngIf="emailWithAttachments.emailId === item.id" class="email-attachments__name">
                <ng-template ngFor let-attachment [ngForOf]="emailWithAttachments.attachments">
                  <app-email-attachment [attachment]="attachment"></app-email-attachment>
                </ng-template>
              </span>
            </ng-template>
          </div>
        </div>
      </div>
      <div class="content__email-body" [innerHTML]="item.body"></div>
    </div>
  </ng-template>
</div>
