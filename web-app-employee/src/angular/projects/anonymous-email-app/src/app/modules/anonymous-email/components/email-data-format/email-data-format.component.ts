import {Component, Input} from '@angular/core';
import {SECONDARY_COLUMN_MAX_LENGTH} from "@anonymous-email-app/modules/anonymous-email/constants/table-columns";
import {MAX_TABLE_TEXT_LENGTH} from "@anonymous-email-app/modules/anonymous-email/constants/default-constants";

@Component({
  selector: 'app-email-data-format',
  templateUrl: './email-data-format.component.html',
  styleUrl: './email-data-format.component.scss'
})
export class EmailDataFormatComponent {

  @Input()
  text = '';
  @Input()
  column = '';

  secondaryColumnMaxLengths = SECONDARY_COLUMN_MAX_LENGTH;
  maxThreadLength = MAX_TABLE_TEXT_LENGTH;

  shorten(str: string, maxValue = this.maxThreadLength): string {
    return `${str.substr(0, maxValue)}..`;
  }
}
