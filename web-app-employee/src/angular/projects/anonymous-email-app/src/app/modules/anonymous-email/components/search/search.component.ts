import { Component, OnInit, ChangeDetectionStrategy, Input, Output, EventEmitter } from '@angular/core';
import { QueryParamsHandler } from '~shared/services/query-params-handler.service';
import { DropdownInput } from '~shared/model/dropdown-input.model';

@Component({
  selector: 'app-emails-search',
  templateUrl: './search.component.html',
  styleUrls: ['./search.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [QueryParamsHandler],
})
export class SearchComponent implements OnInit {
  @Input()
  reset: Function;
  @Input()
  searchQuery: string;
  @Output()
  resetEmitter = new EventEmitter<void>();
  selectedField: DropdownInput;
  options = [new DropdownInput('orderNumber', 'Order Number'), new DropdownInput('email', 'Email')];
  searchText: string;
  constructor(private paramsHandler: QueryParamsHandler) {}

  ngOnInit() {
    this.selectedField = this.options[0];
    this.fillFromQuery();
  }
  fillFromQuery() {
    const filters = Object.keys(this.searchQuery);
    if (filters.length > 0) {
      for (const filter of filters) {
        const field = this.findField(filter);
        if (field !== null) {
          this.selectedField = field;
          this.searchText = this.searchQuery[filter];
          break;
        }
      }
    }
  }
  findField(key: string): DropdownInput {
    for (const option of this.options) {
      if (option.id === key) {
        return option;
      }
    }
    return null;
  }
  onSelectField(value: DropdownInput): void {
    this.selectedField = value;
    this.search();
  }
  onTextChange(text: string): void {
    this.searchText = text;
    this.search();
  }
  search(): void {
    this.resetEmitter.emit();
    this.paramsHandler.changeQueryParams(
      {
        [this.selectedField.id]: this.searchText,
      },
      '',
    );
  }
  onClear(): void {
    this.paramsHandler.changeQueryParams({}, '');
  }
}
