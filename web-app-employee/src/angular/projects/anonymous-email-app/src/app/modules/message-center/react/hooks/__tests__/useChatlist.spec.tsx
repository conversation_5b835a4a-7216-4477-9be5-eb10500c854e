import { renderHook, act } from '@testing-library/react-hooks';
import { useChatList } from '../useChatlist';
import { getChatlist } from '@anonymous-email-app/modules/message-center/react/services/message-center.service';

// Mock the dependencies
jest.mock('@anonymous-email-app/modules/message-center/react/services/message-center.service', () => ({
  getChatlist: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
  useParams: () => ({ chatId: undefined }),
  useNavigate: () => jest.fn(),
}));

describe('useChatList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with empty chats and default values', () => {
    // Act
    const { result } = renderHook(() => useChatList());

    // Assert
    expect(result.current.filteredChats).toEqual([]);
    expect(result.current.loading).toBe(false);
    expect(result.current.hasMore).toBe(true);
  });

  it('should fetch chats on initial load', async () => {
    // Arrange
    const mockChats = [{ id: '1', order: { orderNumber: '12345' } }];
    (getChatlist as jest.Mock).mockResolvedValue({ data: mockChats });

    // Act
    const { result, waitForNextUpdate } = renderHook(() => useChatList());
    act(() => {
      result.current.fetchChats();
    });

    // Wait for the async operation to complete
    await waitForNextUpdate();

    // Assert
    expect(getChatlist).toHaveBeenCalledWith({ offset: 0, limit: 20 });
    expect(result.current.filteredChats).toEqual(mockChats);
    expect(result.current.loading).toBe(false);
  });

  it('should load more chats when loadMore is called', async () => {
    // Arrange
    const initialChats = [{ id: '1', order: { orderNumber: '12345' } }];
    const moreChats = [{ id: '2', order: { orderNumber: '67890' } }];

    (getChatlist as jest.Mock).mockResolvedValueOnce({ data: initialChats }).mockResolvedValueOnce({ data: moreChats });

    // Act
    const { result, waitForNextUpdate } = renderHook(() => useChatList());

    // Initial load
    act(() => {
      result.current.fetchChats();
    });
    await waitForNextUpdate();

    // Load more
    act(() => {
      result.current.loadMore();
    });
    await waitForNextUpdate();

    // Assert
    expect(getChatlist).toHaveBeenCalledTimes(2);
    expect(getChatlist).toHaveBeenNthCalledWith(1, { offset: 0, limit: 20 });
    expect(getChatlist).toHaveBeenNthCalledWith(2, { offset: 1, limit: 20 });
    expect(result.current.filteredChats).toEqual([...initialChats, ...moreChats]);
  });

  it('should search by orderNumber and skip pagination', async () => {
    // Arrange
    const mockChats = [{ id: '1', order: { orderNumber: '12345' } }];
    (getChatlist as jest.Mock).mockResolvedValue({ data: mockChats });

    // Act
    const { result, waitForNextUpdate } = renderHook(() => useChatList());
    act(() => {
      result.current.onSearch('12345');
    });

    // Wait for the async operation to complete
    await waitForNextUpdate();

    // Assert
    expect(getChatlist).toHaveBeenCalledWith({ orderNumber: '12345' });
    expect(result.current.filteredChats).toEqual(mockChats);
    expect(result.current.hasMore).toBe(false); // hasMore should be false when searching
  });

  it('should reset pagination when search is cleared', async () => {
    // Arrange
    const mockChats = [{ id: '1', order: { orderNumber: '12345' } }];
    (getChatlist as jest.Mock).mockResolvedValue({ data: mockChats });

    // Act
    const { result, waitForNextUpdate } = renderHook(() => useChatList());

    // First search by orderNumber
    act(() => {
      result.current.onSearch('12345');
    });
    await waitForNextUpdate();

    // Then clear search
    act(() => {
      result.current.onSearch('');
    });
    await waitForNextUpdate();

    // Assert
    expect(getChatlist).toHaveBeenNthCalledWith(1, { orderNumber: '12345' });
    expect(getChatlist).toHaveBeenNthCalledWith(2, { offset: 0, limit: 20 });
    expect(result.current.hasMore).toBe(true); // hasMore should be reset to true when search is cleared
  });
});
