import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, switchMap, filter } from 'rxjs/operators';
import { AnonymousEmailService } from '../services/anonymous-email.service';
import {
  LoadAnonymousEmail,
  LoadAnonymousEmailFailure,
  LoadAnonymousEmailSuccess,
  AnonymousEmailActionTypes,
  LoadAnonymousEmailAttachments,
  LoadAnonymousEmailAttachmentSuccess,
  LoadAnonymousEmailAttachmentFailure,
  DownloadingAnonymousEmailAttachment,
  DownloadingAnonymousEmailAttachmentSuccess,
} from './anonymous-email.actions';
import { AnonymousEmailAttachmentService } from '@anonymous-email-app/modules/anonymous-email/services/anonymous-email-attachment.service';
import { saveAs } from 'file-saver';
import { ROUTER_NAVIGATED, RouterNavigatedAction } from '@ngrx/router-store';
import { RouterStateUrl } from '~core/store';
import { isEmpty, get } from 'lodash';
import { isUrlMatchRoute } from '~shared/utils/common';
import { ANONYMOUS_EMAIL_ROUTE, EMAILS_ROUTE } from '~shared/constants';
import { PAGE_SIZE, DEFAULT_OFFSET } from '../constants/api';

@Injectable()
export class AnonymousEmailEffects {
  loadAnonymousEmail$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AnonymousEmailActionTypes.LOAD_ANONYMOUS_EMAIL),
      map((action: LoadAnonymousEmail) => action.payload),
      switchMap(({ params, shouldConcat }) =>
        this.anonymousEmailService.loadAnonymousEmails(params).pipe(
          map(emails => new LoadAnonymousEmailSuccess({ emails, shouldConcat })),
          catchError(error => of(new LoadAnonymousEmailFailure({ error }))),
        ),
      ),
    ),
  );

  loadAnonymousEmailByRouter$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ROUTER_NAVIGATED),
      map((action: RouterNavigatedAction) => get(action, 'payload.routerState', {})),
      map(({ url, queryParams }: RouterStateUrl) => ({ url, queryParams })),
      filter(({ url }) => isUrlMatchRoute(url, `${ANONYMOUS_EMAIL_ROUTE}/${EMAILS_ROUTE}`)),
      map(({ queryParams }) => (!isEmpty(queryParams) ? queryParams : { offset: DEFAULT_OFFSET, limit: PAGE_SIZE })),
      map(params => new LoadAnonymousEmail({ params, shouldConcat: false })),
    ),
  );

  loadAnonymousEmailAttachments$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AnonymousEmailActionTypes.LOAD_ANONYMOUS_EMAIL_ATTACHMENTS),
      map((action: LoadAnonymousEmailAttachments) => action.payload),
      switchMap(({ email }) =>
        this.anonymousEmailAttachmentService
          .loadAnonymousEmailAttachments({
            orderNumber: email.orderNumber,
            threadNumber: email.threadNumber,
          })
          .pipe(
            map(attachments => new LoadAnonymousEmailAttachmentSuccess({ attachments })),
            catchError(error => of(new LoadAnonymousEmailAttachmentFailure({ error }))),
          ),
      ),
    ),
  );

  downloadAnonymousEmailAttachment$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AnonymousEmailActionTypes.DOWNLOAD_ANONYMOUS_EMAIL_ATTACHMENT),
      map((action: DownloadingAnonymousEmailAttachment) => action.payload),
      switchMap(({ name }) =>
        this.anonymousEmailAttachmentService.getAnonymousEmailAttachmentUrl({ name }).pipe(
          map(anonymousEmailAttachmentUrl => {
            saveAs(anonymousEmailAttachmentUrl.url, name);
            return new DownloadingAnonymousEmailAttachmentSuccess({ ...anonymousEmailAttachmentUrl });
          }),
          catchError(error => of(new LoadAnonymousEmailAttachmentFailure({ error }))),
        ),
      ),
    ),
  );

  constructor(
    private actions$: Actions,
    private anonymousEmailService: AnonymousEmailService,
    private anonymousEmailAttachmentService: AnonymousEmailAttachmentService,
  ) {}
}
