<div *ngIf="thread" class="thread">
  <div class="first-column">
    <div>
      <span
        *ngIf="thread.fromEmail.length >= MAX_THREAD_TEXT_LENGTH; else wholeEmail"
        [mmTooltip]="thread.fromEmail"
        [mmTooltipHideDelay]="400"
        [mmTooltipShowDelay]="1000"
        mmTooltipColor="blue"
        mmTooltipPlacement="bottom"
        >{{ shorten(thread.fromEmail) }}</span
      >
      <ng-template #wholeEmail>
        <span>{{ thread.fromEmail }}</span>
      </ng-template>
    </div>
    <div class="email-title">
      <span
        *ngIf="thread.subject.length >= MAX_THREAD_TEXT_LENGTH; else wholeSubject"
        [mmTooltip]="thread.subject"
        [mmTooltipHideDelay]="400"
        [mmTooltipShowDelay]="1000"
        mmTooltipColor="blue"
        mmTooltipPlacement="bottom"
        >{{ shorten(thread.subject) }}</span
      >
      <ng-template #wholeSubject>
        <span>{{ thread.subject }}</span>
      </ng-template>
    </div>
    <div fxLayout="row" fxLayoutAlign="start center" >
      <label class="last-response">Last reply:&nbsp;</label>
      <span>{{ thread.emailDate | date: 'dd.MM.yyyy' }}</span>
    </div>
  </div>
</div>
