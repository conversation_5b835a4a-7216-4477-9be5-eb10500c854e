import { ArraySerializer, Field, Model, Type } from 'serialize-ts/dist';
import { AnonymousEmailAttachment } from '@anonymous-email-app/modules/anonymous-email/models/anonymous-email-attachment';
import { ModelMetadataSerializer } from 'serialize-ts/dist/serializers/model-metadata.serializer';

@Model()
export class AnonymousEmailContent {
  @Field()
  id: string;

  @Field()
  recipientEmail: string;

  @Field()
  anonymousRecipientEmail: string;

  @Field()
  fromEmail: string;

  @Field()
  subject: string;

  @Field()
  emailDate: string;

  @Field()
  body: string;

  @Field()
  attachmentsCount: number;

  @Field()
  @Type(new ArraySerializer(new ModelMetadataSerializer(AnonymousEmailAttachment)))
  attachments: AnonymousEmailAttachment[];
}
