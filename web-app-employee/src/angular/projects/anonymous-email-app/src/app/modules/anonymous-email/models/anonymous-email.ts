import { Field, Model, ArraySerializer, Type } from 'serialize-ts/dist';
import { AnonymousEmailThread } from './anonymous-email-thread';
import { ModelMetadataSerializer } from 'serialize-ts/dist/serializers/model-metadata.serializer';
import { Buyer } from './buyer';

@Model()
export class AnonymousEmail {
  @Field()
  orderNumber: string;

  @Field()
  status: number;

  @Field()
  buyer: Buyer;

  @Field()
  orderLinesNumber: number;

  @Field()
  createdAt: string;

  @Field()
  @Type(new ArraySerializer(new ModelMetadataSerializer(AnonymousEmailThread)))
  threads: AnonymousEmailThread[];
}
