import { Params } from '@angular/router';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import * as fromRouter from '~core/store';
import { AnonymousEmailState } from './anonymous-email.state';
import { PaginatedResponse } from '~shared/model';
import { FEATURE_NAME } from './feature-name';
import { AnonymousEmail } from '../models/anonymous-email';

export const getModuleFeatureState = createFeatureSelector(FEATURE_NAME);

export const getAnonymousEmailState = createSelector(getModuleFeatureState, (state: AnonymousEmailState) => state);

export const isAnonymousEmailLoading = createSelector(
  getAnonymousEmailState,
  (state: AnonymousEmailState) => state !== null && state.loading,
);

export const getAnonymousEmailPayload = createSelector(
  getAnonymousEmailState,
  (state: AnonymousEmailState) => state.payload,
);

export const loadingAttachments = createSelector(
  getAnonymousEmailState,
  (state: AnonymousEmailState) => state.loadingAttachments,
);

export const getThreadAttachments = createSelector(
  getAnonymousEmailState,
  (state: AnonymousEmailState) => state.threadAttachments,
);

export const getAnonymousEmails = createSelector(
  getAnonymousEmailPayload,
  (payload: PaginatedResponse<AnonymousEmail>) => payload !== null && payload.items,
);

export const getTotalCount = createSelector(
  getAnonymousEmailPayload,
  (payload: PaginatedResponse<AnonymousEmail>) => payload !== null && payload.totalCount,
);

export const getFilterQuery = createSelector(fromRouter.getQueryParams, (filter: Params) => filter);

export const getShouldLoad = createSelector(
  getAnonymousEmailState,
  (state: AnonymousEmailState) => state !== null && state.shouldLoadMore,
);
