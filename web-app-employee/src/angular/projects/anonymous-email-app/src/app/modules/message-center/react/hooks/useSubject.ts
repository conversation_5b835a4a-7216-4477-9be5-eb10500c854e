import { Subject } from '@metromarkets/message-center-sdk';
import { useEffect, useState } from 'react';
import { getSubjects } from '@anonymous-email-app/modules/message-center/react/services/message-center.service';

export const useSubject = () => {
  const [subjects, setSubjects] = useState<Subject[]>();
  useEffect(() => {
    fetchSubjects()
  }, []);
  const fetchSubjects = async () => {
    try {
      const {data} = await getSubjects();
      setSubjects(data);
    } catch (error) {
      console.error('Error fetching subjects', error);
    }
  }
  return { subjects }
}
