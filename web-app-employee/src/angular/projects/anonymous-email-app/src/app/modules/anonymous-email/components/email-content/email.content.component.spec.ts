import 'jest';
import { AnonymousEmailContent } from '../../models/anonymous-email-content';
import { EmailContentComponent } from './email-content.component';
describe('Email Content', () => {
  let component: EmailContentComponent;

  beforeEach(() => {
    component = new EmailContentComponent();
    component.emailsWithAttachments = [
      {
        emailId: '1',
        attachments: [
          {
            type: 'pdf',
            dirname: 'tempdir',
            path: '/etc/somepath',
            timestamp: new Date().toDateString(),
            mimetype: 'file/pdf',
            size: '123456',
          },
        ],
      },
    ];
  });
  it('Email Content renders', () => {
    expect(component).toBeTruthy();
  });

  it('Email Content has attachments', () => {
    const content = new AnonymousEmailContent();
    content.id = '1';
    expect(component.emailHaveAttachments(content)).toEqual(true);
  });
  it('Email Content does not have attachments', () => {
    const content = new AnonymousEmailContent();
    content.id = '1';
    component.emailsWithAttachments = [];
    expect(component.emailHaveAttachments(content)).toEqual(false);
  });
});
