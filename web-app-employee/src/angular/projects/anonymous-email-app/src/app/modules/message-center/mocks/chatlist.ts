import { ChatStatus, Employee<PERSON>hat, OrderStatus } from '@metromarkets/message-center-sdk';

export const employeeChatList: EmployeeChat[] = [
  {
    'id': '1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d',
    'subject': 'ORDER_TRACKING',
    'customSubject': 'Custom Subject',
    'status': ChatStatus.NEW,
    'initiatorUserType': 'SELLER',
    'isSeen': false,
    'buyer': {
      'id': '123e4567-e89b-12d3-a456-426614174000',
      'firstName': 'Alice',
      'lastName': 'Johnson'
    },
    'seller': [
      {
        'id': 'abc12345-6789-def0-1234-56789abcdef0',
        'email': '<EMAIL>',
        'firstName': 'Tom',
        'lastName': 'Riddle'
      }
    ],
    'organization': {
      'id': 'org12345-6789-def0-1234-56789abcdef1',
      'name': 'Tech Ventures',
      'shopName': 'TechShop'
    },
    'order': {
      'id': 'ord12345-6789-def0-1234-56789abcdef2',
      'orderNumber': 'O35-111111111',
      'salesChannel': 'DE'
      , 'status': OrderStatus.CONFIRMED
    },
    'createdAt': '2025-01-10T10:00:00+00:00'
  },
  {
    'id': '2b3c4d5e-6f7a-8b9c-0d1e-2f3a4b5c6d7e',
    'subject': 'ORDER_TRACKING',
    'customSubject': 'Custom Subject',
    'status': ChatStatus.RESOLVED,
    'initiatorUserType': 'SELLER',
    'isSeen': false,
    'buyer': {
      'id': '223e4567-e89b-12d3-a456-426614174001',
      'firstName': 'Bob',
      'lastName': 'Smith'
    },
    'seller': [
      {
        'id': 'bbc12345-6789-def0-1234-56789abcdef0',
        'email': '<EMAIL>',
        'firstName': 'Harry',
        'lastName': 'Potter'
      }
    ],
    'organization': {
      'id': 'org22345-6789-def0-1234-56789abcdef1',
      'name': 'Magic Mart',
      'shopName': 'Wizards Den'
    },
    'order': {
      'id': 'ord22345-6789-def0-1234-56789abcdef2',
      'orderNumber': 'O35-222222222',
      'salesChannel': 'DE',
      'status': OrderStatus.WAITING_FOR_PAYMENT
    },
    'createdAt': '2025-01-11T11:00:00+00:00'
  },
  {
    'id': '3c4d5e6f-7a8b-9c0d-1e2f-3a4b5c6d7e8f',
    'subject': 'ORDER_TRACKING',
    'customSubject': 'Custom Subject',
    status: ChatStatus.NEW,
    'initiatorUserType': 'SELLER',
    'isSeen': false,
    'buyer': {
      'id': '323e4567-e89b-12d3-a456-426614174002',
      'firstName': 'Clara',
      'lastName': 'Lee'
    },
    'seller': [
      {
        'id': 'cbc12345-6789-def0-1234-56789abcdef0',
        'email': '<EMAIL>',
        'firstName': 'Hermione',
        'lastName': 'Granger'
      }
    ],
    'organization': {
      'id': 'org32345-6789-def0-1234-56789abcdef1',
      'name': 'Books & More',
      'shopName': 'Knowledge Hub'
    },
    'order': {
      'id': 'ord32345-6789-def0-1234-56789abcdef2',
      'orderNumber': 'O35-333333333',
      salesChannel: 'FR',
      status: OrderStatus.WAITING_FOR_PAYMENT
    },
    'createdAt': '2025-01-12T12:00:00+00:00'
  },
  {
    'id': '4d5e6f7a-8b9c-0d1e-2f3a-4b5c6d7e8f9g',
    'subject': 'ORDER_TRACKING',
    'customSubject': 'Custom Subject',
    status: ChatStatus.NEW,
    'initiatorUserType': 'SELLER',
    'isSeen': false,
    'buyer': {
      'id': '423e4567-e89b-12d3-a456-426614174003',
      'firstName': 'Daniel',
      'lastName': 'Kim'
    },
    'seller': [
      {
        'id': 'dbc12345-6789-def0-1234-56789abcdef0',
        'email': '<EMAIL>',
        'firstName': 'Ron',
        'lastName': 'Weasley'
      }
    ],
    'organization': {
      'id': 'org42345-6789-def0-1234-56789abcdef1',
      'name': 'Daily Needs Co.',
      'shopName': 'QuickCart'
    },
    'order': {
      'id': 'ord42345-6789-def0-1234-56789abcdef2',
      'orderNumber': 'O35-444444444',
      'salesChannel': 'IT',
      status: OrderStatus.WAITING_FOR_PAYMENT
    },
    'createdAt': '2025-01-13T13:00:00+00:00'
  },
  {
    'id': '5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9g0h',
    'subject': 'ORDER_TRACKING',
    'customSubject': 'Custom Subject',
    status: ChatStatus.NEW,
    'initiatorUserType': 'SELLER',
    'isSeen': false,
    'buyer': {
      'id': '523e4567-e89b-12d3-a456-426614174004',
      'firstName': 'Emily',
      'lastName': 'Clark'
    },
    'seller': [
      {
        'id': 'ebc12345-6789-def0-1234-56789abcdef0',
        'email': '<EMAIL>',
        'firstName': 'Luna',
        'lastName': 'Lovegood'
      }
    ],
    'organization': {
      'id': 'org52345-6789-def0-1234-56789abcdef1',
      'name': 'Style Avenue',
      'shopName': 'FashionFirst'
    },
    'order': {
      'id': 'ord52345-6789-def0-1234-56789abcdef2',
      'orderNumber': 'O35-555555555',
      'salesChannel': 'ES',
      status: OrderStatus.WAITING_FOR_PAYMENT
    },
    'createdAt': '2025-01-14T14:00:00+00:00'
  },
  {
    'id': '6f7a8b9c-0d1e-2f3a-4b5c-6d7e8f9g0h1i',
    'subject': 'ORDER_TRACKING',
    'customSubject': 'Custom Subject',
    status: ChatStatus.NEW,
    'initiatorUserType': 'SELLER',
    'isSeen': false,
    'buyer': {
      'id': '623e4567-e89b-12d3-a456-426614174005',
      'firstName': 'Frank',
      'lastName': 'Thomas'
    },
    'seller': [
      {
        'id': 'fbc12345-6789-def0-1234-56789abcdef0',
        'email': '<EMAIL>',
        'firstName': 'Neville',
        'lastName': 'Longbottom'
      }
    ],
    'organization': {
      'id': 'org62345-6789-def0-1234-56789abcdef1',
      'name': 'Gadget Galaxy',
      'shopName': 'GadgetZone'
    },
    'order': {
      'id': 'ord62345-6789-def0-1234-56789abcdef2',
      'orderNumber': 'O35-666666666',
      'salesChannel': 'DE',
      status: OrderStatus.WAITING_FOR_PAYMENT
    },
    'createdAt': '2025-01-15T15:00:00+00:00'
  },
  {
    'id': '7a8b9c0d-1e2f-3a4b-5c6d-7e8f9g0h1i2j',
    'subject': 'ORDER_TRACKING',
    'customSubject': 'Custom Subject',
    status: ChatStatus.NEW,
    'initiatorUserType': 'SELLER',
    'isSeen': false,
    'buyer': {
      'id': '723e4567-e89b-12d3-a456-426614174006',
      'firstName': 'Grace',
      'lastName': 'Hopper'
    },
    'seller': [
      {
        'id': 'gbc12345-6789-def0-1234-56789abcdef0',
        'email': '<EMAIL>',
        'firstName': 'Ginny',
        'lastName': 'Weasley'
      }
    ],
    'organization': {
      'id': 'org72345-6789-def0-1234-56789abcdef1',
      'name': 'Code Supplies',
      'shopName': 'DevMart'
    },
    'order': {
      'id': 'ord72345-6789-def0-1234-56789abcdef2',
      'orderNumber': 'O35-777777777',
      'salesChannel': 'ES',
      status: OrderStatus.WAITING_FOR_PAYMENT
    },
    'createdAt': '2025-01-16T16:00:00+00:00'
  },
  {
    'id': '8b9c0d1e-2f3a-4b5c-6d7e-8f9g0h1i2j3k',
    'subject': 'ORDER_TRACKING',
    'customSubject': 'Custom Subject',
    status: ChatStatus.NEW,
    'initiatorUserType': 'SELLER',
    'isSeen': false,
    'buyer': {
      'id': '823e4567-e89b-12d3-a456-426614174007',
      'firstName': 'Henry',
      'lastName': 'Ford'
    },
    'seller': [
      {
        'id': 'hbc12345-6789-def0-1234-56789abcdef0',
        'email': '<EMAIL>',
        'firstName': 'Draco',
        'lastName': 'Malfoy'
      }
    ],
    'organization': {
      'id': 'org82345-6789-def0-1234-56789abcdef1',
      'name': 'Auto Parts Hub',
      'shopName': 'GearZone'
    },
    'order': {
      'id': 'ord82345-6789-def0-1234-56789abcdef2',
      'orderNumber': 'O35-888888888',
      'salesChannel': 'PT',
      status: OrderStatus.WAITING_FOR_PAYMENT
    },
    'createdAt': '2025-01-17T17:00:00+00:00'
  },
  {
    'id': '9c0d1e2f-3a4b-5c6d-7e8f-9g0h1i2j3k4l',
    'subject': 'ORDER_TRACKING',
    'customSubject': 'Custom Subject',
    status: ChatStatus.NEW,
    'initiatorUserType': 'SELLER',
    'isSeen': false,
    'buyer': {
      'id': '923e4567-e89b-12d3-a456-426614174008',
      'firstName': 'Isabel',
      'lastName': 'Martinez'
    },
    'seller': [
      {
        'id': 'ibc12345-6789-def0-1234-56789abcdef0',
        'email': '<EMAIL>',
        'firstName': 'Cedric',
        'lastName': 'Diggory'
      }
    ],
    'organization': {
      'id': 'org92345-6789-def0-1234-56789abcdef1',
      'name': 'House Supplies',
      'shopName': 'HomeHaven'
    },
    'order': {
      'id': 'ord92345-6789-def0-1234-56789abcdef2',
      'orderNumber': 'O35-999999999',
      'salesChannel': 'NL',
      status: OrderStatus.WAITING_FOR_PAYMENT
    },
    'createdAt': '2025-01-18T18:00:00+00:00'
  },
  {
    'id': '0d1e2f3a-4b5c-6d7e-8f9g-0h1i2j3k4l5m',
    'subject': 'ORDER_TRACKING',
    'customSubject': 'Custom Subject',
    status: ChatStatus.NEW,
    'initiatorUserType': 'SELLER',
    'isSeen': false,
    'buyer': {
      'id': '023e4567-e89b-12d3-a456-426614174009',
      'firstName': 'Jack',
      'lastName': 'Anderson'
    },
    'seller': [
      {
        'id': 'jbc12345-6789-def0-1234-56789abcdef0',
        'email': '<EMAIL>',
        'firstName': 'Cho',
        'lastName': 'Chang'
      }
    ],
    'organization': {
      'id': 'org02345-6789-def0-1234-56789abcdef1',
      'name': 'Kitchen Experts',
      'shopName': 'CookStore'
    },
    'order': {
      'id': 'ord02345-6789-def0-1234-56789abcdef2',
      'orderNumber': 'O35-101010101',
      'salesChannel': 'ES',
      status: OrderStatus.WAITING_FOR_PAYMENT
    },
    'createdAt': '2025-01-19T19:00:00+00:00'
  }

];
