import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { PaginatedResponse, RequestParams } from '~shared/model';
import { Observable } from 'rxjs';
import { AnonymousEmail } from '../models/anonymous-email';
import { queryBuilder } from '@metromarkets/sdk-17';
import { deserialize } from 'serialize-ts';
import { map } from 'rxjs/operators';
import { ANONYMOUS_EMAILS_API } from '../constants/api';

@Injectable({
  providedIn: 'root',
})
export class AnonymousEmailService {
  constructor(private httpClient: HttpClient) {}

  loadAnonymousEmails(requestParams: RequestParams): Observable<PaginatedResponse<AnonymousEmail>> {
    const params = queryBuilder.toParams(requestParams) as HttpParams;

    return this.httpClient.get<AnonymousEmail[]>(ANONYMOUS_EMAILS_API, { params }).pipe(
      map(response => ({
        totalCount: response.length,
        items: response.map(i => deserialize(i, AnonymousEmail)),
      })),
    );
  }
}
