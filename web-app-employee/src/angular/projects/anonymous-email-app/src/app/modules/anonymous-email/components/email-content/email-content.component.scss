.email-content-wrapper {
  padding: 20px;
  max-height: 800px;

  .divider {
    border-top: #E6E7EA;
    margin: 17px 0 10px;
  }

  .email-content-wrapper__content {
    padding: 10px 20px 10px 0;

    .content__email-headers {
      margin-top: 10px;
      display: flex;
      flex: 0;
      flex-direction: column;

      .email-headers__text {
        font-size: 14px;
      }
      .email-headers__title {
        font-size: 14px;
        font-weight: bold;
      }
    }
    .email-attachments__name {
      float: left;
      width: 100%;
    }

    .mat-mdc-progress-bar {
      z-index: -1000;
    }

    .content__email-body {
      margin-top: 20px;
      font-size: 14px;
    }
  }

  .heading{
    margin-bottom: 14px;
    line-height: 24px;
    .order-number {
      color: #002D72;
      font-size: 16px;
      font-weight: 400;
      float: left;
    }
    .order-heading{
      color: #002D72;
      font-size: 16px;
      font-weight: 700;
      float: left;
    }
    .order-status-chip {
      border-radius: 16px;
      padding: 4px 12px;
      float: left;
      margin-left: 10px;
    }
  }

  .email-data {
    width: 80%;

    label {
      color: #002D72;
      font-size: 14px;
      font-weight: 700;
      line-height: 25px;
    }

    span {
      color: #002D72;
      font-size: 14px;
      font-weight: 400;
      line-height: 25px;

      &.seller-id {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
