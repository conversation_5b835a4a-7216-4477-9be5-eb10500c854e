import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { AnonymousEmail } from '../../models/anonymous-email';
import { ORDER_STATUSES } from '../../constants/order-statuses';
@Component({
  selector: 'app-order-number',
  templateUrl: './order-number.component.html',
  styleUrls: ['./order-number.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OrderNumberComponent {
  @Input()
  email: AnonymousEmail;
  STATUS_DATA = ORDER_STATUSES;

  getStatusData() {
    if (this.STATUS_DATA[this.email.status] !== undefined) {
      return this.STATUS_DATA[this.email.status];
    }
    return { title: 'N/A', color: '#d6f2e6' };
  }
}
