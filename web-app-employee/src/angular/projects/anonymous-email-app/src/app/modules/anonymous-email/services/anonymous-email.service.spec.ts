import 'jest';
import { AnonymousEmailService } from './anonymous-email.service';
import { EMAILS_MOCK } from '../mocks/emails.mock';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed, inject } from '@angular/core/testing';
import { ANONYMOUS_EMAILS_API } from '../constants/api';

describe('Anonymous Email Service', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [AnonymousEmailService],
      imports: [HttpClientTestingModule],
    });
  });
  it('Order Number renders', inject(
    [HttpTestingController, AnonymousEmailService],
    (httpMock: HttpTestingController, service: AnonymousEmailService) => {
      service.loadAnonymousEmails({}).subscribe(data => {
        expect(data.totalCount).toBe(90);
      });
      const req = httpMock.expectOne(ANONYMOUS_EMAILS_API);
      expect(req.request.method).toEqual('GET');
      req.flush(EMAILS_MOCK);
    },
  ));
});
