import * as faker from 'faker';
import { deserialize } from 'serialize-ts';
import { AnonymousEmailThread } from '../models/anonymous-email-thread';
import { AnonymousEmailContent } from '../models/anonymous-email-content';
import { AnonymousEmail } from '../models/anonymous-email';
const totalCount = 90;
const minOrderNo = 1000000;
const maxOrderNo = 9999999;
const randomBodyWordCount = 30;

export const EMAILS_MOCK = Array(totalCount)
  .fill({})
  .map(() => {
    return {
      orderNumber: `O20-${faker.random.number({
        min: minOrderNo,
        max: maxOrderNo,
      })}`,
      buyer: {
        firstName: faker.name.firstName(),
        lastName: faker.name.lastName(),
        type: 'ROLE_CONSUMER',
      },
      orderLinesNumber: faker.random.number({ min: 1, max: 5 }),
      createdAt: faker.date.between('2020-07-01', '2020-08-01'),
      status: faker.random.number({ min: 1, max: 9 }),
      threads: Array(faker.random.number({ min: 1, max: 2 }))
        .fill({})
        .map(() => {
          return {
            threadNumber: faker.random.uuid(),
            seller: {
              id: faker.random.uuid(),
              organisationName: faker.company.companyName(),
            },
            emails: Array(faker.random.number({ min: 1, max: 5 }))
              .fill({})
              .map(() => {
                return {
                  recipientEmail: faker.internet.email(),
                  anonymousRecipientEmail: faker.internet.exampleEmail(),
                  fromEmail: faker.internet.email(),
                  subject: faker.random.words(faker.random.number({ min: 1, max: 5 })),
                  emailDate: faker.date.between('2020-08-01', '2020-08-15'),
                  body: faker.random.words(randomBodyWordCount),
                };
              })
              .map(e => deserialize(e, AnonymousEmailContent)),
          };
        })
        .map(e => deserialize(e, AnonymousEmailThread)),
    };
  })
  .map(e => deserialize(e, AnonymousEmail));
