import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { AnonymousEmailThread } from '../../models/anonymous-email-thread';
import { MAX_THREAD_TEXT_LENGTH } from '../../constants/default-constants';
@Component({
  selector: 'app-thread',
  templateUrl: './thread.component.html',
  styleUrls: ['./thread.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ThreadComponent {
  @Input()
  thread: AnonymousEmailThread;
  MAX_THREAD_TEXT_LENGTH = MAX_THREAD_TEXT_LENGTH;

  shorten(str: string, maxValue = this.MAX_THREAD_TEXT_LENGTH): string {
    return `${str.substr(0, maxValue)}..`;
  }
  hasAttachment(): boolean {
    return false;
  }
}
