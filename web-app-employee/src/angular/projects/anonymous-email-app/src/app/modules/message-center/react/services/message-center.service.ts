import { Chat, Message, Subject } from '@metromarkets/message-center-sdk';
import { httpService } from '@anonymous-email-app/modules/message-center/react/services/http.service';
import {
  CHAT_LIST_API,
  getMessageHistoryApi,
  SUBJECTS_API,
} from '@anonymous-email-app/modules/message-center/react/constants/message-center';

export interface MessageCenterResponse<T, U = any> {
  data: T;
  status: string;
  metadata?: U;
  message?: string;
}

export interface ChatFilters {
  orderNumber?: string;
  destinations?: string[];
  chatId?: string;
}

export type ChatListParams = {
  offset?: number;
  limit?: number;
  filters?: ChatFilters;
};

type ChatListMetadata = {
  total: number;
  offset: number;
  limit: number;
};

export const getChatlist = async (
  params: ChatListParams = {},
): Promise<MessageCenterResponse<Chat[], ChatListMetadata>> => {
  const queryParams = new URLSearchParams();

  if (params.offset !== undefined) {
    queryParams.append('offset', params.offset.toString());
  }
  if (params.limit !== undefined) {
    queryParams.append('limit', params.limit.toString());
  }

  if (params.filters) {
    if (params.filters.orderNumber) {
      queryParams.append('filters[orderNumber]', params.filters.orderNumber);
    }

    if (params.filters.destinations && params.filters.destinations.length > 0) {
      const encodedDestinations = JSON.stringify(params.filters.destinations);
      queryParams.append('filters[destinations]', encodedDestinations);
    }

    if (params.filters.chatId) {
      queryParams.append('filters[chatId]', params.filters.chatId);
    }
  }

  const url = queryParams.toString() ? `${CHAT_LIST_API}?${queryParams.toString()}` : CHAT_LIST_API;

  return httpService.get<MessageCenterResponse<Chat[], ChatListMetadata>>(url);
};

export const getSubjects = async (): Promise<MessageCenterResponse<Subject[]>> => {
  return httpService.get<MessageCenterResponse<Subject[]>>(SUBJECTS_API);
};

export const getMessageHistory = async (chat: Chat): Promise<MessageCenterResponse<Message[]>> => {
  return httpService.get<MessageCenterResponse<Message[]>>(getMessageHistoryApi(chat.id));
};
