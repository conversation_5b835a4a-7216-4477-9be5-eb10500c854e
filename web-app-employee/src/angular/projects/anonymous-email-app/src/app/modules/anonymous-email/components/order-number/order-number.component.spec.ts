import 'jest';
import { EMAILS_MOCK } from '../../mocks/emails.mock';
import { OrderNumberComponent } from './order-number.component';
describe('Order Number', () => {
  let component: OrderNumberComponent;

  beforeEach(() => {
    component = new OrderNumberComponent();
  });
  it('Order Number renders', () => {
    expect(component).toBeTruthy();
  });

  it('Status colors are right', () => {
    component.email = EMAILS_MOCK[0];
    expect(component.getStatusData().title).not.toEqual('N/A');
    component.email.status = -1;
    expect(component.getStatusData().title).toEqual('N/A');
  });
});
