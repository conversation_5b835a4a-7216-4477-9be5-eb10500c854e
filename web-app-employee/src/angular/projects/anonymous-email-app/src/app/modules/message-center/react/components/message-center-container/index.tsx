import { MessageCenterProvider, Settings } from '@metromarkets/message-center-sdk';
import { JSX, useEffect } from 'react';
import { ChatList } from '@anonymous-email-app/modules/message-center/react/components/chat-list';
import { useSubject } from '@anonymous-email-app/modules/message-center/react/hooks/useSubject';
import { useChatList } from '@anonymous-email-app/modules/message-center/react/hooks/useChatlist';
import { ChatDetails } from '@anonymous-email-app/modules/message-center/react/components/chat-details';

interface Props {
  settings: Partial<Settings>;
}

export const MessageCenterContainer = ({ settings }: Props): JSX.Element => {
  const { filteredChats, selectChat, onSearch, selectedChat, loading, hasMore, loadMore, searchOrderNumber } =
    useChatList();
  const { subjects } = useSubject();

  if (!subjects) {
    return <></>;
  }

  return (
    <MessageCenterProvider settings={settings} subjects={subjects}>
      <div className="message-center">
        <h2 className="heading">Messages</h2>
        <div className="message-center__container mc">
          <div className="mc message-center__holder">
            <div className="message-center__chatlist">
              <div className="message-center__height">
                <ChatList
                  chats={filteredChats}
                  onSearch={onSearch}
                  onSelectChat={selectChat}
                  selectedChat={selectedChat}
                  loading={loading}
                  hasMore={hasMore}
                  loadMore={loadMore}
                  orderNumber={searchOrderNumber}
                />
              </div>
            </div>
            {selectedChat && (
              <div className="message-center__details">
                <ChatDetails selectedChat={selectedChat} />
              </div>
            )}
          </div>
        </div>
      </div>
    </MessageCenterProvider>
  );
};
