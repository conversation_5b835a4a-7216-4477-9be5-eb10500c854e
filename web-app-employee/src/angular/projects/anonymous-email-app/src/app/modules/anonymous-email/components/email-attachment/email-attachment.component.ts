import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { AnonymousEmailAttachment } from '@anonymous-email-app/modules/anonymous-email/models/anonymous-email-attachment';
import { AnonymousEmailAttachmentService } from '@anonymous-email-app/modules/anonymous-email/services/anonymous-email-attachment.service';
import { Store } from '@ngrx/store';
import * as fromAnonymousEmails from '@anonymous-email-app/modules/anonymous-email/store';

@Component({
  selector: 'app-email-attachment',
  templateUrl: './email-attachment.component.html',
  styleUrls: ['./email-attachment.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EmailAttachmentComponent {
  @Input()
  attachment: AnonymousEmailAttachment;

  constructor(
    private anonymousEmailAttachmentService: AnonymousEmailAttachmentService,
    private store: Store<fromAnonymousEmails.AnonymousEmailState>,
  ) {}

  downloadAttachment(name): void {
    this.store.dispatch(new fromAnonymousEmails.DownloadingAnonymousEmailAttachment({ name }));
  }
  getAttachmentName(attachment: AnonymousEmailAttachment): string {
    return attachment.path.split('/')[1];
  }
  getAttachmentExtension(attachment: AnonymousEmailAttachment): string {
    switch (attachment.mimetype) {
      case 'image/jpeg':
        return '.jpg';
      case 'image/gif':
        return '.gif';
      case 'application/pdf':
        return '.pdf';
      case 'image/svg+xml':
        return '.svg';
      case 'application/msword':
        return '.doc';
      case 'application/vnd.ms-excel':
        return '.xls';
      case 'text/csv':
        return '.csv';
      default:
        return '';
    }
  }
}
