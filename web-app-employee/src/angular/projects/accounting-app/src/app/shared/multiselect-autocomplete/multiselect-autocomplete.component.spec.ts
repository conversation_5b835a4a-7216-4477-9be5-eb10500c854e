import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MultiselectAutocompleteComponent } from './multiselect-autocomplete.component';
import { MatIconModule } from '@angular/material/icon';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MmCheckboxModule, MmFormFieldModule } from '@metromarkets/components-17';
import { SearchInputModule } from '~shared/modules/search-input/search-input.module';

describe.skip('MultiselectAutocompleteComponent', () => {
  let component: MultiselectAutocompleteComponent;
  let fixture: ComponentFixture<MultiselectAutocompleteComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [MultiselectAutocompleteComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [
        MatAutocompleteModule,
        MatCheckboxModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        FormsModule,
        ReactiveFormsModule,
        MmCheckboxModule,
        SearchInputModule,
        MmFormFieldModule,
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MultiselectAutocompleteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
