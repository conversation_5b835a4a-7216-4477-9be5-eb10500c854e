import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { map, startWith } from 'rxjs/operators';
import { MatAutocompleteTrigger } from '@angular/material/autocomplete';

@Component({
  selector: 'app-multiselect-autocomplete',
  templateUrl: './multiselect-autocomplete.component.html',
  styleUrls: ['./multiselect-autocomplete.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MultiselectAutocompleteComponent implements OnChanges, OnInit {
  @Output() result = new EventEmitter<Array<string>>();
  @Input() placeholder = 'Search';
  @Input() data: any[];
  @Input() key = '';
  rawData: any[] = [];
  selectData: string[] = [];
  filteredData: Array<any>;
  filterString = '';
  filterForm: UntypedFormGroup;

  @ViewChild(MatAutocompleteTrigger) trigger;

  constructor(private formBuilder: UntypedFormBuilder) {
    this.filterForm = this.formBuilder.group({
      search: [''],
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['data']) {
      this.initData();
    }
  }

  ngOnInit() {
    this.filterForm
      .get('search')
      .valueChanges.pipe(
        startWith<string>(''),
        map(value => (typeof value === 'string' ? value : this.filterString)),
      )
      .subscribe(filter => {
        this.filteredData = this.filter(filter);
      });
  }

  private initData() {
    this.rawData = [];
    this.selectData = [];
    this.data.forEach(data => {
      this.rawData.push({ ...data });
      if (data.selected) {
        this.selectData.push(data.id);
      }
    });
    this.filteredData = this.rawData;
    this.filterForm.get('search').setValue('');
    this.filterForm.get('search').updateValueAndValidity();
  }

  private filter = (filter: string) => {
    this.filterString = filter;
    if (filter.length > 0) {
      return this.rawData.filter(option => {
        return option.name.toLowerCase().indexOf(filter.toLowerCase()) >= 0;
      });
    } else {
      return this.rawData;
    }
  };

  displayFn = (): string => '';

  optionClicked = (event: Event, data): void => {
    event.stopPropagation();
    this.toggleSelection(data);
  };

  private toggleSelection = (data): void => {
    data = {
      ...data,
      selected: !data.selected,
    };
    if (data.selected === true) {
      this.selectData.push(data.id);
    } else {
      const i = this.selectData.findIndex(value => value === data.id);
      this.selectData.splice(i, 1);
    }
    this.rawData = this.rawData.map(item => (item.id === data.id ? { ...data } : item));
    this.filteredData = this.filter(this.filterForm.get('search').value);
  };

  emitAdjustedData = (): void => {
    this.trigger.closePanel();
    this.result.emit(this.selectData);
  };

  onFocus() {
    this.trigger._onChange(this.filterForm.get('search').value || '');
    this.trigger.openPanel();
  }

  clearSelection() {
    this.initData();
    this.selectData = [];
    this.emitAdjustedData();
    this.trigger.closePanel();
  }
}
