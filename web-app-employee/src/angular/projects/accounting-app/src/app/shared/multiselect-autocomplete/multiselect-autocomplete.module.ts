import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MmCheckboxModule, MmFlexLayoutModule, MmFormFieldModule } from '@metromarkets/components-17';
import { MultiselectAutocompleteComponent } from './multiselect-autocomplete.component';
import { SearchInputModule } from '~shared/modules/search-input/search-input.module';

@NgModule({
  declarations: [MultiselectAutocompleteComponent],
  imports: [
    CommonModule,
    MatAutocompleteModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    MmCheckboxModule,
    SearchInputModule,
    MmFormFieldModule,
    MmFlexLayoutModule,
  ],
  exports: [MultiselectAutocompleteComponent],
})
export class MultiselectAutocompleteModule {}
