<div class="multiselect">
  <form [formGroup]="filterForm">
    <mm-field class="search__field">
      <mat-icon class="search-field__icon">search</mat-icon>
      <input
        mmInput
        type="text"
        [placeholder]="placeholder"
        [matAutocomplete]="auto"
        formControlName="search"
        #trigger="matAutocompleteTrigger"
        (focus)="onFocus()"
      />
    </mm-field>
  </form>

  <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn">
    <mat-option>
      <div class="multiselect__action-buttons" fxLayout="row" fxLayoutAlign="space-between stretch">
        <button (click)="clearSelection()">Clear All</button>
        <button (click)="emitAdjustedData()">Apply</button>
      </div>
    </mat-option>
    <mat-option *ngFor="let data of filteredData">
      <div (click)="optionClicked($event, data)">
        <mm-checkbox
          [checked]="data.selected"
          (change)="toggleSelection(data)"
          (click)="$event.stopPropagation()"
          class="multiselect__checkbox"
        >
          {{ data.name }}
        </mm-checkbox>
      </div>
    </mat-option>
  </mat-autocomplete>
</div>
