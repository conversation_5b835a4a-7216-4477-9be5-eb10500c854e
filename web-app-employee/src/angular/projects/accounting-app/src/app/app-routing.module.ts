import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import {
  ORGANIZATIONS_ROUTE,
  PAYOUTS_ROUTE,
  SELLER_BALANCE_ROUTE,
  SELLER_INVOICES_ROUTE,
} from '@root/shared/constants';
import { UserRolesEnum } from '@root/shared/model';
import { AuthGuard } from '@root/modules/user-auth/services/auth.guard';

const routes: Routes = [
  {
    path: '',
    children: [
      { path: '', redirectTo: SELLER_INVOICES_ROUTE, pathMatch: 'full' },
      {
        path: SELLER_INVOICES_ROUTE,
        loadChildren: () =>
          import('./modules/seller-invoices/seller-invoices.module').then(m => m.SellerInvoicesModule),
        data: { roles: [UserRolesEnum.METROMARKETS_EMP_ACCOUNTING] },
      },
      {
        path: ORGANIZATIONS_ROUTE,
        loadChildren: () => import('./modules/organizations/organizations.module').then(m => m.OrganizationsModule),
        data: { roles: [UserRolesEnum.METROMARKETS_EMP_ACCOUNTING] },
      },
      {
        path: PAYOUTS_ROUTE,
        loadChildren: () => import('./modules/payouts/payouts.module').then(m => m.PayoutsModule),
        data: { roles: [UserRolesEnum.METROMARKETS_EMP_ACCOUNTING] },
      },
      {
        path: SELLER_BALANCE_ROUTE,
        canLoad: [AuthGuard],
        loadChildren: () => import('./modules/seller-balance/seller-balance.module').then(m => m.SellerBalanceModule),
        data: { roles: [UserRolesEnum.METROMARKETS_EMP_SELLER_BALANCE] },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountingRoutingModule {}
