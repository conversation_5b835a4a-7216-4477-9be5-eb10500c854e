<!--suppress HtmlFormInputWithoutLabel -->
<input
  mmInput
  [matAutocomplete]="shopName"
  placeholder="Select Shop Name"
  (input)="searchSellers(sellerInput.value)"
  (blur)="onTouched()"
  autocomplete="off"
  [formControl]="sellerFormControl"
  #sellerInput
/>
<mat-autocomplete
  [displayWith]="displayShopName"
  (optionSelected)="onSelect($event)"
  (opened)="searchSellers(sellerInput.value)"
  #shopName="matAutocomplete"
>
  <mat-option *ngFor="let option of sellers" [value]="option">
    {{ option.shopName }}
  </mat-option>
</mat-autocomplete>
