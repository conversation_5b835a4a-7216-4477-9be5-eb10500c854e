import { createAction, props } from '@ngrx/store';
import { SellerInvoiceDetails } from '@accounting-app/modules/seller-invoice-details/models';
import { ErrorResponse } from '~shared/model';

export const loadInvoiceDetails = createAction('[Invoice Details] Invoice details load', props<{ id: string }>());

export const loadInvoiceDetailsSuccess = createAction(
  '[Invoice Details] Invoice details load success',
  props<{ invoice: SellerInvoiceDetails }>(),
);

export const loadInvoiceDetailsFailure = createAction(
  '[Invoice Details] Invoice details load failure',
  props<{ error: ErrorResponse }>(),
);
