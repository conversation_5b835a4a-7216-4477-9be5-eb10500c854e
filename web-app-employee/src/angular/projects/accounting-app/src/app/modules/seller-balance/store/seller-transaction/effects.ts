import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action, select, Store } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import { catchError, filter, map, switchMap, withLatestFrom } from 'rxjs/operators';
import { SellerBalanceService } from '@accounting-app/modules/seller-balance/services/seller-balance.service';
import {
  DownloadTransactions,
  DownloadTransactionsFailure,
  DownloadTransactionsSuccess,
  LoadTransactions,
  LoadTransactionsFailure,
  LoadTransactionsSuccess,
  SellerTransactionsActionTypes,
} from '@accounting-app/modules/seller-balance/store/seller-transaction/actions';
import { ROUTER_NAVIGATED, RouterNavigatedAction } from '@ngrx/router-store';
import { get, isEmpty } from 'lodash';
import { ACCOUNTING_ROUTE, SELLER_BALANCE_ROUTE } from '~shared/constants';
import {
  DEFAULT_LIMIT,
  DEFAULT_OFFSET,
  DOWNLOAD_LIMIT,
  DOWNLOAD_OFFSET,
} from '@accounting-app/modules/seller-balance/constants';
import * as fromRouter from '~core/store';

@Injectable()
export class SellerTransactionsEffects {
  loadSellerTransactions$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(SellerTransactionsActionTypes.LOAD_TRANSACTIONS),
      map((action: LoadTransactions) => action.payload),
      switchMap(({ sellerId, params }) =>
        this.httpService.getSellerTransactions(sellerId, params).pipe(
          map(transactions => new LoadTransactionsSuccess({ transactions })),
          catchError(error => of(new LoadTransactionsFailure({ error }))),
        ),
      ),
    ),
  );

  loadSellerTransactionsByRouter$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(ROUTER_NAVIGATED),
      map((action: RouterNavigatedAction) => get(action, 'payload.routerState', {})),
      map(({ url, queryParams, params }: fromRouter.RouterStateUrl) => ({ url, queryParams, params })),
      filter(({ url, params }) => url.indexOf(`${ACCOUNTING_ROUTE}/${SELLER_BALANCE_ROUTE}`) !== -1 && params.id),
      map(
        ({ queryParams, params }) =>
          new LoadTransactions({
            sellerId: params.id,
            params: !isEmpty(queryParams)
              ? queryParams
              : {
                  offset: DEFAULT_OFFSET,
                  limit: DEFAULT_LIMIT,
                },
          }),
      ),
    ),
  );

  downloadSellerTransactions$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(SellerTransactionsActionTypes.DOWNLOAD_TRANSACTIONS),
      map((action: DownloadTransactions) => action.payload),
      withLatestFrom(this.store$.pipe(select(fromRouter.getQueryParams))),
      switchMap(([{ sellerId, fileName }, params]) =>
        this.httpService
          .downloadSellerTransaction(sellerId, fileName, {
            ...params,
            offset: DOWNLOAD_OFFSET,
            limit: DOWNLOAD_LIMIT,
          })
          .pipe(
            map(transactions => new DownloadTransactionsSuccess()),
            catchError(error => of(new DownloadTransactionsFailure({ error }))),
          ),
      ),
    ),
  );

  constructor(
    private actions$: Actions,
    private httpService: SellerBalanceService,
    @Inject(LOCALE_ID) private locale: string,
    private store$: Store<fromRouter.RouterState>,
  ) {}
}
