import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { ROUTER_NAVIGATED, RouterNavigatedAction } from '@ngrx/router-store';
import { Action, select, Store } from '@ngrx/store';
import { get, isEmpty } from 'lodash';
import { Observable, of } from 'rxjs';
import { catchError, filter, map, switchMap, withLatestFrom } from 'rxjs/operators';
import * as fromRouter from '~core/store';
import { ACCOUNTING_ROUTE, SELLER_BALANCE_ROUTE } from '~shared/constants';
import { isUrlMatchRoute } from '~shared/utils/common';
import {
  DownloadSellerBalance,
  DownloadSellerBalanceFailure,
  DownloadSellerBalanceSuccess,
  LoadSellerBalanceFailure,
  LoadSellerBalanceList,
  LoadSellerBalanceListSuccess,
  SellerBalanceListActionTypes,
} from '@accounting-app/modules/seller-balance/store/seller-balance-list/actions';
import { SellerBalanceService } from '@accounting-app/modules/seller-balance/services/seller-balance.service';
import { formatDate } from '@angular/common';
import { DATE_FORMAT } from '@accounting-app/modules/seller-balance/constants/date-format';
import { DOWNLOAD_LIMIT, DOWNLOAD_OFFSET, INIT_QUERY_PARAMS } from '@accounting-app/modules/seller-balance/constants';

@Injectable()
export class SellerBalanceListEffects {
  loadSellerBalance$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(SellerBalanceListActionTypes.LOAD_SELLER_BALANCE_LIST),
      map((action: LoadSellerBalanceList) => action.payload),
      switchMap(({ params }) =>
        this.httpService.getSellerBalances(params).pipe(
          map(sellerBalance => new LoadSellerBalanceListSuccess({ sellerBalance })),
          catchError(error => of(new LoadSellerBalanceFailure({ error }))),
        ),
      ),
    ),
  );

  loadSellerBalanceByRouter$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(ROUTER_NAVIGATED),
      map((action: RouterNavigatedAction) => get(action, 'payload.routerState', {})),
      map(({ url, queryParams }: fromRouter.RouterStateUrl) => ({ url, queryParams })),
      filter(({ url }) => isUrlMatchRoute(url, `${ACCOUNTING_ROUTE}/${SELLER_BALANCE_ROUTE}`)),
      map(({ queryParams }) =>
        !isEmpty(queryParams)
          ? queryParams
          : {
              offset: INIT_QUERY_PARAMS.offset,
              limit: INIT_QUERY_PARAMS.limit,
              filter: {
                date: formatDate(new Date(), DATE_FORMAT, this.locale),
              },
            },
      ),
      map(params => new LoadSellerBalanceList({ params })),
    ),
  );

  downloadSellerTransactions$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(SellerBalanceListActionTypes.DOWNLOAD_SELLER_BALANCE),
      map((action: DownloadSellerBalance) => action.payload),
      withLatestFrom(this.store$.pipe(select(fromRouter.getQueryParams))),
      switchMap(([{ sellerId, fileName }, params]) =>
        this.httpService
          .downloadSellerBalance(
            fileName,
            {
              ...params,
              offset: DOWNLOAD_OFFSET,
              limit: DOWNLOAD_LIMIT,
            },
            sellerId,
          )
          .pipe(
            map(transactions => new DownloadSellerBalanceSuccess()),
            catchError(error => of(new DownloadSellerBalanceFailure({ error }))),
          ),
      ),
    ),
  );

  constructor(
    private actions$: Actions,
    private httpService: SellerBalanceService,
    @Inject(LOCALE_ID) private locale: string,
    private store$: Store<fromRouter.RouterState>,
  ) {}
}
