import { createFeatureSelector, createSelector } from '@ngrx/store';
import { SELLER_BALANCE_FEATURE_NAME } from '@accounting-app/modules/seller-balance/constants';
import { State } from '@accounting-app/modules/seller-balance/store';
import { SellerBalanceState } from '@accounting-app/modules/seller-balance/store/seller-balance-list/state';
import * as fromRouter from '~core/store';
import { Params } from '@angular/router';
import {
  PaymentBalance,
  SellerBalance,
  SellerBalancePaginatedResponse,
} from '@accounting-app/modules/seller-balance/models';

export const getModuleFeatureState = createFeatureSelector(SELLER_BALANCE_FEATURE_NAME);

export const getSellerBalanceState = createSelector(getModuleFeatureState, (state: State) => state.sellerBalance);

export const isSellerBalanceListLoading = createSelector(
  getSellerBalanceState,
  (state: SellerBalanceState) => state && state.loading,
);

export const getSellerBalancePayload = createSelector(
  getSellerBalanceState,
  (state: SellerBalanceState) => state.payload,
);

export const getSellerBalance = createSelector(
  getSellerBalancePayload,
  (payload: SellerBalancePaginatedResponse<SellerBalance, PaymentBalance>) => payload && payload.items,
);

export const getTotalCount = createSelector(
  getSellerBalancePayload,
  (payload: SellerBalancePaginatedResponse<SellerBalance, PaymentBalance>) => payload && payload.totalCount,
);

export const getTotals = createSelector(
  getSellerBalancePayload,
  (payload: SellerBalancePaginatedResponse<SellerBalance, PaymentBalance>) => payload && payload.totals,
);

export const getDateFromUrl = createSelector(
  fromRouter.getFilterFromQueryParams,
  (filter: Params) => (filter && filter.date) || new Date(),
);
