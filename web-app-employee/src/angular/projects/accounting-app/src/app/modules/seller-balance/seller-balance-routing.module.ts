import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SellerBalanceListComponent } from '@accounting-app/modules/seller-balance/components/seller-balance-list/seller-balance-list.component';
import { SellerTransactionComponent } from '@accounting-app/modules/seller-balance/components/seller-transaction/seller-transaction.component';

const routes: Routes = [
  { path: '', component: SellerBalanceListComponent },
  { path: ':id', component: SellerTransactionComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SellerBalanceRoutingModule {}
