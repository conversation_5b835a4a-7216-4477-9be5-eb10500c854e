import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { queryBuilder } from '@metromarkets/sdk-17';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { deserialize } from 'serialize-ts/dist';
import { ORGANIZATIONS_API } from '@accounting-app/modules/organizations/constants';
import { Organizations } from '@accounting-app/modules/organizations/models';
import { RequestParams } from '~shared/model';

@Injectable({
  providedIn: 'root',
})
export class OrganizationsService {
  constructor(private httpClient: HttpClient) {}

  loadOrganizations(requestParams: RequestParams): Observable<Organizations> {
    const params = queryBuilder.toParams(requestParams) as HttpParams;

    return this.httpClient
      .get<Organizations>(ORGANIZATIONS_API, { params })
      .pipe(map(response => deserialize(response, Organizations)));
  }
}
