import { Field } from 'serialize-ts';
import { Money } from '~shared/model';

export class SellerTransaction {
  @Field()
  orderNumber: string;

  @Field()
  transactionDate: string;

  @Field()
  paidoutDate: string;

  @Field()
  accountedAt: string;

  @Field()
  pspReference: string;

  @Field()
  paymentProvider: string;

  @Field()
  transactionType: string;

  @Field()
  salesChannel: string;

  @Field()
  amount: Money;

  @Field()
  vatRate: string;
}
