import { Component, <PERSON><PERSON><PERSON><PERSON>, OnIni<PERSON>, QueryList, ViewChildren } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { MmCheckboxComponent, MmDialogRefService, MmDialogService } from '@metromarkets/components-17';
import { urlBuilder } from '@metromarkets/sdk-17';
import { select, Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { SellerInvoice, SellerInvoiceStatesEnum } from '@accounting-app/modules/seller-invoices/models';
import * as fromInvoices from '@accounting-app/modules/seller-invoices/store/invoices';
import { createInvoiceSuccess } from '@accounting-app/modules/seller-invoices/store/invoices/actions/create-invoice.actions';
import { loadInvoices } from '@accounting-app/modules/seller-invoices/store/invoices/actions/load-invoices.actions';
import {
  sendInvoices,
  sendInvoicesSuccess,
} from '@accounting-app/modules/seller-invoices/store/invoices/actions/send-invoices.actions';
import { RequestParams } from '~shared/model';
import { QueryParamsHandler } from '~shared/services/query-params-handler.service';
import { StoreActions } from '~shared/services/store-actions.service';
import { ConfirmDialogComponent } from '~shared/modules/confirm-dialog/components/confirm-dialog/confirm-dialog.component';
import { ConfirmDialogData } from '~shared/modules/confirm-dialog/models';
import { GenerateInvoicesModalComponent } from './generate-invoices-modal/generate-invoices-modal.component';
import { INVOICES_LIMIT, INVOICES_OFFSET, SELLERS_MODIFIED_INVOICES_API_ROUTE } from '../constants';
import { DomSanitizer } from '@angular/platform-browser';
import { OAuthService } from 'angular-oauth2-oidc';

const ENABLE_PAGE_LINKS = true;
const ENABLE_JUMP_TO_PAGE = true;

@Component({
  selector: 'employee-app-seller-invoices',
  templateUrl: './seller-invoices.component.html',
  styleUrls: ['./seller-invoices.component.scss'],
  providers: [QueryParamsHandler],
})
export class SellerInvoicesComponent implements OnInit, OnDestroy {
  public invoices$: Observable<SellerInvoice[]>;
  public totalCount$: Observable<number>;
  public allInvoicesSelected: boolean;
  public sendButtonEnabled: boolean;
  public displayedColumns = [
    'selector',
    'invoiceNumber',
    'sellerNumber',
    'shopName',
    'period',
    'total',
    'downloadLink',
    'created',
    'status',
  ];
  public INVOICES_LIMIT = INVOICES_LIMIT;
  public enableJumpToPage = ENABLE_JUMP_TO_PAGE;
  public enablePageLinks = ENABLE_PAGE_LINKS;
  public isNewInvoiceTableVisible = false;
  public iframeSourceUrl = null;
  @ViewChildren('invoiceCheckbox') private invoiceCheckboxes: QueryList<MmCheckboxComponent>;
  private readonly unsubscribe$ = new Subject();
  private dialogRefService: MmDialogRefService<GenerateInvoicesModalComponent>;

  constructor(
    private store: Store<fromInvoices.InvoicesState>,
    private route: ActivatedRoute,
    private queryParamsHandler: QueryParamsHandler,
    private dialogService: MmDialogService,
    private storeActions: StoreActions,
    private paramsHandler: QueryParamsHandler,
    private domSanitizer: DomSanitizer,
    private oAuthService: OAuthService,
  ) {}

  private get selectedInvoices(): Partial<SellerInvoice>[] {
    return this.invoiceCheckboxes.reduce((prev: MmCheckboxComponent[], cur: MmCheckboxComponent) => {
      if (cur.checked) {
        return [...prev, cur.value];
      }
      return prev;
    }, []);
  }

  public ngOnInit() {
    this.getModifiedInvoicesSource();
    this.invoices$ = this.store.pipe(select(fromInvoices.selectInvoices));
    this.totalCount$ = this.store.pipe(select(fromInvoices.totalCount));

    this.route.queryParams.pipe(takeUntil(this.unsubscribe$)).subscribe(this.loadInvoices.bind(this));

    this.storeActions
      .ofType(createInvoiceSuccess.type)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => this.dialogRefService.close());

    this.storeActions
      .ofType(sendInvoicesSuccess.type)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => (this.sendButtonEnabled = false));
  }

  public ngOnDestroy(): void {
    this.unsubscribe$.next(void 0);
    this.unsubscribe$.complete();
  }

  changePage(pageEvent): void {
    const { currentPageIndex, pageSizeSelectedOption } = pageEvent;
    this.INVOICES_LIMIT = pageSizeSelectedOption;
    const nextPaging = {
      offset: currentPageIndex * pageSizeSelectedOption,
      limit: pageSizeSelectedOption,
    };
    this.paramsHandler.mergeQueryParams(nextPaging);
  }

  public openGenerateInvoicesModal(): void {
    this.dialogRefService = this.dialogService.open(GenerateInvoicesModalComponent);
  }

  public selectAll(target: HTMLInputElement): void {
    this.allInvoicesSelected = target.checked;
    this.sendButtonEnabled = this.allInvoicesSelected;
  }

  public selectOne(): void {
    this.sendButtonEnabled = !!this.selectedInvoices.length;
  }

  public sendInvoices(): void {
    const selected = this.selectedInvoices;
    const sentInvoices = selected.filter(invoice => invoice.status === SellerInvoiceStatesEnum.Sent);
    const selectedIds = selected.map(invoice => invoice.id);

    let message = `You are going to send ${selected.length} invoice(s).`;
    message = sentInvoices.length ? `${message}<br>(${sentInvoices.length} with status - 'Sent')` : message;

    this.openConfirmDialog({ message })
      .afterClosed()
      .pipe(
        filter((isConfirmed: boolean) => isConfirmed),
        takeUntil(this.unsubscribe$),
      )
      .subscribe(() => this.store.dispatch(sendInvoices({ ids: selectedIds })));
  }

  public filterInvoices(params: { [key: string]: any }): void {
    this.queryParamsHandler.changeQueryParams(params, 'merge');
    this.resetSelected();
  }

  public openPdfInvoice(url): void {
    window.open(url, '_blank');
  }

  private loadInvoices(requestParams: RequestParams): void {
    const params = urlBuilder.decode(requestParams);

    this.store.dispatch(
      loadInvoices({
        params: { offset: INVOICES_OFFSET, limit: INVOICES_LIMIT, ...params },
      }),
    );
  }

  private openConfirmDialog(data: ConfirmDialogData): MmDialogRefService<ConfirmDialogComponent> {
    return this.dialogService.open(ConfirmDialogComponent, {
      data: {
        ...data,
        submit: {
          color: 'metro-blue',
          label: 'Confirm',
        },
        cancel: {
          color: 'secondary',
          label: 'Cancel',
        },
      },
      backdropDisableClose: true,
    });
  }

  private resetSelected(): void {
    this.allInvoicesSelected = false;
    this.sendButtonEnabled = false;
  }

  private getModifiedInvoicesSource(): void {
    const userToken = this.oAuthService.getAccessToken();
    this.iframeSourceUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(
      `${SELLERS_MODIFIED_INVOICES_API_ROUTE}/reports?type=invoice&token=${userToken}`,
    );
  }
}
