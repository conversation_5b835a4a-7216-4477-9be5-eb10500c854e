import { Field, Name } from 'serialize-ts/dist';
import { Money } from '~shared/model';

export class Organization {
  @Field()
  @Name('organizationId')
  id: string;

  @Field()
  @Name('organizationNumber')
  // eslint-disable-next-line id-blacklist
  number: string;

  @Field()
  @Name('organizationName')
  name: string;

  @Field()
  total: Money;

  @Field()
  vat: Money;

  @Field()
  net: Money;

  @Field()
  fee: Money;
}
