import { createAction, props } from '@ngrx/store';
import { InvoiceGeneratorModel, SellerInvoice } from '@accounting-app/modules/seller-invoices/models';
import { ErrorResponse } from '~shared/model';

export const createInvoice = createAction(
  '[Seller Invoices] Invoice create',
  props<{ invoiceGenerator: InvoiceGeneratorModel }>(),
);

export const createInvoiceSuccess = createAction(
  '[Seller Invoices] Invoice create success',
  props<{ invoice: SellerInvoice }>(),
);

export const createInvoiceFailure = createAction(
  '[Seller Invoices] Invoice create failure',
  props<{ error: ErrorResponse }>(),
);
