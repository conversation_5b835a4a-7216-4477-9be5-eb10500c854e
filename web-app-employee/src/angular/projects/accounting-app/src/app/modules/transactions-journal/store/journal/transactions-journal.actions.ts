import { Action } from '@ngrx/store';
import { TransactionJournal } from '@accounting-app/modules/transactions-journal/models';
import { ErrorResponse, PaginatedResponse, RequestParams } from '~shared/model';

export enum TransactionsJournalActionTypes {
  LOAD_JOURNAL = '[Transactions journal] Transactions journal load',
  LOAD_JOURNAL_SUCCESS = '[Transactions journal] Transactions journal load success',
  LOAD_JOURNAL_FAILURE = '[Transactions journal] Transactions journal load failure',
}

export class LoadTransactionsJournal implements Action {
  readonly type = TransactionsJournalActionTypes.LOAD_JOURNAL;

  constructor(public payload: { params: RequestParams }) {}
}

export class LoadTransactionsJournalSuccess implements Action {
  readonly type = TransactionsJournalActionTypes.LOAD_JOURNAL_SUCCESS;

  constructor(public payload: { transactions: PaginatedResponse<TransactionJournal> }) {}
}

export class LoadTransactionsJournalFailure implements Action {
  readonly type = TransactionsJournalActionTypes.LOAD_JOURNAL_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export type TransactionsJournalActionsUnion =
  | LoadTransactionsJournal
  | LoadTransactionsJournalSuccess
  | LoadTransactionsJournalFailure;
