import { createAction, props } from '@ngrx/store';
import { Seller } from '@accounting-app/modules/seller-invoices/models';
import { ErrorResponse } from '~shared/model';

export const searchSellers = createAction('[Seller Invoices] Sellers search', props<{ query: string }>());

export const searchSellersSuccess = createAction(
  '[Seller Invoices] Sellers search success',
  props<{ sellers: Seller[] }>(),
);

export const searchSellersFailure = createAction(
  '[Seller Invoices] Sellers search failure',
  props<{ error: ErrorResponse }>(),
);
