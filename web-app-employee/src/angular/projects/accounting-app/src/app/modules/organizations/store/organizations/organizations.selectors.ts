import { createFeatureSelector, createSelector } from '@ngrx/store';
import { State } from '@accounting-app/modules/organizations/store';
import { ORGANIZATIONS_FEATURE_NAME } from '@accounting-app/modules/organizations/store/feature-name';
import { organizationsAdapter } from '@accounting-app/modules/organizations/store/organizations/organizations.adapter';
import { OrganizationsState } from '@accounting-app/modules/organizations/store/organizations/organizations.state';

const selectOrganizationsFeatureState = createFeatureSelector(ORGANIZATIONS_FEATURE_NAME);

const { selectAll } = organizationsAdapter.getSelectors();

export const selectOrganizationsState = createSelector(
  selectOrganizationsFeatureState,
  (state: State) => state.organizations,
);

export const selectOrganizations = createSelector(selectOrganizationsState, selectAll);

export const selectTotalCount = createSelector(
  selectOrganizationsState,
  (state: OrganizationsState) => state.totalCount,
);

export const selectSummary = createSelector(selectOrganizationsState, (state: OrganizationsState) => state.summary);
