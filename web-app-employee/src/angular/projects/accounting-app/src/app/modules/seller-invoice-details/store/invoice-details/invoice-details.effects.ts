import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { SellerInvoiceDetails } from '@accounting-app/modules/seller-invoice-details/models';
import { SellerInvoiceDetailsService } from '@accounting-app/modules/seller-invoice-details/services/seller-invoice-details.service';
import { ErrorResponse } from '~shared/model';
import { LoadInvoiceDetailsActions } from './actions';

@Injectable()
export class InvoiceDetailsEffects {
  loadInvoiceDetails$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LoadInvoiceDetailsActions.loadInvoiceDetails),
      switchMap(({ id }) =>
        this.invoiceDetailsService.loadInvoiceById(id).pipe(
          map((invoice: SellerInvoiceDetails) => LoadInvoiceDetailsActions.loadInvoiceDetailsSuccess({ invoice })),
          catchError((error: ErrorResponse) => of(LoadInvoiceDetailsActions.loadInvoiceDetailsFailure({ error }))),
        ),
      ),
    ),
  );

  constructor(
    private actions$: Actions,
    private invoiceDetailsService: SellerInvoiceDetailsService
  ) {}
}
