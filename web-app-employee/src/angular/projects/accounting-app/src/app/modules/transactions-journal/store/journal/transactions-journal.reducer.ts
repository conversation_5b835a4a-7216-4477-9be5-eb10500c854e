import {
  TransactionsJournalActionsUnion,
  TransactionsJournalActionTypes,
} from '@accounting-app/modules/transactions-journal/store/journal/transactions-journal.actions';
import { transactionsJournalAdapter } from '@accounting-app/modules/transactions-journal/store/journal/transactions-journal.adapter';
import {
  transactionsJournalInitialState,
  TransactionsJournalState,
} from '@accounting-app/modules/transactions-journal/store/journal/transactions-journal.state';

export function transactionsJournalReducer(
  state = transactionsJournalInitialState,
  action: TransactionsJournalActionsUnion,
): TransactionsJournalState {
  switch (action.type) {
    case TransactionsJournalActionTypes.LOAD_JOURNAL:
      return { ...state, loading: true };
    case TransactionsJournalActionTypes.LOAD_JOURNAL_SUCCESS:
      return transactionsJournalAdapter.setAll(action.payload.transactions.items, {
        ...state,
        loading: false,
        error: null,
        totalCount: action.payload.transactions.totalCount,
      });
    case TransactionsJournalActionTypes.LOAD_JOURNAL_FAILURE:
      return transactionsJournalAdapter.removeAll({
        ...state,
        loading: false,
        error: action.payload.error,
        totalCount: 0,
      });
    default:
      return state;
  }
}
