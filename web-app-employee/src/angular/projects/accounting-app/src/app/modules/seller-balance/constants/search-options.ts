import {
  DropdownSearchSingle,
  DropdownSearchDropdown,
  DropdownSearchDate,
} from '@root/shared/modules/search-bar/models';
import { DropdownInput } from '~shared/model/dropdown-input.model';
import { TransactionTypes } from '@accounting-app/modules/seller-balance/constants/transaction-types';
import { PaymentProviders } from '@accounting-app/modules/seller-balance/constants/payment-providers';

const paymentProviderOptions = [
  new DropdownInput(PaymentProviders.ADYEN, 'Adyen'),
  new DropdownInput(PaymentProviders.PAYPAL, 'Paypal'),
];

const txnTypeOptions = [];

Object.keys(TransactionTypes).forEach(key => {
  txnTypeOptions.push(new DropdownInput(TransactionTypes[key], TransactionTypes[key].split(/(?=[A-Z])/).join(' ')));
});

export const TRANSACTION_SEARCH_OPTIONS = [
  new DropdownSearchDropdown('paymentProvider', 'Payment Provider', paymentProviderOptions),
  new DropdownSearchDropdown('transactionType', 'Transaction Type', txnTypeOptions),
  new DropdownSearchSingle('orderNumber', 'Order Number'),
  new DropdownSearchSingle('pspReference', 'PSP Reference'),
  new DropdownSearchDate('transactionDate', 'Transaction Date', 'transactionStartDate', 'transactionEndDate'),
  new DropdownSearchDate('paidoutDate', 'Paidout Date', 'paidoutStartDate', 'paidoutEndDate'),
  new DropdownSearchDate('accountedAtDate', 'Accounted At Date', 'accountedAtStartDate', 'accountedAtEndDate'),
];
