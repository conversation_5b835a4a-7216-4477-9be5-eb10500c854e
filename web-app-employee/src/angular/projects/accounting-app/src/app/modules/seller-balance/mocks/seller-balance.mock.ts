import * as faker from 'faker';
import { deserialize } from 'serialize-ts';
import {
  SellerBalancePaginatedResponse,
  SellerBalance,
  PaymentBalance,
} from '@accounting-app/modules/seller-balance/models';
import { getFakeAmount } from '~shared/utils/common';

const totalCount = 20;
const currency = 'EUR';
const pspCount = 2;

export const SELLER_BALANCE_MOCK: SellerBalancePaginatedResponse<SellerBalance, PaymentBalance> = {
  items: Array(totalCount)
    .fill({})
    .map(() => {
      return {
        sellerName: faker.random.word(),
        sellerId: faker.random.uuid(),
        sellerFriendlyId: `SA21-${faker.random.number()}`,
        payments: {
          paypalBalance: getFakeAmount(currency),
          paypalPayableBalance: getFakeAmount(currency),
          adyenBalance: getFakeAmount(currency),
          adyenPayableBalance: getFakeAmount(currency),
        },
      };
    })
    .map(e => deserialize(e, SellerBalance)),
  totals: {
    paypalBalance: getFakeAmount(currency),
    paypalPayableBalance: getFakeAmount(currency),
    adyenBalance: getFakeAmount(currency),
    adyenPayableBalance: getFakeAmount(currency),
  },
  totalCount,
};
