import { createAction, props } from '@ngrx/store';
import { Organizations } from '@accounting-app/modules/organizations/models';
import { ErrorResponse, RequestParams } from '~shared/model';

export const loadOrganizations = createAction('[Organizations] Organizations load', props<{ params: RequestParams }>());

export const loadOrganizationsSuccess = createAction(
  '[Organizations] Organizations load success',
  props<{ organizations: Organizations }>(),
);

export const loadOrganizationsFailure = createAction(
  '[Organizations] Organizations load failure',
  props<{ error: ErrorResponse }>(),
);
