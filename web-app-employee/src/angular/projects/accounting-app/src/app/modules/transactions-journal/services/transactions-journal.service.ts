import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { queryBuilder } from '@metromarkets/sdk-17';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { deserialize } from 'serialize-ts/dist';
import { TRANSACTIONS_JOURNAL_API } from '@accounting-app/modules/transactions-journal/constants';
import { TransactionJournal } from '@accounting-app/modules/transactions-journal/models';
import { PaginatedResponse, RequestParams } from '~shared/model';

@Injectable({
  providedIn: 'root',
})
export class TransactionsJournalService {
  constructor(private http: HttpClient) {}

  getTransactions(requestParams: RequestParams): Observable<PaginatedResponse<TransactionJournal>> {
    const params = queryBuilder.toParams(requestParams) as HttpParams;

    return this.http.get<PaginatedResponse<TransactionJournal>>(TRANSACTIONS_JOURNAL_API, { params }).pipe(
      map(({ items, totalCount }) => ({
        items: items.map(i => deserialize(i, TransactionJournal)),
        totalCount,
      })),
    );
  }
}
