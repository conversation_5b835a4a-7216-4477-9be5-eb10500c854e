import { Component, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>ni<PERSON>, ViewChild } from '@angular/core';
import { select, Store } from '@ngrx/store';
import * as fromStore from '@accounting-app/modules/seller-balance/store/seller-transaction';
import { ActivatedRoute, Params } from '@angular/router';
import { Observable, Subject } from 'rxjs';
import { TRANSACTION_SORT_STATE } from '@accounting-app/modules/seller-balance/constants/seller-balance-sort';
import { INIT_QUERY_PARAMS } from '@accounting-app/modules/seller-balance/constants/query-params';
import { PAGE_SIZE_OPTIONS } from '@accounting-app/modules/seller-balance/constants/page-size-options';
import {
  SellerTransactionDisplayedColumns,
  TxnDisplayedColumns,
} from '@accounting-app/modules/seller-balance/constants/transactions-displayed-columns';
import { QueryParamsHandler } from '~shared/services/query-params-handler.service';
import { first, takeUntil } from 'rxjs/operators';
import { SellerIn<PERSON>, SellerTransaction } from '@accounting-app/modules/seller-balance/models';
import { OrderFilter } from '@sales-orders-app/modules/orders/models/order-filter.model';
import { MmTablePaginationComponent } from '@metromarkets/components-17';
import {
  TRANSACTION_FILTER_TITLES,
  TRANSACTION_SEARCH_OPTIONS,
} from '@accounting-app/modules/seller-balance/constants';
import { Money } from '~shared/model';

const ENABLE_PAGE_LINKS = true;
const ENABLE_JUMP_TO_PAGE = true;

@Component({
  selector: 'app-seller-transaction',
  templateUrl: './seller-transaction.component.html',
  styleUrls: ['./seller-transaction.component.scss'],
  providers: [QueryParamsHandler],
})
export class SellerTransactionComponent implements OnInit, OnDestroy {
  isLoading$: Observable<boolean>;
  transactions$: Observable<SellerTransaction[]>;
  totalCount$: Observable<number>;
  totalBalanceWithMM: Money;
  sellerInfo: SellerInfo;
  @ViewChild(MmTablePaginationComponent) paginator: MmTablePaginationComponent;
  public searchQuery$: Observable<string | Params>;
  public sortState = TRANSACTION_SORT_STATE;
  public pageLimit = INIT_QUERY_PARAMS.limit;
  public pageSizeOptions = PAGE_SIZE_OPTIONS;
  public displayedColumnsTitles = SellerTransactionDisplayedColumns;
  public displayedColumns = TxnDisplayedColumns;
  public enableJumpToPage = ENABLE_JUMP_TO_PAGE;
  public enablePageLinks = ENABLE_PAGE_LINKS;
  public searchFilterTitles = TRANSACTION_FILTER_TITLES;
  public searchOptions = TRANSACTION_SEARCH_OPTIONS;
  private unsubscribe$ = new Subject();

  constructor(
    private store: Store<fromStore.SellerTransactionState>,
    private route: ActivatedRoute,
    private paramsHandler: QueryParamsHandler,
  ) {}

  ngOnInit(): void {
    this.isLoading$ = this.store.select(fromStore.isLoading);
    this.transactions$ = this.store.select(fromStore.getSellerTransaction);
    this.totalCount$ = this.store.select(fromStore.getTotalCount);
    this.store
      .pipe(select(fromStore.getSellerInfo), takeUntil(this.unsubscribe$))
      .subscribe((sellerInfo: SellerInfo) => {
        this.sellerInfo = sellerInfo;
      });
    this.store
      .pipe(select(fromStore.getTotalBalanceWithMM), takeUntil(this.unsubscribe$))
      .subscribe((totalBalanceWithMM: Money) => {
        this.totalBalanceWithMM = totalBalanceWithMM;
      });

    this.searchQuery$ = this.store.pipe(select(fromStore.getFilterQuery), first());
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(void 0);
    this.unsubscribe$.complete();
  }

  onSortChange(sortEvent: { mmSortActive: string; mmSortDirection: string }) {
    const { mmSortActive, mmSortDirection } = sortEvent;
    const nextSort = {
      sort: { [`${mmSortActive}`]: mmSortDirection },
      limit: this.pageLimit,
    };
    this.paramsHandler.mergeQueryParams(nextSort);
  }

  changePage(pageEvent): void {
    const { currentPageIndex, pageSizeSelectedOption } = pageEvent;
    this.pageLimit = pageSizeSelectedOption;
    const nextPaging = {
      offset: currentPageIndex * pageSizeSelectedOption,
      limit: pageSizeSelectedOption,
    };
    this.paramsHandler.mergeQueryParams(nextPaging);
  }

  searchByFields(orderFilters: Array<OrderFilter>): void {
    const queryParams = {};
    for (const orderFilter of orderFilters) {
      queryParams[orderFilter.name] = orderFilter.value;
    }

    this.paramsHandler.changeQueryParams(
      {
        offset: 0,
        limit: this.pageLimit,
        filter: queryParams,
      },
      'merge',
    );
  }

  onClearSearchField(field: string): void {
    this.paramsHandler.changeQueryParams(
      {
        offset: 0,
        limit: this.pageLimit,
        filter: { [field]: null },
      },
      'merge',
    );
    this.resetPaginator();
  }

  resetPaginator(): void {
    this.paginator.firstPage();
  }

  downloadTransactions(): void {
    const fileName = `${this.sellerInfo.name.replace(/ /g, '-').toLowerCase()}-${
      new Date().toISOString().split('T')[0]
    }.xlsx`;
    const sellerId = this.route.snapshot.paramMap.get('id');
    this.store.dispatch(new fromStore.DownloadTransactions({ sellerId, fileName }));
  }
}
