import { deserialize } from 'serialize-ts/dist';
import { TRANSACTIONS_JOURNAL_MOCK } from '@accounting-app/modules/transactions-journal/mocks/transactions-journal.mock';
import { TransactionJournal } from '@accounting-app/modules/transactions-journal/models';
import { TransactionsJournalState } from '@accounting-app/modules/transactions-journal/store/journal';
import { StoreUtils } from '~shared/utils/store.utils';

const transactions = TRANSACTIONS_JOURNAL_MOCK.items.map(t => deserialize(t, TransactionJournal));

export const TRANSACTIONS_JOURNAL_STATE_MOCK: TransactionsJournalState = {
  entities: StoreUtils.fromArrayToDictionary<TransactionJournal>(transactions),
  ids: transactions.map(t => t.id),
  error: null,
  loading: false,
  totalCount: TRANSACTIONS_JOURNAL_MOCK.totalCount,
};
