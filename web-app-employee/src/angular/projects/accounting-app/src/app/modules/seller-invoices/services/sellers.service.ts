import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { queryBuilder } from '@metromarkets/sdk-17';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { deserialize } from 'serialize-ts/dist';
import { SELLERS_API_ROUTE } from '@accounting-app/modules/seller-invoices/constants';
import { Seller } from '@accounting-app/modules/seller-invoices/models';
import { PaginatedResponse, RequestParams } from '~shared/model';

@Injectable({
  providedIn: 'root',
})
export class SellersService {
  constructor(private httpClient: HttpClient) {}

  searchSellers(requestParams: RequestParams): Observable<Seller[]> {
    const url = `${SELLERS_API_ROUTE}/search`;
    const params = queryBuilder.toParams(requestParams) as HttpParams;

    return this.httpClient
      .get<PaginatedResponse<Seller>>(url, { params })
      .pipe(map(({ items }) => items.map(i => deserialize(i, Seller))));
  }
}
