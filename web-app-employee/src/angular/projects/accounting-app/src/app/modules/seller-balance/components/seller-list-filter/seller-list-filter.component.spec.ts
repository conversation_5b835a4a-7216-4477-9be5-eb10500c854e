import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SellerListFilterComponent } from './seller-list-filter.component';
import { MatChipsModule } from '@angular/material/chips';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { reducers } from '../../store';
import { MatNativeDateModule } from '@angular/material/core';

describe('SellerListFilterComponent', () => {
  let component: SellerListFilterComponent;
  let fixture: ComponentFixture<SellerListFilterComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SellerListFilterComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [
        MatChipsModule,
        MatDatepickerModule,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
        StoreModule.forFeature('sellerList', reducers.sellerList),
        MatNativeDateModule,
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SellerListFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
