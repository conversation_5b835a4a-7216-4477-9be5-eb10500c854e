import { SELLER_INVOICE_DETAILS_STATE_MOCK } from '@accounting-app/modules/seller-invoice-details/mocks';
import { selectInvoiceDetails } from "@accounting-app/modules/seller-invoice-details/store/invoice-details/invoice-details.selectors";

describe('Invoice Details Selectors', () => {
  describe('selectInvoiceDetails', () => {
    it('should return payload from state', () => {
      const actual = selectInvoiceDetails.projector(SELLER_INVOICE_DETAILS_STATE_MOCK);

      expect(actual).toEqual(SELLER_INVOICE_DETAILS_STATE_MOCK.payload);
    });
  });
});
