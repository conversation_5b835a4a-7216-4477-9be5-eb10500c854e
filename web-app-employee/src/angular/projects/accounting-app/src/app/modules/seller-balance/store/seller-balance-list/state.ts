import { ErrorResponse } from '~shared/model/error-response.model';
import {
  PaymentBalance,
  SellerBalance,
  SellerBalancePaginatedResponse,
} from '@accounting-app/modules/seller-balance/models';

export interface SellerBalanceState {
  payload: SellerBalancePaginatedResponse<SellerBalance, PaymentBalance>;
  loading: boolean;
  error: ErrorResponse;
}

export const sellerBalanceInitialState: SellerBalanceState = {
  payload: {
    items: [],
    totalCount: 0,
    totals: null,
  },
  loading: false,
  error: null,
};
