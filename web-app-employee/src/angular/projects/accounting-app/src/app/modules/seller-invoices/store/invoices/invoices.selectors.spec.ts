import { SELLER_INVOICES_MOCK, SELLER_INVOICES_STATE_MOCK } from '@accounting-app/modules/seller-invoices/mocks';
import { SellerInvoice } from '@accounting-app/modules/seller-invoices/models';
import { selectInvoices, totalCount } from '@accounting-app/modules/seller-invoices/store/invoices/invoices.selectors';
import { PaginatedResponse } from '~shared/model';
import { StoreUtils } from '~shared/utils/store.utils';

describe('Invoices Selectors', () => {
  describe('selectInvoices', () => {
    it('should return invoices array from state', () => {
      const actual = selectInvoices.projector(SELLER_INVOICES_STATE_MOCK);
      const expected = StoreUtils.deserializePaginatedCollection<SellerInvoice>(
        SELLER_INVOICES_MOCK as PaginatedResponse<any>,
        SellerInvoice,
      );

      expect(actual).toEqual(expected);
    });
  });

  describe('totalCount', () => {
    it('should return totalCount from state', () => {
      const actual = totalCount.projector(SELLER_INVOICES_STATE_MOCK);
      const expected = SELLER_INVOICES_MOCK.totalCount;

      expect(actual).toBe(expected);
    });
  });
});
