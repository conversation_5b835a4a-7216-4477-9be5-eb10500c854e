import { Component, OnInit } from '@angular/core';
import { PageEvent } from '@angular/material/paginator';
import { ActivatedRoute } from '@angular/router';
import { select, Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { shareReplay, takeUntil } from 'rxjs/operators';
import { Organization, OrganizationsSummary } from '@accounting-app/modules/organizations/models';
import {
  OrganizationsState,
  selectOrganizations,
  selectSummary,
  selectTotalCount,
} from '@accounting-app/modules/organizations/store/organizations';
import { loadOrganizations } from '@accounting-app/modules/organizations/store/organizations/actions/load-organizations.actions';
import { RequestParams } from '~shared/model';
import { QueryParamsHandler } from '~shared/services/query-params-handler.service';

const DEFAULT_LIMIT = 30;
const DEFAULT_OFFSET = 0;

@Component({
  selector: 'employee-app-organization-list',
  templateUrl: './organization-list.component.html',
  styleUrls: ['./organization-list.component.scss'],
  providers: [QueryParamsHandler],
})
export class OrganizationListComponent implements OnInit {
  public organizations$: Observable<Organization[]>;
  public totalCount$: Observable<number>;
  public summary$: Observable<OrganizationsSummary>;

  public readonly defaultLimit = DEFAULT_LIMIT;
  public readonly displayedColumns = ['id', 'name', 'total', 'VAT', 'NET', 'fee'];

  private readonly defaultOffset = DEFAULT_OFFSET;
  private readonly destroy$ = new Subject<void>();

  constructor(
    private store: Store<OrganizationsState>,
    private route: ActivatedRoute,
    private queryParamsHandler: QueryParamsHandler,
  ) {}

  ngOnInit() {
    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe(this.loadOrganizations.bind(this));

    this.organizations$ = this.store.pipe(select(selectOrganizations));
    this.totalCount$ = this.store.pipe(select(selectTotalCount));
    this.summary$ = this.store.pipe(select(selectSummary), shareReplay({ bufferSize: 1, refCount: true }));
  }

  onPageChange({ pageSize, pageIndex }: PageEvent) {
    this.queryParamsHandler.changeQueryParams({ offset: pageSize * pageIndex, limit: pageSize });
  }

  private loadOrganizations(params: RequestParams): void {
    this.store.dispatch(
      loadOrganizations({
        params: { offset: this.defaultOffset, limit: this.defaultLimit, ...params },
      }),
    );
  }
}
