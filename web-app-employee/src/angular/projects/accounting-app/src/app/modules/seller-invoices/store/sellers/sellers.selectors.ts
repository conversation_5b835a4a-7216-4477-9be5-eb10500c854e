import { createFeatureSelector, createSelector } from '@ngrx/store';
import { State } from '@accounting-app/modules/seller-invoices/store';
import { SELLER_INVOICES_FEATURE_NAME } from '@accounting-app/modules/seller-invoices/store/feature-name';
import { sellersAdapter } from '@accounting-app/modules/seller-invoices/store/sellers/sellers.adapter';

import { ALL_SELLERS_ID } from '../../constants';
import { Seller } from '../../models';

const selectFeatureState = createFeatureSelector(SELLER_INVOICES_FEATURE_NAME);

const { selectAll } = sellersAdapter.getSelectors();

export const selectSellersState = createSelector(selectFeatureState, (state: State) => state.sellers);

export const selectSellers = createSelector(selectSellersState, selectAll);

export const selectSellersWithDefaultOption = createSelector(selectSellers, (sellers: Seller[]) => {
  const allSellersOption = { id: ALL_SELLERS_ID, shopName: 'All Sellers' } as Seller;
  return [allSellersOption, ...sellers];
});
