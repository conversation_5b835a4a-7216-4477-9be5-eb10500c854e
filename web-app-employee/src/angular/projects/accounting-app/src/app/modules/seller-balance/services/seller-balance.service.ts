import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { RequestParams } from '~shared/model';
import { Observable } from 'rxjs';
import { queryBuilder } from '@metromarkets/sdk-17';
import { map } from 'rxjs/operators';
import { deserialize } from 'serialize-ts';
import {
  RESPONSE_TYPE_BLOB,
  SELLER_BALANCE_API,
  SELLER_BALANCE_DOWNLOAD_API,
  SELLER_LIST_API,
  SELLER_TRANSACTIONS_API,
  TRANSACTION_DOWNLOAD_API,
  XLSX_FILE_TYPE,
} from '@accounting-app/modules/seller-balance/constants';
import { downloadFile } from '~shared/utils/download.utils';
import {
  PaymentBalance,
  Seller,
  SellerBalance,
  SellerBalancePaginatedResponse,
  SellerInfo,
  SellerTransaction,
  SellerTransactionPaginatedResponse,
} from '@accounting-app/modules/seller-balance/models';

@Injectable({
  providedIn: 'root',
})
export class SellerBalanceService {
  constructor(private http: HttpClient) {}

  getSellerBalances(
    requestParams: RequestParams,
  ): Observable<SellerBalancePaginatedResponse<SellerBalance, PaymentBalance>> {
    const params = queryBuilder.toParams(requestParams) as HttpParams;
    return this.http
      .get<SellerBalancePaginatedResponse<SellerBalance, PaymentBalance>>(SELLER_BALANCE_API, { params })
      .pipe(
        map(response => ({
          totalCount: response.totalCount,
          items: response.items.map(i => deserialize(i, SellerBalance)),
          totals: deserialize(response.totals, PaymentBalance),
        })),
      );
  }

  getSellerList(requestParams): Observable<Seller[]> {
    return this.http.get<Seller[]>(`${SELLER_LIST_API}/${requestParams.date}`);
  }

  getSellerTransactions(
    sellerId: string,
    requestParams: RequestParams,
  ): Observable<SellerTransactionPaginatedResponse<SellerTransaction, SellerInfo>> {
    const params = queryBuilder.toParams(requestParams) as HttpParams;
    return this.http
      .get<SellerTransactionPaginatedResponse<SellerTransaction, SellerInfo>>(
        `${SELLER_TRANSACTIONS_API}/${sellerId}`,
        { params },
      )
      .pipe(
        map(response => ({
          totalCount: response.totalCount,
          items: response.items.map(i => deserialize(i, SellerTransaction)),
          totalBalanceWithMM: response.totalBalanceWithMM,
          sellerInfo: response.sellerInfo,
        })),
      );
  }

  downloadSellerTransaction(sellerId: string, fileName: string, requestParams: RequestParams) {
    const params = queryBuilder.toParams(requestParams) as HttpParams;
    return this.http.get(`${TRANSACTION_DOWNLOAD_API}/${sellerId}`, { responseType: RESPONSE_TYPE_BLOB, params }).pipe(
      map(response => {
        downloadFile(response, fileName, XLSX_FILE_TYPE);
      }),
    );
  }

  downloadSellerBalance(fileName: string, requestParams: RequestParams, sellerId?: string) {
    if (sellerId) {
      requestParams = {
        ...requestParams,
        filter: {
          ...requestParams.filter,
          sellers: sellerId,
        },
      };
    }
    const params = queryBuilder.toParams(requestParams) as HttpParams;
    return this.http.get(SELLER_BALANCE_DOWNLOAD_API, { responseType: RESPONSE_TYPE_BLOB, params }).pipe(
      map(response => {
        downloadFile(response, fileName, XLSX_FILE_TYPE);
      }),
    );
  }
}
