@import 'node_modules/@metromarkets/components-17/src/theme/settings';

$fontSize: 16px;

:host {
  display: flex;
  justify-content: center;
  padding: 10px;
  flex-grow: 1;
  position: relative;
  font-size: $fontSize;
}

form {
  width: 100%;
}

.section-title {
  background-color: map-get($baseColors, blue-tint-90);
  text-align: center;
  margin: 10px 0;
  padding: 10px;
  border-radius: 3px;
}

.section-content {
  display: flex;
  border: 1px solid map-get($baseColors, disabled-border);
  border-radius: 3px;
  padding: 10px;
  margin: 15px 0;

  button {
    flex-grow: 0;
    align-self: end;
    height: auto;
    padding: 10px 5px;

    a, a:hover {
      color: inherit;
      text-decoration: inherit;
      font-weight: inherit;
    }
  }

  table {
    flex-grow: 1;

    td {
      padding: 10px 5px;
      vertical-align: top;
      min-width: 150px;
    }

    tr td:first-child {
      min-height: 25px;
      color: map_get($baseColors, metro-blue);
    }
  }

  .section-download {
    min-width: 150px;

    button {
      font-weight: normal;
      border-radius: 3px;
      border: 1px solid map-get($baseColors, grey);
    }

    ::ng-deep .mm-button__container {
      display:block;
    }
  }
}

.label-input {
  min-height: 25px;
  color: map_get($baseColors, metro-blue);
}

input {
  width: 100%;
  height: 40px;
  padding: 0 11px;
  color: map_get($baseColors, metro-blue);
  font-weight: 400;
  border-radius: 2px;

  &::placeholder {
    color: map_get($baseColors, grey-tint-40);
    font-style: italic;
  }
}

.section-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;

  button {
    min-height: 50px;
    color: black;
    border: 1px solid map-get($baseColors, disabled-border);
    margin-left: 10px;
    box-shadow: 2px map-get($baseColors, disabled-border);
    border-radius: 2px;
    font-size: 18px;

    & + button {
      color: white;
      background-color: map-get($baseColors, green-shade-20);

      &:hover {
        background-color: map-get($baseColors, green-shade-40);
      }
    }
  }
}

form {
  input {
    margin-top: 10px;
    border: 1px solid map-get($baseColors, grey-tint-80);
    border-radius: 3px;
    padding: 30px 5px;

    &::placeholder {
      font-size: $fontSize;
    }
  }

  &.ng-submitted .ng-invalid {
    border-color: red;
  }

  .mm-error {
    color: red;
  }

  .section-result {
    color: map-get($baseColors, green-tint-20);
    padding: 10px;
    border: 1px solid map-get($baseColors, green-tint-20);
    text-align: center;
    font-weight: bold;

    &.failed {
      color: red;
      border-color: red;
    }
  }
}
