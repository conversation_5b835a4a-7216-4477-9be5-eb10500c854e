@import 'node_modules/@metromarkets/components-17/src/theme/settings';

.dialog-title {
  background-color: map_get($baseColors, blue-tint-90);
  text-align: center;
  padding: 10px;
  font-weight: bold;
  font-size: 16px;
}

.dialog-content {
  padding: 20px;
}

.dialog-alert {
  background-color: map_get($baseColors, red-tint-80);
  padding: 10px;
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 10px;

  &.yellow {
    background-color: map_get($baseColors, yellow-tint-80);
  }
}

.cancellation-info {
  font-size: 13px;
  margin-bottom: 10px;

  ul {
    margin: 0;
    padding-inline-start: 15px;
  }
}

mm-label,
label,
::ng-deep label {
  font-size: 13px;
  margin-bottom: 10px;
  font-weight: bold;
}

.mm-textarea {
  height: 120px;
}

.error-message {
  color: map-get($baseColors, warning);
  font-size: 13px;
  font-weight: bold;
  margin-top: -2px;
}

.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  button {
    color: black;
    border: 1px solid map-get($baseColors, disabled-border);
    margin-left: 10px;
    border-shadow: 2px solid map-get($baseColors, disabled-border);
    border-radius: 2px;

    & + button {
      color: white;
      background-color: map-get($baseColors, red);

      &:hover {
        background-color: map-get($baseColors, red-tint-20);
      }
    }
  }
}
