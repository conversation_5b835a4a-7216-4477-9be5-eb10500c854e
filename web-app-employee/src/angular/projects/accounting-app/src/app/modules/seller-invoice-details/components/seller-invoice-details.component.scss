$h-space: 20px;
$v-space: 10px;

.invoice {

  .payment-info-wrapper {
    display: flex;
    flex-direction: column;
    gap: 5px;

    &.with-margin {
      margin-top: 10px;
    }
  }

  position: relative;
  display: flex;
  flex-direction: column;

  &__back-button, &__heading {
    padding: $h-space 20px;
  }

  &__heading {
    display: flex;
    justify-content: space-between;

    h2 {
      font-size: 19px;
      font-weight: bold;
    }
  }

  .invoice-details {
    button {
      margin-bottom: $v-space;
      margin-left: $h-space;

      & + button {
        margin-left: 10px;
      }
    }

    table {
      width: 100%;
      border-spacing: 0;

      h4 {
        display: inline-block;
        margin-right: 5px;
        font-size: 1rem;
      }
    }

    th, td {
      text-align: left;
      padding: $v-space $h-space;
      vertical-align: top;
    }

    thead tr th {
      font-weight: bold;
      border-top: 2px solid #002d72;
      border-bottom: 2px solid #002d72;
    }
  }

  &__alert-container {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
  }

  info-icon-tooltip {
    margin-left: 5px;
  }
}

::ng-deep .mm-tooltip ul {
  margin-top: 0;
  padding-inline-start: 15px;
}
