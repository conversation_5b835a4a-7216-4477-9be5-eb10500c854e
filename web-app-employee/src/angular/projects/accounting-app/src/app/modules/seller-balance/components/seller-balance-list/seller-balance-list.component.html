<div class="seller-balance">
  <div class="seller-balance__header">
    <h2>{{ 'SELLER_BALANCE.TITLE.SELLER_BALANCE_OVERVIEW' | translate }}</h2>
  </div>
  <app-seller-list-filter
    (sellerSelection)="sellersSelected($event)"
    (chipRemoved)="chipRemoved()"
    (filterByDate)="filterByDate($event)"
    [filterDate]="filterDate$ | async"
  ></app-seller-list-filter>

  <mm-table-loader *ngIf="isSellerBalanceListLoading$ | async"></mm-table-loader>
  <table mm-table [dataSource]="sellerBalances$ | async" class="table seller-balance-table mm-elevation-z8">
    <ng-container mmColumnDef="sellerName">
      <th mmHeaderCell *mmHeaderCellDef class="seller-balance__header-cell align-left">
        {{ displayedColumnsTitles.sellerName }}
      </th>
      <td mmCell *mmCellDef="let element" mmTextAlign="left" class="seller-balance__cell seller-name">
        <span mm-button (click)="navigateToDetails(element.sellerId)" class="seller-name-link">
          {{ element.sellerName }}
        </span>
      </td>
      <td mat-footer-cell mmTextAlign="left" *matFooterCellDef class="seller-balance__cell seller-balance__footer-cell">
        Grand Total ( {{ totalCount$ | async }} Sellers )
      </td>
    </ng-container>
    <ng-container mmColumnDef="sellerFriendlyId">
      <th mmHeaderCell *mmHeaderCellDef mmTextAlign="left">
        {{ displayedColumnsTitles.sellerFriendlyId }}
      </th>
      <td mmCell *mmCellDef="let element" mmTextAlign="left" class="seller-balance__cell">
        {{ element.sellerFriendlyId }}
      </td>
      <td
        mat-footer-cell
        mmTextAlign="right"
        *matFooterCellDef
        class="seller-balance__cell seller-balance__footer-cell"
      ></td>
    </ng-container>
    <ng-container mmColumnDef="paypalBalance">
      <th mmHeaderCell *mmHeaderCellDef class="seller-balance__header-cell" mmTextAlign="right">
        {{ displayedColumnsTitles.paypalBalance }}
      </th>
      <td mmCell *mmCellDef="let element" mmTextAlign="right" class="seller-balance__cell">
        {{ element.paypalBalance.amount | currency: element.paypalBalance.currency }}
      </td>
      <td
        mat-footer-cell
        mmTextAlign="right"
        *matFooterCellDef="let element"
        class="seller-balance__cell seller-balance__footer-cell align-right"
      >
        {{ totalBalance?.paypalBalance?.amount | currency: totalBalance?.paypalBalance?.currency }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="paypalPayableBalance">
      <th mmHeaderCell *mmHeaderCellDef class="seller-balance__header-cell" mmTextAlign="right">
        {{ displayedColumnsTitles.paypalPayableBalance }}
      </th>
      <td mmCell *mmCellDef="let element" mmTextAlign="right" class="seller-balance__cell">
        {{ element.paypalPayableBalance.amount | currency: element.paypalPayableBalance.currency }}
      </td>
      <td
        mat-footer-cell
        mmTextAlign="right"
        *matFooterCellDef="let element"
        class="seller-balance__cell seller-balance__footer-cell align-right"
      >
        {{ totalBalance?.paypalPayableBalance?.amount | currency: totalBalance?.paypalPayableBalance?.currency }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="adyenBalance">
      <th mmHeaderCell *mmHeaderCellDef class="seller-balance__header-cell" mmTextAlign="right">
        {{ displayedColumnsTitles.adyenBalance }}
      </th>
      <td mmCell *mmCellDef="let element" mmTextAlign="right" class="seller-balance__cell">
        {{ element.adyenBalance.amount | currency: element.adyenBalance.currency }}
      </td>
      <td
        mat-footer-cell
        mmTextAlign="right"
        *matFooterCellDef="let element"
        class="seller-balance__cell seller-balance__footer-cell align-right"
      >
        {{ totalBalance?.adyenBalance?.amount | currency: totalBalance?.adyenBalance?.currency }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="adyenPayableBalance">
      <th mmHeaderCell *mmHeaderCellDef class="seller-balance__header-cell" mmTextAlign="right">
        {{ displayedColumnsTitles.adyenPayableBalance }}
      </th>
      <td mmCell *mmCellDef="let element" mmTextAlign="right" class="seller-balance__cell">
        {{ element.adyenPayableBalance.amount | currency: element.adyenPayableBalance.currency }}
      </td>
      <td
        mat-footer-cell
        mmTextAlign="right"
        *matFooterCellDef="let element"
        class="seller-balance__cell seller-balance__footer-cell align-right"
      >
        {{ totalBalance?.adyenPayableBalance?.amount | currency: totalBalance?.adyenPayableBalance?.currency }}
      </td>
    </ng-container>
    <tr mm-header-row *mmHeaderRowDef="displayedColumns"></tr>
    <tr mm-row *mmRowDef="let row; columns: displayedColumns" class="element-row"></tr>
    <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
  </table>
  <mm-table-pagination
    #paginator
    (page)="changePage($event)"
    [itemsPerPageLabel]="'PAGINATOR.SELLERS_PER_PAGE' | translate"
    [pageSizeOptions]="pageSizeOptions"
    [pageSizeSelectedOption]="pageLimit"
    [pageSizeTotal]="totalCount$ | async"
    [rangeLabel]="'PAGINATOR.OF_RANGE' | translate"
    [enableJumpToPage]="enableJumpToPage"
    [enablePageLinks]="enablePageLinks"
  >
  </mm-table-pagination>
</div>
