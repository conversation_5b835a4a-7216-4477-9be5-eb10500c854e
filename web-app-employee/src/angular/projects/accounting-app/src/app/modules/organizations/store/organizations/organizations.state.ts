import { EntityState } from '@ngrx/entity';
import { Organization, OrganizationsSummary } from '@accounting-app/modules/organizations/models';
import { organizationsAdapter } from '@accounting-app/modules/organizations/store/organizations/organizations.adapter';
import { ErrorResponse } from '~shared/model';

export interface OrganizationsState extends EntityState<Organization> {
  error: ErrorResponse;
  totalCount: number;
  summary: OrganizationsSummary;
}

export const organizationsInitialState: OrganizationsState = organizationsAdapter.getInitialState({
  error: null,
  totalCount: 0,
  summary: null,
});
