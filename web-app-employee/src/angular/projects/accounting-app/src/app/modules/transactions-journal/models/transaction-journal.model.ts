import { Field, Name } from 'serialize-ts/dist';
import { TransactionTypes } from '@accounting-app/modules/transactions-journal/models/transaction-types.enum';

export class TransactionJournal {
  @Field()
  @Name('transactionId')
  id: string;

  @Field()
  type: TransactionTypes;

  @Field()
  @Name('balance')
  amount: string;

  @Field()
  currency: string;

  @Field()
  description: string;

  @Field()
  fromAccountName: string;

  @Field()
  toAccountName: string;

  @Field()
  createdAt: string;
}
