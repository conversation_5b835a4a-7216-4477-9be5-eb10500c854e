import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { urlBuilder } from '@metromarkets/sdk-17';
import { select, Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { filter, take } from 'rxjs/operators';
import { Period } from '~shared/model';
import { ALL_SELLERS_ID } from '../../constants';
import { Seller, SellerInvoiceStatesEnum } from '../../models';
import * as fromInvoices from '../../store/invoices';
import * as fromSellers from '../../store/sellers';
import { searchSellers } from '../../store/sellers/actions/search-sellers.actions';

@Component({
  selector: 'employee-app-invoices-filter',
  templateUrl: './invoices-filter.component.html',
  styleUrls: ['./invoices-filter.component.scss'],
})
export class InvoicesFilterComponent implements OnInit {
  @Output() filtered = new EventEmitter<{ [key: string]: any }>();

  public sellers$: Observable<Seller[]>;
  public selectedStatus$: Observable<SellerInvoiceStatesEnum>;
  public periodControl: UntypedFormControl;

  public readonly invoiceStatuses: string[];
  public readonly defaultStatusesValue = 'All Statuses';

  constructor(private store: Store<fromSellers.SellersState | fromInvoices.InvoicesState>) {
    this.invoiceStatuses = [this.defaultStatusesValue, ...Object.keys(SellerInvoiceStatesEnum)];
  }

  ngOnInit() {
    this.searchSellers('');

    this.sellers$ = this.store.pipe(select(fromSellers.selectSellersWithDefaultOption));
    this.selectedStatus$ = this.store.pipe(select(fromInvoices.selectStateFromUrl));
    this.store
      .pipe(
        select(fromInvoices.selectPeriodFromUrl),
        filter(period => !!period),
        take(1),
      )
      .subscribe(period => (this.periodControl = new UntypedFormControl(period)));
  }

  searchSellers(query: string): void {
    this.store.dispatch(searchSellers({ query }));
  }

  filterBySeller(seller: Seller) {
    const sellerId = seller.id !== ALL_SELLERS_ID ? seller.id : undefined;
    const encodedFilter = urlBuilder.encode({ seller: sellerId });
    this.filtered.emit({ filter: encodedFilter });
  }

  filterByStatus(status: SellerInvoiceStatesEnum): void {
    const encodedFilter = urlBuilder.encode({ state: SellerInvoiceStatesEnum[status] });
    this.filtered.emit({ filter: encodedFilter });
  }

  filterByPeriod(period: Period) {
    const encodedFilter = urlBuilder.encode({ period: { ...period } });
    this.filtered.emit({ filter: encodedFilter });
  }
}
