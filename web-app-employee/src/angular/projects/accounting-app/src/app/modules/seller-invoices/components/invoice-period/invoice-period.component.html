<!--suppress HtmlFormInputWithoutLabel -->
<input
  mmInput
  placeholder="Period"
  [matDatepicker]="periodPicker"
  (click)="periodPicker.open()"
  (blur)="onTouched()"
  readonly="readonly"
  [max]="maxDate"
  [formControl]="periodControl"
  (dateChange)="onDateChange($event)"
  [mmTestTarget]="'invoices-period'"
/>
<mat-datepicker [startView]="'multi-year'" #periodPicker (monthSelected)="onMonthSelect($event)"></mat-datepicker>
