import * as faker from 'faker';
import { SellerInvoiceStatesEnum } from '@accounting-app/modules/seller-invoices/models';

const totalCount = 5;

export const SELLER_INVOICES_MOCK = {
  items: Array(totalCount)
    .fill({})
    .map(() => ({
      id: faker.random.uuid(),
      sellerId: faker.random.uuid(),
      invoiceNumber: faker.random.alphaNumeric(),
      invoiceState: SellerInvoiceStatesEnum.Pending,
      status: SellerInvoiceStatesEnum.Pending,
      from: new Date().toISOString(),
      to: new Date().toISOString(),
      total: {
        amount: faker.finance.amount(),
        currency: faker.finance.currencyCode(),
      },
      netAmount: {
        amount: faker.finance.amount(),
        currency: faker.finance.currencyCode(),
      },
      grossAmount: {
        amount: faker.finance.amount(),
        currency: faker.finance.currencyCode(),
      },
      vatAmount: {
        amount: faker.finance.amount(),
        currency: faker.finance.currencyCode(),
      },
      vatRate: '0',
      relatedPeriod: 'Feb 2025',
      paymentStatus: 'Paid',
      month: 'February',
      organizationId: faker.random.uuid(),
      createdAt: new Date().toISOString(),
      shopName: faker.random.word(),
      sellerNumber: faker.finance.accountName(),
      downloadLink: faker.internet.url(),
      address: 'Address',
      taxId: 'taxId',
      correctionReportId: '1',
      correctionReportName: 'MF-19-XXX',
      correctionReportUrl: 'aaa',
      correctionForReportId: '1',
      membershipFee: {
        id: faker.random.uuid(),
        status: 'Paid externally',
        cancelledBy: 'test.user',
        cancelledAt: '10.02.2024 10:55',
        cancellationReason: 'test reason',
        deletedAt: '10.02.2024 10:55',
        refundedAt: '10.02.2024 10:55',
        refundedPsp: '123456',
      }
    })),
  totalCount,
};
