import {
  SellerListActionTypes,
  SellerListActionUnion,
} from '@accounting-app/modules/seller-balance/store/seller-list/actions';
import {
  sellerListInitialState,
  SellerListState,
} from '@accounting-app/modules/seller-balance/store/seller-list/state';

export function sellerListReducer(state = sellerListInitialState, action: SellerListActionUnion): SellerListState {
  switch (action.type) {
    case SellerListActionTypes.LOAD_LIST:
      return { ...state };

    case SellerListActionTypes.LOAD_LIST_SUCCESS:
      return {
        ...state,
        sellers: [...action.payload.sellers],
      };

    case SellerListActionTypes.LOAD_LIST_FAILURE:
      return {
        ...state,
        sellers: [],
        error: action.payload.error,
      };

    case SellerListActionTypes.RESET_SELLER_SELECTION:
      return {
        ...state,
        sellers: [...state.sellers.map(seller => ({ ...seller, selected: false }))],
      };
    case SellerListActionTypes.UPDATE_SELLER_SELECTION:
      return {
        ...state,
        sellers: [
          ...state.sellers.map(seller => ({ ...seller, selected: action.payload.sellerIds.includes(seller.id) })),
        ],
      };

    default:
      return state;
  }
}
