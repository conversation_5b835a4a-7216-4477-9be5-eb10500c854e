import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { deserialize } from 'serialize-ts/dist';
import { SELLERS_MODIFIED_INVOICES_API_ROUTE } from '@accounting-app/modules/seller-invoice-details/constants';
import { SellerInvoiceDetails } from '@accounting-app/modules/seller-invoice-details/models';
import { OAuthService } from "angular-oauth2-oidc";

@Injectable({
  providedIn: 'root',
})
export class SellerInvoiceDetailsService {
  constructor(
    private httpClient: HttpClient,
    private oAuthService: OAuthService
  ) {}

  loadInvoiceById(id: string): Observable<SellerInvoiceDetails> {
    const headers = new HttpHeaders({
      Authorization: `Bearer ${this.oAuthService.getAccessToken()}`,
      'Content-Type': 'application/json',
      Accept: 'application/json',
    });

    return this.httpClient
      .get<SellerInvoiceDetails>(`${SELLERS_MODIFIED_INVOICES_API_ROUTE}/${id}`, { headers })
      .pipe(map(invoice => deserialize(invoice, SellerInvoiceDetails)));
  }
}
