import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { ROUTER_NAVIGATED, RouterNavigatedAction } from '@ngrx/router-store';
import { Action } from '@ngrx/store';
import { get, isEmpty } from 'lodash';
import { Observable, of } from 'rxjs';
import { catchError, filter, map, switchMap } from 'rxjs/operators';
import { RouterStateUrl } from '~core/store';
import { ACCOUNTING_ROUTE, SELLER_BALANCE_ROUTE } from '~shared/constants';
import { isUrlMatchRoute } from '~shared/utils/common';

import { SellerBalanceService } from '@accounting-app/modules/seller-balance/services/seller-balance.service';
import { formatDate } from '@angular/common';
import { DATE_FORMAT } from '@accounting-app/modules/seller-balance/constants/date-format';
import {
  LoadSellerList,
  LoadSellerListFailure,
  LoadSellerListSuccess,
  SellerListActionTypes,
} from '@accounting-app/modules/seller-balance/store/seller-list/actions';
import { ActivatedRoute } from '@angular/router';
import { SELLERS_FILTER_QUERY_PARAM } from '@accounting-app/modules/seller-balance/constants/seller-list';

@Injectable()
export class SellerListEffects {
  private dateParam = `${formatDate(new Date(), DATE_FORMAT, this.locale)}`;
  private previousDate: string;

  loadSellerList$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(SellerListActionTypes.LOAD_LIST),
      map((action: LoadSellerList) => action.payload),
      switchMap(({ date }) => {
        return this.httpService.getSellerList({ date }).pipe(
          map(sellerList => {
            this.previousDate = date;
            const selectedIDs = (this.route.snapshot.queryParamMap.get(SELLERS_FILTER_QUERY_PARAM) || '').split(',');
            return new LoadSellerListSuccess({
              sellers: sellerList.map(seller => ({ ...seller, selected: selectedIDs.indexOf(seller.id) > -1 })),
            });
          }),
          catchError(error => of(new LoadSellerListFailure({ error }))),
        );
      }),
    ),
  );

  loadSellerListByRouter$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(ROUTER_NAVIGATED),
      map((action: RouterNavigatedAction) => get(action, 'payload.routerState', {})),
      map(({ url, queryParams }: RouterStateUrl) => ({ url, queryParams })),
      filter(({ url }) => isUrlMatchRoute(url, `${ACCOUNTING_ROUTE}/${SELLER_BALANCE_ROUTE}`)),
      map(({ queryParams }) => {
        if (!isEmpty(queryParams.filter) && !isEmpty(queryParams.filter.date)) {
          return { date: queryParams.filter.date };
        }
        return { date: this.dateParam };
      }),
      filter(date => date.date !== this.previousDate),
      map(date => new LoadSellerList({ ...date })),
    ),
  );

  constructor(
    private actions$: Actions,
    private httpService: SellerBalanceService,
    @Inject(LOCALE_ID) private locale: string,
    private route: ActivatedRoute,
  ) {}
}
