import { Component, Inject, LOCALE_ID, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import {
  DATE_FILTER_QUERY_PARAM,
  DisplayedColumns,
  SellerBalanceDisplayedColumns,
} from '@accounting-app/modules/seller-balance/constants';
import { select, Store } from '@ngrx/store';
import * as fromSellerBalance from '@accounting-app/modules/seller-balance/store/seller-balance-list';
import { Observable, Subject } from 'rxjs';
import { QueryParamsHandler } from '~shared/services/query-params-handler.service';
import { INIT_QUERY_PARAMS } from '@accounting-app/modules/seller-balance/constants/query-params';
import { takeUntil } from 'rxjs/operators';
import { BALANCE_SORT_STATE } from '@accounting-app/modules/seller-balance/constants/seller-balance-sort';
import dayjs from 'dayjs';
import { ActivatedRoute, Router } from '@angular/router';
import { MmTablePaginationComponent } from '@metromarkets/components-17';
import { FILTER_DATE_FORMAT } from '@accounting-app/modules/seller-balance/constants/date-format';
import { QUERY_PARAM_SELLERS } from '@accounting-app/modules/seller-balance/constants/seller-list';
import { PAGE_SIZE_OPTIONS } from '@accounting-app/modules/seller-balance/constants/page-size-options';
import { parseDate } from '~shared/utils/date.utils';
import { PaymentBalance, SellerBalance, SellerListFilterResult } from '@accounting-app/modules/seller-balance/models';

const ENABLE_PAGE_LINKS = true;
const ENABLE_JUMP_TO_PAGE = true;

@Component({
  selector: 'app-seller-balance-list',
  templateUrl: './seller-balance-list.component.html',
  styleUrls: ['./seller-balance-list.component.scss'],
  providers: [QueryParamsHandler],
})
export class SellerBalanceListComponent implements OnInit, OnDestroy {
  public displayedColumnsTitles = SellerBalanceDisplayedColumns;
  public displayedColumns = DisplayedColumns;
  public isSellerBalanceListLoading$: Observable<boolean>;
  public sellerBalances$: Observable<SellerBalance[]>;
  public filterDate$: Observable<string>;
  public totalBalance: PaymentBalance;
  public pageLimit = INIT_QUERY_PARAMS.limit;
  public pageSizeOptions = PAGE_SIZE_OPTIONS;
  public totalCount$: Observable<number>;
  public enableJumpToPage = ENABLE_JUMP_TO_PAGE;
  public enablePageLinks = ENABLE_PAGE_LINKS;
  public sortState = BALANCE_SORT_STATE;
  public selectedSellersCount: number;
  @ViewChild('paginator') paginator: MmTablePaginationComponent;
  private unsubscribe$ = new Subject();

  constructor(
    private store: Store<fromSellerBalance.SellerBalanceState>,
    private paramsHandler: QueryParamsHandler,
    private route: ActivatedRoute,
    private router: Router,
    @Inject(LOCALE_ID) private locale: string,
  ) {}

  ngOnInit(): void {
    this.isSellerBalanceListLoading$ = this.store.pipe(select(fromSellerBalance.isSellerBalanceListLoading));
    this.sellerBalances$ = this.store.pipe(select(fromSellerBalance.getSellerBalance));
    this.totalCount$ = this.store.pipe(select(fromSellerBalance.getTotalCount));
    this.store
      .pipe(select(fromSellerBalance.getTotals), takeUntil(this.unsubscribe$))
      .subscribe((totals: PaymentBalance) => {
        this.totalBalance = totals;
      });
    this.filterDate$ = this.store.pipe(select(fromSellerBalance.getDateFromUrl));
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(void 0);
    this.unsubscribe$.complete();
  }

  changePage(pageEvent): void {
    const { currentPageIndex, pageSizeSelectedOption } = pageEvent;
    this.pageLimit = pageSizeSelectedOption;
    const nextPaging = {
      offset: currentPageIndex * pageSizeSelectedOption,
      limit: pageSizeSelectedOption,
    };
    this.paramsHandler.mergeQueryParams(nextPaging);
  }

  onSortChange(sortEvent: { mmSortActive: string; mmSortDirection: string }) {
    const { mmSortActive, mmSortDirection } = sortEvent;
    const nextSort = {
      sort: { [`${mmSortActive}`]: mmSortDirection },
      limit: this.pageLimit,
    };
    this.paramsHandler.mergeQueryParams(nextSort);
  }

  filterByDate(filterResult: SellerListFilterResult) {
    const requestDateFrom = dayjs(filterResult.date).format(FILTER_DATE_FORMAT);
    const { queryParams } = this.route.snapshot;
    this.paramsHandler.changeQueryParams(
      {
        ...queryParams,
        filter: { [filterResult.name]: requestDateFrom, [QUERY_PARAM_SELLERS]: '' },
      },
      'merge',
    );
    this.paginator.firstPage();
  }

  sellersSelected(selectedIDs: string[]): void {
    const { queryParams } = this.route.snapshot;
    this.paramsHandler.changeQueryParams(
      {
        ...queryParams,
        filter: { ...queryParams.filter, [QUERY_PARAM_SELLERS]: selectedIDs.join() },
      },
      'merge',
    );
    this.paginator.firstPage();
  }

  getSelectedSellerCount(count: number) {
    this.selectedSellersCount = count;
  }

  chipRemoved() {
    const { queryParams } = this.route.snapshot;
    this.paramsHandler.changeQueryParams(
      {
        ...queryParams,
        filter: { ...queryParams.filter, [QUERY_PARAM_SELLERS]: '' },
      },
      'merge',
    );
    this.paginator.firstPage();
  }

  navigateToDetails(id: string) {
    this.router.navigate(['.', id], { relativeTo: this.route });
  }

  downloadSellerBalance(sellerId?: string, sellerName?: string): void {
    const date =
      this.route.snapshot.queryParamMap.get(DATE_FILTER_QUERY_PARAM) || parseDate(new Date().toDateString(), 'Y-M-D');
    let fileName = `seller-balance-${date}.xlsx`;
    if (sellerId && sellerName) {
      fileName = `${sellerName.replace(/ /g, '-').toLowerCase()}-${date}.xlsx`;
    }
    this.store.dispatch(new fromSellerBalance.DownloadSellerBalance({ sellerId, fileName }));
  }
}
