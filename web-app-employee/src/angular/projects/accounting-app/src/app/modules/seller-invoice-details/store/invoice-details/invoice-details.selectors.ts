import { createFeatureSelector, createSelector } from '@ngrx/store';
import { SELLER_INVOICE_DETAILS_FEATURE_NAME } from '@accounting-app/modules/seller-invoice-details/store/feature-name';
import { InvoiceDetailsState } from '@accounting-app/modules/seller-invoice-details/store/invoice-details/invoice-details.state';
import { State } from '@accounting-app/modules/seller-invoice-details/store/state';

const featureModuleState = createFeatureSelector(SELLER_INVOICE_DETAILS_FEATURE_NAME);

export const selectInvoiceDetailsState = createSelector(featureModuleState, (state: State) => state.invoiceDetails);

export const selectInvoiceDetails = createSelector(
  selectInvoiceDetailsState,
  (state: InvoiceDetailsState) => state.payload,
);
