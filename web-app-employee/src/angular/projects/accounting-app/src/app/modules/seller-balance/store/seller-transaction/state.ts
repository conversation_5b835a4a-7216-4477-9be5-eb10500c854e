import { ErrorResponse } from '~shared/model';
import {
  SellerTransactionPaginatedResponse,
  SellerInfo,
  SellerTransaction,
} from '@accounting-app/modules/seller-balance/models';

export interface SellerTransactionState {
  payload: SellerTransactionPaginatedResponse<SellerTransaction, SellerInfo>;
  loading: boolean;
  error: ErrorResponse;
}

export const sellerTransactionInitialState = {
  payload: {
    items: [],
    totalCount: 0,
    totalBalanceWithMM: null,
    sellerInfo: null,
  },
  loading: false,
  error: null,
};
