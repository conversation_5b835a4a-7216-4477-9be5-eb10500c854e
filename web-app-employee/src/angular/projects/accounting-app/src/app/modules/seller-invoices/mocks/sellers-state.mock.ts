import { SELLERS_MOCK } from '@accounting-app/modules/seller-invoices/mocks/sellers.mock';
import { Seller } from '@accounting-app/modules/seller-invoices/models';
import { SellersState } from '@accounting-app/modules/seller-invoices/store/sellers';
import { PaginatedResponse } from '~shared/model';
import { StoreUtils } from '~shared/utils/store.utils';

const sellers = StoreUtils.deserializePaginatedCollection<Seller>(SELLERS_MOCK as PaginatedResponse<any>, Seller);

export const SELLERS_STATE_MOCK: SellersState = {
  entities: StoreUtils.fromArrayToDictionary<Seller>(sellers),
  ids: StoreUtils.getIdsFromArray<Seller>(sellers),
  error: null,
};
