import { Action, createReducer, on } from '@ngrx/store';
import { LoadInvoiceDetailsActions } from './actions';
import { invoiceDetailsInitialState, InvoiceDetailsState } from '@accounting-app/modules/seller-invoice-details/store/invoice-details/invoice-details.state';

const reducer = createReducer(
  invoiceDetailsInitialState,
  on(LoadInvoiceDetailsActions.loadInvoiceDetailsSuccess, (state, { invoice }) => ({
    ...state,
    loading: false,
    payload: invoice
  })),
  on(LoadInvoiceDetailsActions.loadInvoiceDetailsFailure, (state, { error }) => ({
    ...state,
    loading: false,
    payload: null,
    error,
  })),
);

export function invoiceDetailsReducer(state: InvoiceDetailsState, action: Action) {
  return reducer(state, action);
}
