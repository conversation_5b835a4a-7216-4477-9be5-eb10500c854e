import { EntityState } from '@ngrx/entity';
import { Seller } from '@accounting-app/modules/seller-invoices/models';
import { sellersAdapter } from '@accounting-app/modules/seller-invoices/store/sellers/sellers.adapter';
import { ErrorResponse } from '~shared/model';

export interface SellersState extends EntityState<Seller> {
  error: ErrorResponse;
}

export const sellersInitialState: SellersState = sellersAdapter.getInitialState({
  error: null,
});
