import { TestBed, inject } from '@angular/core/testing';

import { SellerBalanceService } from './seller-balance.service';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { SELLER_BALANCE_API, SELLER_TRANSACTIONS_API } from '../constants';
import { SELLER_BALANCE_MOCK } from '../mocks/seller-balance.mock';
import { SELLER_TRANSACTION_MOCK } from '../mocks/seller-transaction.mock';
import * as faker from 'faker';

describe('SellerBalanceService', () => {
  let sellerBalanceService: SellerBalanceService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
    });
    sellerBalanceService = TestBed.inject(SellerBalanceService);
  });

  it('should be created', () => {
    expect(sellerBalanceService).toBeTruthy();
  });

  it('should get Seller balances', inject(
    [HttpTestingController, SellerBalanceService],
    (httpMock: HttpTestingController, service: SellerBalanceService) => {
      service.getSellerBalances({}).subscribe(data => {
        expect(data.totalCount).toBe(20);
      });
      const req = httpMock.expectOne(SELLER_BALANCE_API);
      expect(req.request.method).toEqual('GET');
      req.flush(SELLER_BALANCE_MOCK);
    },
  ));

  it('should get Seller transactions', inject(
    [HttpTestingController, SellerBalanceService],
    (httpMock: HttpTestingController, service: SellerBalanceService) => {
      const uuid = faker.random.uuid();
      service.getSellerTransactions(uuid, {}).subscribe(data => {
        expect(data.totalCount).toBe(20);
      });
      const req = httpMock.expectOne(`${SELLER_TRANSACTIONS_API}/${uuid}`);
      expect(req.request.method).toEqual('GET');
      req.flush(SELLER_TRANSACTION_MOCK);
    },
  ));
});
