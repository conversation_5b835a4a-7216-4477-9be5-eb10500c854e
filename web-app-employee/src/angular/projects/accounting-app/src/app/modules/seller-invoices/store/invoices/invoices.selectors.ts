import { Params } from '@angular/router';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import * as fromRouter from '~core/store';
import { SELLER_INVOICES_FEATURE_NAME } from '@accounting-app/modules/seller-invoices/store/feature-name';
import { invoicesAdapter } from '@accounting-app/modules/seller-invoices/store/invoices/invoices.adapter';
import { InvoicesState } from '@accounting-app/modules/seller-invoices/store/invoices/invoices.state';
import { State } from '@accounting-app/modules/seller-invoices/store/state';
import { ErrorResponse } from '~shared/model';

const featureModuleState = createFeatureSelector(SELLER_INVOICES_FEATURE_NAME);

const { selectAll } = invoicesAdapter.getSelectors();

export const selectInvoicesState = createSelector(featureModuleState, (state: State) => state.invoices);

export const selectInvoices = createSelector(selectInvoicesState, selectAll);

export const totalCount = createSelector(selectInvoicesState, (state: InvoicesState) => state.totalCount);

export const isGenerating = createSelector(selectInvoicesState, (state: InvoicesState) => state.isGenerating);

export const selectError = createSelector(selectInvoicesState, (state: InvoicesState) => state.error);

export const selectInvalidParams = createSelector(selectError, (error: ErrorResponse) => error && error.invalidParams);

export const selectPeriodFromUrl = createSelector(
  fromRouter.getFilterFromQueryParams,
  (filter: Params) => filter && filter.period.from,
);

export const selectStateFromUrl = createSelector(
  fromRouter.getFilterFromQueryParams,
  (filter: Params) => filter && filter.state,
);
