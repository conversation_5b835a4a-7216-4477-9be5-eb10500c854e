import * as faker from 'faker';

const totalCount = 10;

export const ORGANIZATIONS_MOCK = {
  overview: {
    total: {
      amount: faker.finance.amount(),
      currency: faker.finance.currencySymbol(),
    },
    vat: {
      amount: faker.finance.amount(),
      currency: faker.finance.currencySymbol(),
    },
    net: {
      amount: faker.finance.amount(),
      currency: faker.finance.currencySymbol(),
    },
    fee: {
      amount: faker.finance.amount(),
      currency: faker.finance.currencySymbol(),
    },
  },
  items: Array(totalCount)
    .fill({})
    .map(() => ({
      organizationId: faker.random.uuid(),
      organizationNumber: faker.finance.account(),
      organizationName: faker.random.word(),
      total: {
        amount: faker.finance.amount(),
        currency: faker.finance.currencySymbol(),
      },
      vat: {
        amount: faker.finance.amount(),
        currency: faker.finance.currencySymbol(),
      },
      net: {
        amount: faker.finance.amount(),
        currency: faker.finance.currencySymbol(),
      },
      fee: {
        amount: faker.finance.amount(),
        currency: faker.finance.currencySymbol(),
      },
    })),
  totalCount,
};
