import { createFeatureSelector, createSelector } from '@ngrx/store';
import { SELLER_BALANCE_FEATURE_NAME } from '@accounting-app/modules/seller-balance/constants';
import { State } from '@accounting-app/modules/seller-balance/store';
import { SellerTransactionState } from '@accounting-app/modules/seller-balance/store/seller-transaction/state';
import * as fromRouter from '~core/store';
import { Params } from '@angular/router';

export const getModuleFeatureState = createFeatureSelector(SELLER_BALANCE_FEATURE_NAME);

export const getSellerTransactionState = createSelector(
  getModuleFeatureState,
  (state: State) => state.sellerTransaction,
);

export const getSellerTransaction = createSelector(
  getSellerTransactionState,
  (state: SellerTransactionState) => state && state.payload.items,
);

export const getTotalCount = createSelector(
  getSellerTransactionState,
  (state: SellerTransactionState) => state && state.payload.totalCount,
);

export const getTotalBalanceWithMM = createSelector(
  getSellerTransactionState,
  (state: SellerTransactionState) => state && state.payload.totalBalanceWithMM,
);

export const isLoading = createSelector(getSellerTransactionState, (state: SellerTransactionState) => state.loading);

export const getSellerInfo = createSelector(
  getSellerTransactionState,
  (state: SellerTransactionState) => state.payload.sellerInfo,
);

export const getFilterQuery = createSelector(fromRouter.getFilterFromQueryParams, (filter: Params) => filter);
