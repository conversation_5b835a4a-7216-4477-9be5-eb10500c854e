import { Injectable } from '@angular/core';
import { MmAlertMode, MmAlertService, MmAlertType } from '@metromarkets/components-17';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, exhaustMap, map, switchMap, tap } from 'rxjs/operators';
import { SellerInvoice, SellerInvoiceStatesEnum } from '@accounting-app/modules/seller-invoices/models';
import { SellerInvoicesService } from '@accounting-app/modules/seller-invoices/services/seller-invoices.service';
import {
  sendInvoices,
  sendInvoicesFailure,
  sendInvoicesSuccess,
} from '@accounting-app/modules/seller-invoices/store/invoices/actions/send-invoices.actions';
import { ErrorResponse, PaginatedResponse } from '~shared/model';
import { LoadInvoicesActions } from './actions';
import { createInvoice, createInvoiceFailure, createInvoiceSuccess } from './actions/create-invoice.actions';
import { loadInvoicesFailure, loadInvoicesSuccess } from './actions/load-invoices.actions';

@Injectable()
export class InvoicesEffects {
  loadInvoices$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LoadInvoicesActions.loadInvoices),
      switchMap(({ params }) =>
        this.invoicesService.loadInvoices(params).pipe(
          map((invoicesPaginated: PaginatedResponse<SellerInvoice>) => loadInvoicesSuccess({ invoicesPaginated })),
          catchError((error: ErrorResponse) => of(loadInvoicesFailure({ error }))),
        ),
      ),
    ),
  );

  createInvoice$ = createEffect(() =>
    this.actions$.pipe(
      ofType(createInvoice),
      switchMap(({ invoiceGenerator }) =>
        this.invoicesService.createInvoice(invoiceGenerator).pipe(
          map((invoice: SellerInvoice) => createInvoiceSuccess({ invoice })),
          catchError((error: ErrorResponse) => of(createInvoiceFailure({ error }))),
        ),
      ),
    ),
  );

  sendInvoices$ = createEffect(() =>
    this.actions$.pipe(
      ofType(sendInvoices),
      exhaustMap(({ ids }) =>
        this.invoicesService.sendInvoices(ids).pipe(
          map(() =>
            sendInvoicesSuccess({
              invoices: ids.map(id => ({
                id,
                changes: { status: SellerInvoiceStatesEnum.Sent },
              })),
            }),
          ),
          catchError((error: ErrorResponse) => of(sendInvoicesFailure({ error }))),
        ),
      ),
    ),
  );
  private readonly alertTargetCssSelector = '#alert-target';
  sendInvoicesSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(sendInvoicesSuccess),
        tap(() => {
          this.alertService.addAlert({
            target: this.alertTargetCssSelector,
            type: MmAlertType.success,
            mode: MmAlertMode.banner,
            message: 'Invoices are successfully sent',
          });
        }),
      ),
    { dispatch: false },
  );
  sendInvoicesFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(sendInvoicesFailure),
        tap(({ error }) => {
          this.alertService.addAlert({
            target: this.alertTargetCssSelector,
            type: MmAlertType.error,
            mode: MmAlertMode.banner,
            message: error.detail,
          });
        }),
      ),
    { dispatch: false },
  );

  constructor(
    private actions$: Actions,
    private invoicesService: SellerInvoicesService,
    private alertService: MmAlertService,
  ) {}
}
