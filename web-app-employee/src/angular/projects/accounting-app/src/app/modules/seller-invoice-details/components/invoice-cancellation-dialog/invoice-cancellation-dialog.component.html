<div class="dialog-title">Invoice cancellation</div>
<div class="dialog-content">
    <div *ngIf="error" class="dialog-alert">
      Sorry, it seems like there is a technical issue.<br>Please drop a message in #support-seller-operations
    </div>

    <div *ngIf="invoiceIsPaid()" class="dialog-alert yellow">
        The invoice {{ invoice.invoiceNumber }} was paid.
    </div>

    <div class="cancellation-info">
      <b>The cancellation of this invoice will result in:</b>
      <ul>
          <li>Creation of the Credit Note related to this invoice</li>
          <li *ngIf="invoiceIsPaid()">Fund transfer being made to the seller liable account</li>
      </ul>
    </div>

    <form [formGroup]="invoiceCancellationForm">
      <mm-field>
        <mm-label for="reason">Cancellation reason</mm-label>
        <textarea mmTextarea placeholder="Please indicate the reason" formControlName="reason" class="mm-textarea"></textarea>
        <mm-error fieldName="reason">Cancellation reason is mandatory</mm-error>
      </mm-field>

      <div class="dialog-buttons">
        <button
            mm-button
            type="button"
            class="mm-button--ghost mm-button--medium mm-button"
            (click)="close()"
        >Back</button>
        <button
            mm-button
            class="mm-button--medium mm-button"
            (click)="cancelInvoice()"
        >Cancel Invoice</button>
      </div>
    </form>
</div>
