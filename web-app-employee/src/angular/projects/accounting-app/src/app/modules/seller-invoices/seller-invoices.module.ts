import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatPaginatorModule } from '@angular/material/paginator';
import {
  MmAlertModule,
  MmButtonModule,
  MmCheckboxModule,
  MmDialogModule,
  MmFormFieldModule,
  MmIconModule,
  MmSelectModule,
  MmTableModule,
  MmTablePaginationModule,
} from '@metromarkets/components-17';
import { QaLocatorsModule } from '@metromarkets/sdk-17';
import { NgSelectModule } from '@ng-select/ng-select';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { ConfirmDialogModule } from '~shared/modules/confirm-dialog/confirm-dialog.module';
import { FormErrorsModule } from '~shared/modules/form-errors/form-errors.module';
import { GenerateInvoicesModalComponent } from './components/generate-invoices-modal/generate-invoices-modal.component';
import { InvoicePeriodComponent } from './components/invoice-period/invoice-period.component';
import { InvoicesFilterComponent } from './components/invoices-filter/invoices-filter.component';
import { SellerAutocompleteComponent } from './components/seller-autocomplete/seller-autocomplete.component';
import { SellerInvoicesComponent } from './components/seller-invoices.component';
import { SellerInvoicesRoutingModule } from './seller-invoices-routing.module';
import { reducers } from './store';
import { SELLER_INVOICES_FEATURE_NAME } from './store/feature-name';
import { InvoicesEffects } from './store/invoices';

import { SellersEffects } from './store/sellers';

@NgModule({
  declarations: [
    SellerInvoicesComponent,
    GenerateInvoicesModalComponent,
    SellerAutocompleteComponent,
    InvoicesFilterComponent,
    InvoicePeriodComponent,
  ],
  imports: [
    CommonModule,
    StoreModule.forFeature(SELLER_INVOICES_FEATURE_NAME, reducers),
    EffectsModule.forFeature([InvoicesEffects, SellersEffects]),
    SellerInvoicesRoutingModule,
    MmTableModule,
    MatPaginatorModule,
    TranslateModule,
    ReactiveFormsModule,
    MmButtonModule,
    MmIconModule,
    MmFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatAutocompleteModule,
    MmDialogModule,
    FormErrorsModule,
    MmCheckboxModule,
    MmAlertModule,
    QaLocatorsModule,
    ConfirmDialogModule,
    MmSelectModule,
    NgSelectModule,
    FormsModule,
    MmTablePaginationModule,
  ],
})
export class SellerInvoicesModule {}
