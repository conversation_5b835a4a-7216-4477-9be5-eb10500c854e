export enum TransactionTypes {
  PENDING_CREDIT = 'PendingCredit',
  CREDIT_FAILED = 'CreditFailed',
  PENDING_DEBIT = 'PendingDebit',
  DEBIT_REVERSED_RECEIVED = 'DebitReversedReceived',
  DEBIT_FAILED = 'DebitFailed',
  CHARGEBACK_RECEIVED = 'ChargebackReceived',
  SECOND_CHARGEBACK_RECEIVED = 'SecondChargebackReceived',
  CHARGEBACK_REVERSED_RECEIVED = 'ChargebackReversedReceived',
  FUND_TRANSFER = 'FundTransfer',
  PAYOUT = 'Payout',
  CROSS_FLOW_PAYOUT = 'CrossFlowPayout',
  VIRTUAL_ACCOUNT_PAYOUT_RETURNED = 'VirtualAccountPayoutReturned',
  TRANSFER_BALANCE = 'TransferBalance',
  CONVERTED = 'Converted',
}
