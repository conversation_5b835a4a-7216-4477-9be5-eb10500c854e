import { Pipe, PipeTransform } from '@angular/core';
import { TransactionJournal, TransactionTypes } from '@accounting-app/modules/transactions-journal/models';

@Pipe({
  name: 'transactionAmount',
})
export class TransactionAmountPipe implements PipeTransform {
  transform(transaction: TransactionJournal, type: TransactionTypes): string {
    if (!transaction) {
      return '';
    }

    return transaction.type === type ? transaction.amount : '';
  }
}
