import { Action, createReducer, on } from '@ngrx/store';
import { SearchSellersActions } from '@accounting-app/modules/seller-invoices/store/sellers/actions';
import { sellersAdapter } from '@accounting-app/modules/seller-invoices/store/sellers/sellers.adapter';
import { sellersInitialState, SellersState } from '@accounting-app/modules/seller-invoices/store/sellers/sellers.state';

const reducer = createReducer(
  sellersInitialState,
  on(SearchSellersActions.searchSellersSuccess, (state, { sellers }) => {
    return sellersAdapter.setAll(sellers, { ...state, error: null });
  }),
  on(SearchSellersActions.searchSellersFailure, (state, { error }) => {
    return sellersAdapter.removeAll({ ...state, error });
  }),
);

export function sellersReducer(state: SellersState, action: Action) {
  return reducer(state, action);
}
