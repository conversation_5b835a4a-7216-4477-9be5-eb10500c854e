import { Field, Name } from 'serialize-ts/dist';
import { SellerInvoiceStatesEnum } from '@accounting-app/modules/seller-invoices/models/seller-invoice-states.enum';
import { SellerInvoiceMembershipFee } from '@accounting-app/modules/seller-invoices/models/seller-invoice-membership-fee.model';
import { Money } from '~shared/model';

export class SellerInvoice {
  @Field()
  id: string;

  @Field()
  sellerId: string;

  @Field()
  sellerNumber: string;

  @Field()
  shopName: string;

  @Field()
  invoiceNumber: string;

  @Field()
  @Name('invoiceState')
  status: SellerInvoiceStatesEnum;

  @Field()
  from: string;

  @Field()
  to: string;

  @Field()
  total: Money;

  @Field()
  netAmount: Money;

  @Field()
  grossAmount: Money;

  @Field()
  vatAmount: Money;

  @Field()
  vatRate: string;

  @Field()
  createdAt: string;

  @Field()
  downloadLink: string;

  @Field()
  relatedPeriod: string;

  @Field()
  paymentStatus: string;

  @Field()
  month: string;

  @Field()
  organizationId: string;

  @Field()
  membershipFee: SellerInvoiceMembershipFee;

  @Field()
  address: string;

  @Field()
  taxId: string;

  @Field()
  correctionReportId: string;

  @Field()
  correctionReportUrl: string;

  @Field()
  correctionReportName: string;

  @Field()
  correctionForReportId: string;
}
