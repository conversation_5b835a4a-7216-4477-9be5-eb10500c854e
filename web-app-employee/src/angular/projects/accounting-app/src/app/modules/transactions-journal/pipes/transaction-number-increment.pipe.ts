import { Pipe, PipeTransform } from '@angular/core';
import { DEFAULT_LIMIT, DEFAULT_OFFSET } from '@accounting-app/modules/transactions-journal/constants';

@Pipe({
  name: 'transactionNumberIncrement',
})
export class TransactionNumberIncrementPipe implements PipeTransform {
  transform(index: number, page = DEFAULT_OFFSET, limit = DEFAULT_LIMIT): number {
    if (!index) {
      return index;
    }
    return limit * page + index;
  }
}
