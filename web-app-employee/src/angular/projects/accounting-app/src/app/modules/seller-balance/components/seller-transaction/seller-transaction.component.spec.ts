import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SellerTransactionComponent } from './seller-transaction.component';
import { TranslateFakeLoader, TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { MmIconModule, MmTableModule, MmTablePaginationModule } from '@metromarkets/components-17';
import { MatTableModule } from '@angular/material/table';
import { RouterTestingModule } from '@angular/router/testing';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { reducers } from '../../store';
import { QaLocatorsModule } from '@metromarkets/sdk-17';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import * as fromSellerTransaction from '../../store/seller-transaction';

describe.skip('SellerTransactionComponent', () => {
  let component: SellerTransactionComponent;
  let fixture: ComponentFixture<SellerTransactionComponent>;
  let store: MockStore;

  function iconFactory() {
    return {};
  }

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SellerTransactionComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [provideMockStore({ initialState: fromSellerTransaction.sellerTransactionInitialState })],
      imports: [
        MmTableModule,
        MatTableModule,
        TranslateModule.forRoot({
          loader: {
            provide: TranslateLoader,
            useClass: TranslateFakeLoader,
          },
        }),
        MmTablePaginationModule,
        RouterTestingModule.withRoutes([]),
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
        StoreModule.forFeature('sellerTransaction', reducers.sellerBalance),
        QaLocatorsModule.forRoot({
          shouldSetupLocators: true,
        }),
        MmIconModule.forRoot(iconFactory),
        HttpClientTestingModule,
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SellerTransactionComponent);
    component = fixture.componentInstance;
    store = TestBed.inject(MockStore);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
