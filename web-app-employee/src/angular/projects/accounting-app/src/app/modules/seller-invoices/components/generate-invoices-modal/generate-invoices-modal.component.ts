import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MmDialogRefService } from '@metromarkets/components-17';
import { select, Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { shareReplay } from 'rxjs/operators';
import { InvoiceGeneratorModel, Seller, SellerInvoiceStatistic } from '@accounting-app/modules/seller-invoices/models';
import * as fromInvoices from '@accounting-app/modules/seller-invoices/store/invoices';
import { createInvoice } from '@accounting-app/modules/seller-invoices/store/invoices/actions/create-invoice.actions';
import * as fromSellers from '@accounting-app/modules/seller-invoices/store/sellers';
import { searchSellers } from '@accounting-app/modules/seller-invoices/store/sellers/actions/search-sellers.actions';
import { InvalidParams, Period } from '~shared/model';
import { FormValidators } from '~shared/validators/form-validators';
import { ALL_SELLERS_ID } from '../../constants';
import { SellerInvoicesService } from '../../services/seller-invoices.service';

@Component({
  selector: 'employee-app-generate-invoices-modal',
  templateUrl: './generate-invoices-modal.component.html',
  styleUrls: ['./generate-invoices-modal.component.scss'],
})
export class GenerateInvoicesModalComponent implements OnInit {
  public isGenerating$: Observable<boolean>;
  public invalidParams$: Observable<InvalidParams[]>;
  public sellers$: Observable<Seller[]>;
  public invoicesStats$: Observable<SellerInvoiceStatistic>;
  public invoiceForm: UntypedFormGroup;

  private period: Period;

  constructor(
    private fb: UntypedFormBuilder,
    private store: Store<fromInvoices.InvoicesState | fromSellers.SellersState>,
    private dialogService: MmDialogRefService<GenerateInvoicesModalComponent>,
    private invoicesService: SellerInvoicesService,
  ) {
    this.invoiceForm = this.buildForm();
  }

  ngOnInit(): void {
    this.searchSellers('');

    this.sellers$ = this.store.pipe(select(fromSellers.selectSellersWithDefaultOption));
    this.isGenerating$ = this.store.pipe(select(fromInvoices.isGenerating));
    this.invalidParams$ = this.store.pipe(select(fromInvoices.selectInvalidParams));
  }

  onSubmit(): void {
    if (this.invoiceForm.valid) {
      const sellerId = this.invoiceForm.get('sellerId').value.id;
      const isAllSellersOptionSelected = sellerId === ALL_SELLERS_ID;
      const partialInvoice: InvoiceGeneratorModel = {
        sellerId: isAllSellersOptionSelected ? undefined : sellerId,
        overwriteExisting: isAllSellersOptionSelected ? this.invoiceForm.get('overwriteExisting').value : undefined,
        overwriteSent: isAllSellersOptionSelected ? this.invoiceForm.get('overwriteSent').value : undefined,
        ...this.period,
      };

      this.store.dispatch(createInvoice({ invoiceGenerator: partialInvoice }));
    }
  }

  close() {
    this.dialogService.close();
  }

  searchSellers(query: string): void {
    this.store.dispatch(searchSellers({ query }));
  }

  loadStatistic(): void {
    const sellerId = this.invoiceForm.get('sellerId').value.id;

    if (sellerId && sellerId === ALL_SELLERS_ID) {
      this.invoicesStats$ = this.invoicesService
        .loadInvoicesStatistic(this.period)
        .pipe(shareReplay({ bufferSize: 1, refCount: true }));
    }
  }

  onPeriodChange(period: Period): void {
    this.period = period;
    this.loadStatistic();
  }

  private buildForm(): UntypedFormGroup {
    return this.fb.group({
      period: [new Date()],
      sellerId: ['', [FormValidators.required(), FormValidators.requireSellerId]],
      overwriteExisting: false,
      overwriteSent: false,
    });
  }
}
