import { Action, createReducer, on } from '@ngrx/store';
import { InvoiceCorrectionActions } from './actions';
import { invoiceCorrectionInitialState, InvoiceCorrectionState } from './invoice-correction.state';

const reducer = createReducer(
  invoiceCorrectionInitialState,
  on(InvoiceCorrectionActions.loadInvoiceCorrectionSuccess, (state, { invoiceCorrection }) => ({
    ...state,
    loading: false,
    payload: invoiceCorrection
  })),
  on(InvoiceCorrectionActions.loadInvoiceCorrectionFailure, (state, { error }) => ({
    ...state,
    loading: false,
    payload: null,
    error: {...error, title: 'some tile: ' + new Date().getTime()},
  })),
);

export function invoiceCorrectionReducer(state: InvoiceCorrectionState, action: Action) {
  return reducer(state, action);
}
