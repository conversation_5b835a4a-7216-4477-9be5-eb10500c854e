@import 'node_modules/@metromarkets/components-17/src/theme/settings/colors';

.seller-balance {
  &__header {
    padding: 20px 64px;
    flex-direction: row;
    justify-content: space-between;
    display: flex;
  }

  &__drilldown {
    flex: 0 0 75px;
    width: 75px;
  }

  &__cell {
    flex: 0 0 14%;
    width: 14%;

    &.seller-name {
      flex: 0 0 20%;
      width: 20%;
    }
  }

  &__footer-cell {
    border-top: 2px solid map-get($baseColors, blue-shade-60);
    border-bottom: 1px solid map-get($baseColors, grey-tint-90);
    font-size: 14px;
    font-weight: bold;
    color: map-get($baseColors, blue-shade-60);
    padding: 16px;

    &.align-right {
      text-align: right;
    }
  }

  &__date {
    padding-left: 64px;
  }

  &__date,
  &__date input {
    font-weight: bold;
    color: map-get($baseColors, metro-blue);
  }

  &__date-control {
    border: none;
    outline: none;
    cursor: pointer;
    width: 70px;
  }

  .drill-down-icon {
    transform: scale(1.5);
    cursor: pointer;
  }

  .seller-name-link {
    font-size: 14px;
    font-weight: 800;
    color: map-get($baseColors, blue);
    text-decoration: none;
    white-space: normal;
    word-wrap: break-word;
    border: none;
    background-color: transparent;
    text-align: left;
    cursor: pointer;
    padding-left: 0;
  }

  .download-button {
    font-weight: 800;
    text-align: center;
    color: #0064fe;
    font-size: 14px;
    cursor: pointer;
  }
}
