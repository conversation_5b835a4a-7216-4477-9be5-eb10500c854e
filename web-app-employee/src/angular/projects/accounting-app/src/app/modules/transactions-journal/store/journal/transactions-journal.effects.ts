import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { ROUTER_NAVIGATED } from '@ngrx/router-store';
import { select, Store } from '@ngrx/store';
import { isEmpty } from 'lodash';
import { of } from 'rxjs';
import { catchError, filter, map, switchMap, withLatestFrom } from 'rxjs/operators';
import { DEFAULT_LIMIT, DEFAULT_OFFSET } from '@accounting-app/modules/transactions-journal/constants';
import { TransactionsJournalService } from '@accounting-app/modules/transactions-journal/services/transactions-journal.service';
import {
  LoadTransactionsJournal,
  LoadTransactionsJournalFailure,
  LoadTransactionsJournalSuccess,
  TransactionsJournalActionTypes,
} from '@accounting-app/modules/transactions-journal/store/journal/transactions-journal.actions';
import * as fromRouter from '~core/store';
import { ACCOUNTING_ROUTE, TRANSACTIONS_JOURNAL_ROUTE } from '~shared/constants';
import { isUrlMatchRoute } from '~shared/utils/common';

@Injectable()
export class TransactionsJournalEffects {
  loadJournal$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TransactionsJournalActionTypes.LOAD_JOURNAL),
      map((action: LoadTransactionsJournal) => action.payload),
      switchMap(({ params }) =>
        this.journalService.getTransactions(params).pipe(
          map(transactions => new LoadTransactionsJournalSuccess({ transactions })),
          catchError(error => of(new LoadTransactionsJournalFailure({ error }))),
        ),
      ),
    ),
  );

  loadJournalByRouter$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ROUTER_NAVIGATED),
      withLatestFrom(
        this.store.pipe(select(fromRouter.getUrl)),
        this.store.pipe(select(fromRouter.getQueryParams)),
        (_, url, params) => ({ url, params }),
      ),
      filter(({ url }) => isUrlMatchRoute(url, `${ACCOUNTING_ROUTE}/${TRANSACTIONS_JOURNAL_ROUTE}`)),
      map(({ url, params }) => (!isEmpty(params) ? params : { offset: DEFAULT_OFFSET, limit: DEFAULT_LIMIT })),
      map(params => new LoadTransactionsJournal({ params })),
    ),
  );

  constructor(
    private actions$: Actions,
    private journalService: TransactionsJournalService,
    private store: Store<fromRouter.RouterState>,
  ) {}
}
