import { Component, OnInit, OnDestroy, Output, EventEmitter, Input } from '@angular/core';
import { Subject } from 'rxjs';
import * as fromSellerList from '@accounting-app/modules/seller-balance/store/seller-list';
import { select, Store } from '@ngrx/store';
import { takeUntil } from 'rxjs/operators';
import { SEARCH_SELLERS } from '@accounting-app/modules/seller-balance/constants/seller-list';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import {
  FilterOption,
  FilterTypes,
  SELLER_FILTER_OPTIONS,
  Seller,
  SellerListFilterResult,
} from '@accounting-app/modules/seller-balance/models';

@Component({
  selector: 'app-seller-list-filter',
  templateUrl: './seller-list-filter.component.html',
  styleUrls: ['./seller-list-filter.component.scss'],
})
export class SellerListFilterComponent implements OnInit, OnDestroy {
  @Input()
  filterDate;

  @Output()
  sellerSelection = new EventEmitter<string[]>();

  selectedSellersCount: number;

  @Output()
  chipRemoved = new EventEmitter<void>();

  @Output()
  filterByDate = new EventEmitter<SellerListFilterResult>();

  sellerList: Seller[];
  private unsubscribe$ = new Subject<void>();
  placeholder = SEARCH_SELLERS;
  sellerFilterOptions = SELLER_FILTER_OPTIONS;
  DropdownFieldType = FilterTypes;
  selectedField: FilterOption = this.sellerFilterOptions[0];

  FILTER_TITLES = {
    date: 'Select Date',
    autocomplete: 'Search Sellers',
  };

  ngOnDestroy(): void {
    this.unsubscribe$.complete();
  }

  ngOnInit(): void {
    this.store
      .pipe(select(fromSellerList.getSellerList), takeUntil(this.unsubscribe$))
      .subscribe((sellers: Seller[]) => {
        this.sellerList = sellers;
      });

    this.store
      .pipe(select(fromSellerList.getSelectedSellersCount), takeUntil(this.unsubscribe$))
      .subscribe((count: number) => (this.selectedSellersCount = count));
  }

  constructor(private store: Store<fromSellerList.SellerListState>) {}

  public getSelected(selected) {
    this.sellerSelection.emit(selected);
    this.selectedSellersCount = selected.length;
    this.store.dispatch(new fromSellerList.UpdateSellerSelection({ sellerIds: selected }));
  }

  chipDeleted() {
    this.chipRemoved.emit();
    this.store.dispatch(new fromSellerList.ResetSellerSelection());
    this.selectedSellersCount = 0;
  }

  dateChange(event: MatDatepickerInputEvent<Date | string>, name: string) {
    this.filterByDate.emit({ date: event.value, name });
    this.selectedSellersCount = 0;
  }

  onFieldChange(field: FilterOption): void {
    this.selectedField = field;
  }
}
