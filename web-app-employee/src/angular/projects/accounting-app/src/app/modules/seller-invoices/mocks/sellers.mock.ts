import * as faker from 'faker';

const arrayLength = 5;

export const SELLERS_MOCK = {
  items: Array(arrayLength)
    .fill({})
    .map(() => ({
      id: faker.random.uuid(),
      userFriendlyId: faker.random.alphaNumeric(),
      organizationId: faker.random.uuid(),
      userFriendlyOrganizationId: faker.random.alphaNumeric(),
      shopName: faker.random.word(),
    })),
  totalCount: arrayLength,
};
