import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SELLERS_INVOICE_CANCELLATION_API_ROUTE } from '@accounting-app/modules/seller-invoice-details/constants';
import { InvoiceCancellationRequest } from '@accounting-app/modules/seller-invoice-details/models/invoice-cancellation-request.model';

@Injectable({
  providedIn: 'root',
})
export class InvoiceCancellationService {
  constructor(private httpClient: HttpClient) {}

  cancelMFInvoiceById(payload: InvoiceCancellationRequest): Observable<{}> {
    return this.httpClient.post(`${SELLERS_INVOICE_CANCELLATION_API_ROUTE}`, payload);
  }
}
