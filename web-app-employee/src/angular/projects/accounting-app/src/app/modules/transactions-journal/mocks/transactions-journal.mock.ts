import * as faker from 'faker';
import { TransactionTypes } from '@accounting-app/modules/transactions-journal/models';

const totalCount = 10;

export const TRANSACTIONS_JOURNAL_MOCK = {
  items: Array(totalCount)
    .fill({})
    .map(() => ({
      transactionId: faker.random.uuid(),
      type: TransactionTypes.Accuran,
      balance: faker.finance.amount(),
      currency: faker.finance.currencyName(),
      fromAccountName: faker.random.word(),
      toAccountName: faker.random.word(),
      description: faker.random.words(),
      createdAt: new Date().toISOString(),
    })),
  totalCount,
};
