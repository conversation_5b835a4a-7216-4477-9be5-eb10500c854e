import { Component, Inject, OnInit } from '@angular/core';
import { SellerInvoiceCorrection, SellerInvoiceDetails } from "@accounting-app/modules/seller-invoice-details/models";
import { FormBuilder, FormGroup } from "@angular/forms";
import { FormValidators } from "~shared/validators/form-validators";
import * as fromInvoice from '@accounting-app/modules/seller-invoice-details/store/invoice-correction';
import { select, Store } from '@ngrx/store';
import { loadInvoiceCorrection } from "@accounting-app/modules/seller-invoice-details/store/invoice-correction/actions/invoice-correction.actions";
import { MmDialogData, MmDialogRefService } from '@metromarkets/components-17';

@Component({
  selector: 'invoice-correction-dialog',
  templateUrl: './invoice-correction-dialog.component.html',
  styleUrls: ['./invoice-correction-dialog.component.scss'],
})
export class InvoiceCorrectionDialogComponent implements OnInit {

  public sellerInvoiceForm: FormGroup;
  public message: string | null = null;
  isLoading$: boolean = false;
  invoiceCorrectionResult: SellerInvoiceCorrection;
  constructor(
    @Inject(MmDialogData) public data: { invoice: SellerInvoiceDetails, invoiceDetailsRefresh: EventListener  },
    private dialogRef: MmDialogRefService<InvoiceCorrectionDialogComponent>,
    private formBuilder: FormBuilder,
    private store: Store<fromInvoice.InvoiceCorrectionState>,
  ) {
  }

  ngOnInit() {
    this.sellerInvoiceForm = this.formBuilder.group({
      reason: [null, FormValidators.required()]
    });
    this.store.pipe(
      select(fromInvoice.selectInvoiceCorrection),
    ).subscribe(
      (data) => {
        if (!data.payload) {
          return;
        }

        if (data.error) {
          this.message = 'Invoice correction failed';
        } else if (this.data.invoice.correctionReportId != data.payload.reportId) {
          this.invoiceCorrectionResult = data.payload;
          this.data.invoiceDetailsRefresh.call(true);
          this.dialogRef.close('Invoice has been successfully corrected');
        }
        this.isLoading$ = false;
      }
    );
  }

  onSubmit() {
    if (this.sellerInvoiceForm.valid) {
      this.isLoading$ = true;
      const reason = this.sellerInvoiceForm.get('reason').value;
      this.store.dispatch(loadInvoiceCorrection({id: this.data.invoice.id, reason}));
    }
  }

  close() {
    this.dialogRef.close();
  }

  protected readonly parseInt = parseInt;

  public isInvoiceCorrected() {
    return this.invoiceCorrectionResult ?? this.data.invoice.correctionReportId;
  }
}
