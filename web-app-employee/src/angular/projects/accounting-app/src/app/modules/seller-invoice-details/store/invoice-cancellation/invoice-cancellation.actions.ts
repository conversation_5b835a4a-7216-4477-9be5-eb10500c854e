import { Action } from '@ngrx/store';
import { ErrorResponse } from '~shared/model/error-response.model';
import { InvoiceCancellationRequest } from '@accounting-app/modules/seller-invoice-details/models/invoice-cancellation-request.model';

export enum InvoiceCancellationActionTypes {
  LOAD_INVOICE_CANCELLATION = '[InvoiceCancellation] InvoiceCancellation load',
  LOAD_INVOICE_CANCELLATION_SUCCESS = '[InvoiceCancellation] InvoiceCancellation load success',
  LOAD_INVOICE_CANCELLATION_FAILURE = '[InvoiceCancellation] InvoiceCancellation load failure',
}

export class LoadInvoiceCancellation implements Action {
  readonly type = InvoiceCancellationActionTypes.LOAD_INVOICE_CANCELLATION;

  constructor(public payload: InvoiceCancellationRequest) {}
}

export class LoadInvoiceCancellationSuccess implements Action {
  readonly type = InvoiceCancellationActionTypes.LOAD_INVOICE_CANCELLATION_SUCCESS;

  constructor() {}
}

export class LoadInvoiceCancellationFailure implements Action {
  readonly type = InvoiceCancellationActionTypes.LOAD_INVOICE_CANCELLATION_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export type InvoiceCancellationActions =
  | LoadInvoiceCancellation
  | LoadInvoiceCancellationSuccess
  | LoadInvoiceCancellationFailure;
