import { Action, createReducer, on } from '@ngrx/store';
import { LoadOrganizationsActions } from '@accounting-app/modules/organizations/store/organizations/actions';
import { organizationsAdapter } from '@accounting-app/modules/organizations/store/organizations/organizations.adapter';
import {
  organizationsInitialState,
  OrganizationsState,
} from '@accounting-app/modules/organizations/store/organizations/organizations.state';

const reducer = createReducer(
  organizationsInitialState,
  on(LoadOrganizationsActions.loadOrganizationsSuccess, (state, { organizations }) => {
    return organizationsAdapter.addMany(organizations.items, {
      ...state,
      totalCount: organizations.totalCount,
      error: null,
      summary: organizations.summary,
    });
  }),
  on(LoadOrganizationsActions.loadOrganizationsFailure, (state, { error }) => {
    return organizationsAdapter.removeAll({
      ...state,
      totalCount: 0,
      summary: null,
      error,
    });
  }),
);

export function organizationsReducer(state: OrganizationsState, action: Action) {
  return reducer(state, action);
}
