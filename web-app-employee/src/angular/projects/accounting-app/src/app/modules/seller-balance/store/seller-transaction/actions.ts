import { Action } from '@ngrx/store';
import { ErrorResponse, RequestParams } from '~shared/model';
import {
  SellerInfo,
  SellerTransaction,
  SellerTransactionPaginatedResponse,
} from '@accounting-app/modules/seller-balance/models';

export enum SellerTransactionsActionTypes {
  LOAD_TRANSACTIONS = '[Seller Transactions] Load Transactions',
  LOAD_TRANSACTIONS_SUCCESS = '[Seller Transactions] Load Transactions Success',
  LOAD_TRANSACTIONS_FAILURE = '[Seller Transactions] Load Transactions Failure',
  DOWNLOAD_TRANSACTIONS = '[Seller Transactions] Download Transactions',
  DOWNLOAD_TRANSACTIONS_SUCCESS = '[Seller Transactions] Download Transactions Success',
  DOWNLOAD_TRANSACTIONS_FAILURE = '[Seller Transactions] Download Transactions Failure',
}

export class LoadTransactions implements Action {
  readonly type = SellerTransactionsActionTypes.LOAD_TRANSACTIONS;

  constructor(public payload: { sellerId: string; params: RequestParams }) {}
}

export class LoadTransactionsSuccess implements Action {
  readonly type = SellerTransactionsActionTypes.LOAD_TRANSACTIONS_SUCCESS;

  constructor(
    public payload: {
      transactions: SellerTransactionPaginatedResponse<SellerTransaction, SellerInfo>;
    },
  ) {}
}

export class LoadTransactionsFailure implements Action {
  readonly type = SellerTransactionsActionTypes.LOAD_TRANSACTIONS_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export class DownloadTransactions implements Action {
  readonly type = SellerTransactionsActionTypes.DOWNLOAD_TRANSACTIONS;

  constructor(public payload: { sellerId: string; fileName: string }) {}
}

export class DownloadTransactionsSuccess implements Action {
  readonly type = SellerTransactionsActionTypes.DOWNLOAD_TRANSACTIONS_SUCCESS;
}

export class DownloadTransactionsFailure implements Action {
  readonly type = SellerTransactionsActionTypes.DOWNLOAD_TRANSACTIONS_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export type SellerTransactionsActionUnion =
  | LoadTransactions
  | LoadTransactionsSuccess
  | LoadTransactionsFailure
  | DownloadTransactions
  | DownloadTransactionsSuccess
  | DownloadTransactionsFailure;
