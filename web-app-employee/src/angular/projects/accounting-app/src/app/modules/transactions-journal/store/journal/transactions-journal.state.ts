import { EntityState } from '@ngrx/entity';
import { TransactionJournal } from '@accounting-app/modules/transactions-journal/models';
import { transactionsJournalAdapter } from '@accounting-app/modules/transactions-journal/store/journal/transactions-journal.adapter';
import { ErrorResponse } from '~shared/model';

export interface TransactionsJournalState extends EntityState<TransactionJournal> {
  error: ErrorResponse;
  loading: boolean;
  totalCount: number;
}

export const transactionsJournalInitialState = transactionsJournalAdapter.getInitialState({
  error: null,
  loading: false,
  totalCount: 0,
});
