import { SELLERS_MOCK, SELLERS_STATE_MOCK } from '@accounting-app/modules/seller-invoices/mocks';
import { Seller } from '@accounting-app/modules/seller-invoices/models';
import {
  selectSellers,
  selectSellersWithDefaultOption,
} from '@accounting-app/modules/seller-invoices/store/sellers/sellers.selectors';
import { StoreUtils } from '~shared/utils/store.utils';
import { PaginatedResponse } from '../../../../../../../../src/app/shared/model';

describe('Sellers Selectors', () => {
  describe('selectSellers', () => {
    it('should return sellers from state', () => {
      const actual = selectSellers.projector(SELLERS_STATE_MOCK);
      const expected = StoreUtils.deserializePaginatedCollection<Seller>(
        SELLERS_MOCK as PaginatedResponse<any>,
        Seller,
      );

      expect(actual).toEqual(expected);
    });
  });

  describe('selectSellersWithDefaultOption', () => {
    it('should return sellers from state with default option All Sellers as first item', () => {
      const sellers = StoreUtils.deserializePaginatedCollection<Seller>(SELLERS_MOCK, Seller);
      const actual = selectSellersWithDefaultOption.projector(sellers);
      const allSellersOption = { id: 'all_sellers', shopName: 'All Sellers' };
      const expected = [allSellersOption, ...sellers];

      expect(actual).toEqual(expected);
    });
  });
});
