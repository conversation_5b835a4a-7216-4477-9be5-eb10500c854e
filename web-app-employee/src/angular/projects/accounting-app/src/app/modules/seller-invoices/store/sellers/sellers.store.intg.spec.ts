import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { EffectsModule } from '@ngrx/effects';
import { select, Store, StoreModule } from '@ngrx/store';
import * as faker from 'faker';
import { deserialize } from 'serialize-ts/dist';
import { SELLERS_MOCK, SELLERS_STATE_MOCK } from '@accounting-app/modules/seller-invoices/mocks';
import { reducers } from '@accounting-app/modules/seller-invoices/store';
import { SELLER_INVOICES_FEATURE_NAME } from '@accounting-app/modules/seller-invoices/store/feature-name';
import { searchSellers } from '@accounting-app/modules/seller-invoices/store/sellers/actions/search-sellers.actions';
import { SellersEffects } from '@accounting-app/modules/seller-invoices/store/sellers/sellers.effects';
import { selectSellersState } from '@accounting-app/modules/seller-invoices/store/sellers/sellers.selectors';
import { SellersState } from '@accounting-app/modules/seller-invoices/store/sellers/sellers.state';
import { BaseHttpInterceptor } from '~core/interceptors/base-http-interceptor';
import { ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK } from '~shared/mock';
import { ErrorResponse } from '~shared/model';
import {OAuthService} from "angular-oauth2-oidc";

const host = window['config'].serviceBaseUrls.sellerOffice;

describe('Sellers Store Integration', () => {
  let httpMock: HttpTestingController;
  let store: Store<SellersState>;
  let oauthService: jest.Mocked<OAuthService>;

  beforeEach(() => {
    const oauthServiceMock = {
      getAccessToken: jest.fn(),
    } as unknown as jest.Mocked<OAuthService>;

    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        HttpClientTestingModule,
        StoreModule.forRoot({}, { runtimeChecks: { strictStateImmutability: true, strictActionImmutability: true } }),
        StoreModule.forFeature(SELLER_INVOICES_FEATURE_NAME, reducers),
        EffectsModule.forRoot([]),
        EffectsModule.forFeature([SellersEffects]),
      ],
      providers: [
        { provide: HTTP_INTERCEPTORS, useClass: BaseHttpInterceptor, multi: true },
        { provide: OAuthService, useValue: oauthServiceMock },
      ],
    });

    httpMock = TestBed.inject(HttpTestingController);
    store = TestBed.inject(Store);
    oauthService = TestBed.inject(OAuthService) as jest.Mocked<OAuthService>;
  });

  describe('Search Sellers', () => {
    const query = faker.random.word();
    const url = `${host}/api/v1/employee/organizations/search?filter[shopName]=${query}`;
    const action = searchSellers({ query });
    const throttling = 300;

    describe('searchSellersSuccess', () => {
      it('should make an api call and return state with sellers when action is dispatched', fakeAsync(() => {
        store.dispatch(action);

        tick(throttling);

        const req = httpMock.expectOne(encodeURI(url));

        expect(req.request.method).toBe('GET');

        req.flush(SELLERS_MOCK);

        store.pipe(select(selectSellersState)).subscribe(state => expect(state).toEqual(SELLERS_STATE_MOCK));

        tick();
      }));
    });

    describe('searchSellersFailure', () => {
      it('should make an api call and return state with an error when action is dispatched', fakeAsync(() => {
        const expected: SellersState = {
          entities: {},
          ids: [],
          error: deserialize(ERROR_RESPONSE_MOCK, ErrorResponse),
        };
        store.dispatch(action);

        tick(throttling);

        const req = httpMock.expectOne(encodeURI(url));

        expect(req.request.method).toBe('GET');

        req.flush(ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK);

        store.pipe(select(selectSellersState)).subscribe(state => expect(state).toEqual(expected));

        tick();
      }));
    });
  });
});
