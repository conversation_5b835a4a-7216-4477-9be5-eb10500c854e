.seller-invoices {
  padding-top: 20px;
  padding-bottom: 20px;

  &__heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 64px;
  }

  &__table {
    width: 100%;
  }

  &__actions {
    display: flex;
    padding-left: 20px;
    padding-right: 20px;
  }

  &__send-btn {
    margin-left: auto;
  }
}

.alert-wrapper {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
}

.paginator-container {
  ::ng-deep .mat-mdc-paginator-page-size {
    align-items: center;
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
    .mat-form-field-wrapper {
      width: 100px;
      padding-bottom: 0;
    }

    .mat-mdc-paginator-page-size-label {
      flex-basis: 500px;
    }
  }
}

.invoices-filter {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  padding-right: 20px;
  padding-left: 20px;
}

.seller-invoices-modified {
  &__heading {
    margin-top: 20px;
    padding-left: 55px;
  }
}

iframe {
  display: block;
  border: none;
  min-height: 1100px;
  width: 100%;
  padding: 20px;
  margin-top: 20px;
}
