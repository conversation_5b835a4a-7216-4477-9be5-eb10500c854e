import { deserialize } from 'serialize-ts/dist';
import { SELLER_INVOICE_DETAILS_MOCK } from '@accounting-app/modules/seller-invoice-details/mocks/seller-invoice-details.mock';
import { SellerInvoiceDetails } from '@accounting-app/modules/seller-invoice-details/models';
import { InvoiceDetailsState } from '@accounting-app/modules/seller-invoice-details/store/invoice-details';

export const SELLER_INVOICE_DETAILS_STATE_MOCK: InvoiceDetailsState = {
  payload: deserialize(SELLER_INVOICE_DETAILS_MOCK, SellerInvoiceDetails),
  loading: false,
  error: null,
};
