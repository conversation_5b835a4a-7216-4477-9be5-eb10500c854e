<div class="seller-list-filter">
  <div fxLayout="row" fxFill fxLayoutAlign="start center">
    <div [ngSwitch]="selectedField.fieldType" fxFlex="50">
      <div class="" *ngSwitchCase="DropdownFieldType.AUTOCOMPLETE">
        <app-multiselect-autocomplete
          [data]="sellerList"
          (result)="getSelected($event)"
          [placeholder]="placeholder"
        ></app-multiselect-autocomplete>
      </div>

      <div
        class="seller-list-filter__date_form"
        *ngSwitchCase="DropdownFieldType.DATE"
        fxLayout="row"
        fxFill
        fxLayoutAlign="start stretch"
      >
        <input
          mmInput
          [matDatepicker]="reportDate"
          (click)="reportDate.open()"
          (dateChange)="dateChange($event, 'date')"
          [value]="filterDate"
          readonly="readonly"
          class="date"
        />
        <mat-datepicker-toggle matSuffix [for]="reportDate" class="date_toggle"></mat-datepicker-toggle>
        <mat-datepicker #reportDate></mat-datepicker>
      </div>
    </div>
    <mm-select
      class="form__select"
      (selectionChanged)="onFieldChange($event)"
      [options]="sellerFilterOptions"
      [displayedProperty]="'value'"
      [value]="selectedField"
      fxFlex="50"
    ></mm-select>
  </div>
  <div class="seller-list-filter__container">
    <div class="seller-list-filter__date" fxFlex fxLayout="row" fxLayoutAlign="fxLayoutGap center">
      <div class="seller-list-filter__chip_container">
        <mat-chip-option
          fxFlex
          fxFill
          fxLayout="row"
          fxLayoutAlign="start stretch"
          [removable]="false"
          class="seller-list-filter__chip"
          ><span class="date">Date: {{ filterDate | date: 'dd.MM.yyyy' }}</span>
        </mat-chip-option>
        <mat-chip-option
          *ngIf="selectedSellersCount"
          (removed)="chipDeleted()"
          [removable]="true"
          class="seller-list-filter__chip"
          fxFlex
          fxLayout="row"
          fxLayoutAlign="space-between center"
        >
          <span class="seller-text">Sellers ({{ selectedSellersCount }})</span>
          <button matChipRemove class="seller-list-filter__chip_remove">
            <mat-icon class="seller-list-filter__icon_remove">close</mat-icon>
          </button>
        </mat-chip-option>
      </div>
    </div>
  </div>
</div>
