import { deserialize } from 'serialize-ts/dist';
import {
  TRANSACTIONS_JOURNAL_MOCK,
  TRANSACTIONS_JOURNAL_STATE_MOCK,
} from '@accounting-app/modules/transactions-journal/mocks';
import { TransactionJournal } from '@accounting-app/modules/transactions-journal/models';
import * as fromJournal from '@accounting-app/modules/transactions-journal/store/journal';

describe('Transactions Journal Selectors', () => {
  const journalDefaultStateMock = TRANSACTIONS_JOURNAL_STATE_MOCK;

  describe('getTransactionsJournal', () => {
    it('should return collection of transactions', () => {
      const actual = fromJournal.getTransactionsJournal.projector(journalDefaultStateMock);
      const expected = TRANSACTIONS_JOURNAL_MOCK.items.map(t => deserialize(t, TransactionJournal));
      expect(actual).toEqual(expected);
    });
  });

  describe('getTransactionsTotal', () => {
    it('should return totalCount property', () => {
      const actual = fromJournal.getTransactionsTotal.projector(journalDefaultStateMock);
      expect(actual).toBe(journalDefaultStateMock.totalCount);
    });
  });
});
