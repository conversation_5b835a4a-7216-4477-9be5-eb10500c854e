import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SellerInvoicesComponent } from './components/seller-invoices.component';

const routes: Routes = [
  { path: '', component: SellerInvoicesComponent },
  {
    path: ':id',
    loadChildren: () =>
      import('../seller-invoice-details/seller-invoice-details.module').then(m => m.SellerInvoiceDetailsModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SellerInvoicesRoutingModule {}
