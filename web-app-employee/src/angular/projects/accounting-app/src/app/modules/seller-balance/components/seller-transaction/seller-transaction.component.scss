@import 'node_modules/@metromarkets/components-17/src/theme/settings';

.seller-txn {
  &__header {
    padding: 20px 64px;
  }

  &__back-button {
    padding: 20px 64px 0px;
  }

  &__seller-info {
    padding: 0 64px 0 64px;
    color: map-get($baseColors, primary);
    font-weight: 600;
  }

  &__dates {
    font-size: 11px;

    &__subtitle {
      font-weight: 600;
    }
  }

  &__header-cell,
  &__cell,
  &__footer-cell {
    width: 15%;
  }

  &__footer-cell {
    border-top: 2px solid map-get($baseColors, blue-shade-60);
    border-bottom: 1px solid map-get($baseColors, grey-tint-90);
    font-size: 14px;
    font-weight: bold;
    color: map-get($baseColors, blue-shade-60);
    padding: 16px;

    &:first-of-type {
      padding-left: 32px;
    }

    &:last-of-type {
      padding-right: 32px;
    }

    &.align-right {
      text-align: right;
    }
  }

  &__search {
    width: 100%;
  }

  &__export {
    padding-right: 64px;
  }

  &__export-button {
    margin-top: 40px;
    margin-bottom: 30px;
    margin-left: -48px;
  }
}
