@import 'node_modules/@metromarkets/components-17/src/theme/settings/colors';

.seller-list-filter {
  padding: 20px 64px;

  &__container {
    margin-top: 16px;
  }

  & __date {
    padding-left: 64px;
  }

  &__date,
  &__date input {
    font-weight: bold;
    color: map-get($baseColors, metro-blue);
  }

  &__date-control,
  &__date-control:focus,
  &__date-control:hover {
    border: none;
    outline: none;
    cursor: pointer;
    width: 70px;
  }

  &__date_form {
    width: 100%;
    position: relative;

    .date {
      width: 100%;
    }

    .date_toggle {
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  &__chip_container {
    .seller-text {
      min-width: 55px;
    }

    .date {
      text-align: center;
      width: 100%;
    }
  }

  &__chip {
    background-color: map-get($baseColors, blue-tint-90);
    padding: 6px 10px 6px 10px;
    font-size: 12px;
    color: map-get($baseColors, grey);
    overflow: hidden;
    height: 30px;
    margin-right: 10px;
    min-width: 120px;
  }

  &__chip_remove {
    background-color: transparent;
    cursor: pointer;
    border: none;
    margin-left: 10px;
    padding-right: 0;
  }

  &__icon_remove {
    font-size: 18px;
    color: map-get($baseColors, blue);
    padding-top: 3px;
  }
}
