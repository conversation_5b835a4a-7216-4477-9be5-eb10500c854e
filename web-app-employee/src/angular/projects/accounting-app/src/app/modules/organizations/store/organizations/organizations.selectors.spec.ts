import { deserialize } from 'serialize-ts/dist';
import { ORGANIZATIONS_MOCK, ORGANIZATIONS_STATE_MOCK } from '@accounting-app/modules/organizations/mocks';
import { Organization } from '@accounting-app/modules/organizations/models';
import {
  selectOrganizations,
  selectSummary,
  selectTotalCount,
} from '@accounting-app/modules/organizations/store/organizations/organizations.selectors';

describe('Organizations Selectors', () => {
  const defaultState = ORGANIZATIONS_STATE_MOCK;

  describe('selectOrganizations', () => {
    it('should return list of organizations from state', () => {
      const actual = selectOrganizations.projector(defaultState);
      const expected = ORGANIZATIONS_MOCK.items.map(i => deserialize(i, Organization));

      expect(actual).toEqual(expected);
    });
  });

  describe('selectTotalCount', () => {
    it('should return totalCount from state', () => {
      const actual = selectTotalCount.projector(defaultState);
      const expected = defaultState.totalCount;

      expect(actual).toBe(expected);
    });
  });

  describe('selectSummary', () => {
    const actual = selectSummary.projector(defaultState);
    const expected = defaultState.summary;

    expect(actual).toEqual(expected);
  });
});
