import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MmTableModule } from '@metromarkets/components-17';
import { QaLocatorsModule } from '@metromarkets/sdk-17';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { reducers } from '@accounting-app/modules/organizations/store';
import { ORGANIZATIONS_FEATURE_NAME } from '@accounting-app/modules/organizations/store/feature-name';
import { OrganizationsEffects } from '@accounting-app/modules/organizations/store/organizations/organizations.effects';
import { OrganizationListComponent } from './components/organization-list.component';

import { OrganizationsRoutingModule } from './organizations-routing.module';

@NgModule({
  declarations: [OrganizationListComponent],
  imports: [
    CommonModule,
    OrganizationsRoutingModule,
    StoreModule.forFeature(ORGANIZATIONS_FEATURE_NAME, reducers),
    EffectsModule.forFeature([OrganizationsEffects]),
    MmTableModule,
    MatPaginatorModule,
    QaLocatorsModule,
  ],
})
export class OrganizationsModule {}
