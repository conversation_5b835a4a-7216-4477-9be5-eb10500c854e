import { Action, createReducer, on } from '@ngrx/store';
import { invoicesAdapter } from '@accounting-app/modules/seller-invoices/store/invoices/invoices.adapter';
import {
  invoicesInitialState,
  InvoicesState,
} from '@accounting-app/modules/seller-invoices/store/invoices/invoices.state';
import { CreateInvoiceActions, LoadInvoicesActions, SendInvoicesActions } from './actions';

const reducer = createReducer(
  invoicesInitialState,
  on(LoadInvoicesActions.loadInvoicesSuccess, (state, { invoicesPaginated }) => {
    return invoicesAdapter.setAll(invoicesPaginated.items, {
      ...state,
      error: null,
      totalCount: invoicesPaginated.totalCount,
    });
  }),
  on(LoadInvoicesActions.loadInvoicesFailure, (state, { error }) => {
    return invoicesAdapter.removeAll({
      ...state,
      error,
      totalCount: 0,
    });
  }),
  on(CreateInvoiceActions.createInvoice, state => ({
    ...state,
    isGenerating: true,
  })),
  on(CreateInvoiceActions.createInvoiceSuccess, (state, { invoice }) => {
    return invoicesAdapter.addOne(invoice, {
      ...state,
      isGenerating: false,
      error: null,
      totalCount: +state.totalCount + 1,
    });
  }),
  on(CreateInvoiceActions.createInvoiceFailure, (state, { error }) => ({
    ...state,
    isGenerating: false,
    error,
  })),
  on(SendInvoicesActions.sendInvoicesSuccess, (state, { invoices }) => {
    return invoicesAdapter.updateMany(invoices, {
      ...state,
      error: null,
    });
  }),
  on(SendInvoicesActions.sendInvoicesFailure, (state, { error }) => ({
    ...state,
    error,
  })),
);

export function invoicesReducer(state: InvoicesState, action: Action) {
  return reducer(state, action);
}
