<div class="seller-txn">
  <div class="seller-txn__back-button">
    <app-back-button backUrl="../" linkText="Sellers’ Balances Overview"></app-back-button>
  </div>
  <div class="seller-txn__header">
    <h2>{{ 'SELLER_BALANCE.TITLE.SELLER_TRANSACTION_OVERVIEW' | translate }}</h2>
  </div>

  <mm-table-loader *ngIf="isLoading$ | async"></mm-table-loader>

  <div class="seller-txn__seller-info">
    <div>
      {{ 'SELLER_BALANCE.TITLE.SELLER_NAME' | translate }}: {{ sellerInfo?.name }} ({{ sellerInfo?.userFriendlyId }})
    </div>
    <div>
      {{ 'SELLER_BALANCE.TITLE.TOTAL_BALANCE_WITH_METRO' | translate }} :
      {{ totalBalanceWithMM?.amount | currency: totalBalanceWithMM?.currency }}
    </div>
  </div>

  <div fxLayout="row" fxLayoutAlign="start stretch" fxFill fxFlexAlign="center" class="seller-txn__search">
    <div fxFlex="80">
      <app-search-bar
        (addFilterEvent)="searchByFields($event)"
        (removeFilterEvent)="onClearSearchField($event)"
        [searchQuery]="searchQuery$ | async"
        [filterTitleMap]="searchFilterTitles"
        [options]="searchOptions"
      >
      </app-search-bar>
    </div>
    <div class="seller-txn__export" fxFlex fxLayout="row" fxLayoutAlign="end stretch">
      <button class="seller-txn__export-button" mm-button (click)="downloadTransactions()">Export</button>
    </div>
  </div>

  <table mm-table [dataSource]="transactions$ | async" class="table seller-txn-table mm-elevation-z8">
    <ng-container mmColumnDef="orderNumber">
      <th mmHeaderCell *mmHeaderCellDef class="seller-txn__header-cell align-left">
        {{ displayedColumnsTitles.orderNumber }}
      </th>
      <td mmCell *mmCellDef="let element" class="seller-txn__cell order-number">
        {{ element.orderNumber }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="transactionDates">
      <th mmHeaderCell *mmHeaderCellDef>
        {{ displayedColumnsTitles.transactionDates }}
      </th>
      <td mmCell *mmCellDef="let element" class="seller-txn__cell seller-txn__dates">
        <div><span class="seller-txn__dates__subtitle">Transaction:&nbsp;</span> {{ element.transactionDate }}</div>
        <div>
          <span class="seller-txn__dates__subtitle">Paidout:&nbsp;</span>
          <span *ngIf="element.paidoutDate !== null">{{ element.paidoutDate }}</span>
          <span *ngIf="!element.paidoutDate">N/A</span>
        </div>
        <div>
          <span class="seller-txn__dates__subtitle">Accounted at:&nbsp;</span>
          <span *ngIf="element.accountedAt !== null">{{ element.accountedAt }}</span>
          <span *ngIf="!element.accountedAt">N/A</span>
        </div>
      </td>
    </ng-container>
    <ng-container mmColumnDef="pspReference">
      <th mmHeaderCell *mmHeaderCellDef class="seller-txn__header-cell">
        {{ displayedColumnsTitles.pspReference }}
      </th>
      <td mmCell *mmCellDef="let element" class="seller-txn__cell">
        {{ element.pspReference }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="paymentProvider">
      <th mmHeaderCell *mmHeaderCellDef class="seller-txn__header-cell">
        {{ displayedColumnsTitles.paymentProvider }}
      </th>
      <td mmCell *mmCellDef="let element" class="seller-txn__cell">
        {{ element.paymentProvider }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="transactionType">
      <th mmHeaderCell *mmHeaderCellDef class="seller-txn__header-cell">
        {{ displayedColumnsTitles.transactionType }}
      </th>
      <td mmCell *mmCellDef="let element" class="seller-txn__cell">
        {{ element.transactionType }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="salesChannel">
      <th mmHeaderCell *mmHeaderCellDef class="seller-txn__header-cell">
        {{ displayedColumnsTitles.salesChannel }}
      </th>
      <td mmCell *mmCellDef="let element" class="seller-txn__cell">
        {{ element.salesChannel }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="amount">
      <th mmHeaderCell *mmHeaderCellDef mmTextAlign="right" class="seller-txn__header-cell">
        {{ displayedColumnsTitles.amount }}
      </th>
      <td mmCell *mmCellDef="let element" mmTextAlign="right" class="seller-txn__cell">
        {{ element.amount.amount | currency: element.amount.currency }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="vatRate">
      <th mmHeaderCell *mmHeaderCellDef mmTextAlign="right" class="seller-txn__header-cell">
        {{ displayedColumnsTitles.vatRate }}
      </th>
      <td mmCell *mmCellDef="let element" mmTextAlign="right" class="seller-txn__cell">{{ element.vatRate }}%</td>
    </ng-container>
    <tr mm-header-row *mmHeaderRowDef="displayedColumns"></tr>
    <tr mm-row *mmRowDef="let row; columns: displayedColumns" class="element-row"></tr>
  </table>
  <mm-table-pagination
    #paginator
    (page)="changePage($event)"
    [itemsPerPageLabel]="'PAGINATOR.ITEMS_PER_PAGE' | translate"
    [pageSizeOptions]="pageSizeOptions"
    [pageSizeSelectedOption]="pageLimit"
    [pageSizeTotal]="totalCount$ | async"
    [rangeLabel]="'PAGINATOR.OF_RANGE' | translate"
    [enableJumpToPage]="enableJumpToPage"
    [enablePageLinks]="enablePageLinks"
  >
  </mm-table-pagination>
</div>
