<div class="invoices-filter" [mmTestId]="'SELLER_INVOICES.INVOICES_FILTERS'">
  <mm-field class="invoices-filter__filter">
    <employee-app-seller-autocomplete
      [mmTestTarget]="'filter-by-seller'"
      [sellers]="sellers$ | async"
      (sellerSearched)="searchSellers($event)"
      (selected)="filterBySeller($event)"
    ></employee-app-seller-autocomplete>
  </mm-field>
  <mm-field class="invoices-filter__filter">
    <employee-app-invoice-period
      [mmTestTarget]="'filter-by-period'"
      [formControl]="periodControl"
      (periodChanged)="filterByPeriod($event)"
    ></employee-app-invoice-period>
  </mm-field>
  <mm-field class="invoices-filter__filter">
    <ng-select
      [items]="invoiceStatuses"
      [ngModel]="selectedStatus$ | async"
      [mmTestTarget]="'filter-by-status'"
      (change)="filterByStatus($event)"
      placeholder="{{ 'SELLER_ADMIN.SELLER_INVOICES.FILTER_BY_STATUS.NONE_PLACEHOLDER' | translate }}"
    ></ng-select>
  </mm-field>
</div>
