import { createFeatureSelector, createSelector } from '@ngrx/store';
import { SELLER_BALANCE_FEATURE_NAME } from '@accounting-app/modules/seller-balance/constants';
import { State } from '@accounting-app/modules/seller-balance/store';
import { SellerListState } from '@accounting-app/modules/seller-balance/store/seller-list/state';

export const getModuleFeatureState = createFeatureSelector(SELLER_BALANCE_FEATURE_NAME);

export const getSellerListState = createSelector(getModuleFeatureState, (state: State) => state.sellerList);

export const getSellerList = createSelector(getSellerListState, (state: SellerListState) => state && state.sellers);

export const getSelectedSellersCount = createSelector(
  getSellerListState,
  (state: SellerListState) => state && state.sellers.filter(seller => seller.selected).length,
);
