import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import {
  LoadInvoiceCancellationSuccess,
  LoadInvoiceCancellationFailure,
  InvoiceCancellationActionTypes
} from '@accounting-app/modules/seller-invoice-details/store/invoice-cancellation/invoice-cancellation.actions';
import { InvoiceCancellationService } from '@accounting-app/modules/seller-invoice-details/services/invoice-cancellation.service';

@Injectable()
export class InvoiceCancellationEffects {
  invoiceCancellation$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(InvoiceCancellationActionTypes.LOAD_INVOICE_CANCELLATION),
      switchMap(({ payload }) =>
        this.invoiceCancellationService.cancelMFInvoiceById(payload).pipe(
          map(() => new LoadInvoiceCancellationSuccess()),
          catchError((error) => {
            return of(new LoadInvoiceCancellationFailure({ error }));
          }),
        ),
      ),
    ),
  );

  constructor(private actions$: Actions, private invoiceCancellationService: InvoiceCancellationService) {}
}
