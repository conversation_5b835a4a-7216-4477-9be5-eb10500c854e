import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { Component, NgZone } from '@angular/core';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { Route, Router } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { EffectsModule } from '@ngrx/effects';
import { RouterStateSerializer, StoreRouterConnectingModule } from '@ngrx/router-store';
import { select, StoreModule } from '@ngrx/store';
import { deserialize } from 'serialize-ts/dist';
import { RouterCustomSerializer, routerReducer } from '~core/store';
import {
  TRANSACTIONS_JOURNAL_MOCK,
  TRANSACTIONS_JOURNAL_STATE_MOCK,
} from '@accounting-app/modules/transactions-journal/mocks';
import { TransactionsJournalService } from '@accounting-app/modules/transactions-journal/services/transactions-journal.service';
import { reducers } from '@accounting-app/modules/transactions-journal/store';
import { TRANSACTIONS_JOURNAL_FEATURE_NAME } from '@accounting-app/modules/transactions-journal/store/feature-name';
import * as fromJournal from '@accounting-app/modules/transactions-journal/store/journal';
import { BaseHttpInterceptor } from '~core/interceptors/base-http-interceptor';
import { environment } from '~env/environment';
import { ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK } from '~shared/mock';
import { ErrorResponse } from '~shared/model';
import { MockStore, provideMockStore } from '@ngrx/store/testing';

@Component({
  template: '',
})
class ComponentStub {}

const routePath = 'accounting/journal';
const routes: Route[] = [
  {
    path: routePath,
    component: ComponentStub,
  },
];

describe.skip('Transactions Journal Store integration', () => {
  let router: Router;
  let httpMock: HttpTestingController;
  let store: MockStore<fromJournal.TransactionsJournalState>;
  let service: TransactionsJournalService;
  let ngService: NgZone;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        RouterTestingModule.withRoutes(routes),
        StoreRouterConnectingModule.forRoot({
          stateKey: 'routerReducer',
        }),
        StoreModule.forRoot(routerReducer, {
          runtimeChecks: { strictStateImmutability: true, strictActionImmutability: true },
        }),
        StoreModule.forFeature(TRANSACTIONS_JOURNAL_FEATURE_NAME, reducers),
        EffectsModule.forRoot([]),
        EffectsModule.forFeature([fromJournal.TransactionsJournalEffects]),
      ],
      declarations: [ComponentStub],
      providers: [
        provideMockStore({ initialState: fromJournal.transactionsJournalInitialState }),
        { provide: HTTP_INTERCEPTORS, useClass: BaseHttpInterceptor, multi: true },
        {
          provide: RouterStateSerializer,
          useClass: RouterCustomSerializer,
        },
      ],
    });

    httpMock = TestBed.inject(HttpTestingController);
    router = TestBed.inject(Router);
    store = TestBed.inject(MockStore);
    service = TestBed.inject(TransactionsJournalService);
    ngService = TestBed.inject(NgZone);
  });

  afterEach(() => {
    router.initialNavigation();
  });

  describe('Load transactions journal', () => {
    const baseUrl = `${environment.svcEmployeeBaseUrl}/api/v1/employee/accounts/transactions`;

    describe('Load transactions journal success', () => {
      it('should return journal entity state', fakeAsync(() => {
        const expected = TRANSACTIONS_JOURNAL_STATE_MOCK;
        const url = `${baseUrl}?offset=4&limit=10`;

        ngService.run(() => router.navigate([routePath], { queryParams: { offset: 4, limit: 10 } }));
        tick();

        const req = httpMock.expectOne(url);

        expect(req.request.method).toBe('GET');
        req.flush(TRANSACTIONS_JOURNAL_MOCK);
        tick();

        store
          .pipe(select(fromJournal.getTransactionsJournalState))
          .subscribe(actual => expect(actual).toEqual(expected));
        tick();
      }));

      it('should request transactions with default request params', fakeAsync(() => {
        const spy = jest.spyOn(service, 'getTransactions');

        ngService.run(() => router.navigate([routePath]));
        tick();

        expect(spy).toHaveBeenCalledWith({ offset: 0, limit: 15 });
      }));
    });

    describe('Load transactions journal failure', () => {
      it('should return an error when load transactions', fakeAsync(() => {
        const expected: fromJournal.TransactionsJournalState = {
          entities: {},
          ids: [],
          totalCount: 0,
          loading: false,
          error: deserialize(ERROR_RESPONSE_MOCK, ErrorResponse),
        };
        const url = `${baseUrl}?offset=0&limit=15`;

        ngService.run(() => router.navigate([routePath]));
        tick();

        const req = httpMock.expectOne(url);

        req.flush(ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK);
        tick();

        store
          .pipe(select(fromJournal.getTransactionsJournalState))
          .subscribe(actual => expect(actual).toEqual(expected));
        tick();
      }));
    });
  });
});
