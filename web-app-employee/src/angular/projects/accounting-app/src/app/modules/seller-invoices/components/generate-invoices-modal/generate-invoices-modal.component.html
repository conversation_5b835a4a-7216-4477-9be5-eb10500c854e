<div class="invoice-modal" [mmTestId]="'SELLER_INVOICES.GENERATE_INVOICES_MODAL'">
  <h4 class="invoice-modal__title">{{ 'SELLER_ADMIN.GENERATE_INVOICES_MODAL.TITLE' | translate }}</h4>
  <button mm-button [color]="'ghost'" [size]="'small'" class="invoice-modal__close-btn" (click)="close()">
    <mm-icon [name]="'cross'"></mm-icon>
  </button>
  <form
    class="invoice-modal__form invoice-form"
    [formGroup]="invoiceForm"
    [errors]="invalidParams$ | async"
    (submit)="onSubmit()"
    novalidate
  >
    <mm-field class="invoice-form__field">
      <employee-app-invoice-period
        formControlName="period"
        (periodChanged)="onPeriodChange($event)"
        [mmTestTarget]="'invoices-period'"
      ></employee-app-invoice-period>
    </mm-field>
    <mm-field class="invoice-form__field">
      <employee-app-seller-autocomplete
        [mmTestTarget]="'invoices-seller'"
        formControlName="sellerId"
        [sellers]="sellers$ | async"
        (sellerSearched)="searchSellers($event)"
        (selected)="loadStatistic()"
      ></employee-app-seller-autocomplete>
    </mm-field>
    <mm-field *ngIf="(invoicesStats$ | async)?.invoicesCount as invoicesCount">
      <mm-checkbox
        [mmTestTarget]="'overwrite-existing'"
        formControlName="overwriteExisting"
        class="invoice-form-checkbox"
      >
        <span>Overwrite existing</span>
        <br />
        <small class="invoice-form-checkbox__warning"> {{ invoicesCount }} existing invoices will be affected </small>
      </mm-checkbox>
    </mm-field>
    <mm-field *ngIf="(invoicesStats$ | async)?.sentInvoicesCount as sentInvoicesCount">
      <mm-checkbox [mmTestTarget]="'overwrite-sent'" formControlName="overwriteSent" class="invoice-form-checkbox">
        <span>Overwrite already sent</span>
        <br />
        <small class="invoice-form-checkbox__warning">
          {{ sentInvoicesCount }} already sent invoices will be affected
        </small>
      </mm-checkbox>
    </mm-field>
    <div class="invoice-form__actions">
      <button
        mm-button
        type="submit"
        class="invoice-form__submit-btn"
        [mmTestTarget]="'submit-button'"
        [disabled]="isGenerating$ | async"
      >
        {{ 'SELLER_ADMIN.GENERATE_INVOICES_MODAL.FORM.SUBMIT_BUTTON.TITLE' | translate }}
      </button>
    </div>
  </form>
</div>
