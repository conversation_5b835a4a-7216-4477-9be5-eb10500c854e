import { Action } from '@ngrx/store';
import { ErrorResponse } from '~shared/model';
import { Seller } from '@accounting-app/modules/seller-balance/models';

export enum SellerListActionTypes {
  LOAD_LIST = '[Seller List] Load seller list',
  LOAD_LIST_SUCCESS = '[Seller List] Load seller list success',
  LOAD_LIST_FAILURE = '[Seller List] Load seller list failure',
  RESET_SELLER_SELECTION = '[Seller List] Seller list reset',
  UPDATE_SELLER_SELECTION = '[Seller List] Seller list update selection',
}

export class LoadSellerList implements Action {
  readonly type = SellerListActionTypes.LOAD_LIST;

  constructor(public payload: { date: string }) {}
}

export class LoadSellerListSuccess implements Action {
  readonly type = SellerListActionTypes.LOAD_LIST_SUCCESS;

  constructor(public payload: { sellers: Seller[] }) {}
}

export class LoadSellerListFailure implements Action {
  readonly type = SellerListActionTypes.LOAD_LIST_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export class ResetSellerSelection implements Action {
  readonly type = SellerListActionTypes.RESET_SELLER_SELECTION;
}

export class UpdateSellerSelection implements Action {
  readonly type = SellerListActionTypes.UPDATE_SELLER_SELECTION;

  constructor(public payload: { sellerIds: string[] }) {}
}

export type SellerListActionUnion =
  | LoadSellerList
  | LoadSellerListSuccess
  | LoadSellerListFailure
  | ResetSellerSelection
  | UpdateSellerSelection;
