import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { isEqual } from 'lodash';
import { of } from 'rxjs';
import { catchError, debounceTime, distinctUntilChanged, map, switchMap } from 'rxjs/operators';
import { Seller } from '../../models';
import { SellersService } from '../../services/sellers.service';
import { searchSellers, searchSellersFailure, searchSellersSuccess } from './actions/search-sellers.actions';
import { ErrorResponse } from '~shared/model';
import { DEBOUNCE_TIME } from '../../constants';

@Injectable()
export class SellersEffects {
  searchSellers$ = createEffect(() =>
    this.actions$.pipe(
      ofType(searchSellers),
      debounceTime(DEBOUNCE_TIME),
      distinctUntilChanged(isEqual),
      switchMap(({ query }) =>
        this.sellersService.searchSellers({ filter: { shopName: query } }).pipe(
          map((sellers: Seller[]) => searchSellersSuccess({ sellers })),
          catchError((error: ErrorResponse) => of(searchSellersFailure({ error }))),
        ),
      ),
    ),
  );

  constructor(private actions$: Actions, private sellersService: SellersService) {}
}
