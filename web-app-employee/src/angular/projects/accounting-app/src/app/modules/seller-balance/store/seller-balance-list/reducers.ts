import {
  sellerBalanceInitialState,
  SellerBalanceState,
} from '@accounting-app/modules/seller-balance/store/seller-balance-list/state';
import {
  SellerBalanceListActionTypes,
  SellerBalanceListActionUnion,
} from '@accounting-app/modules/seller-balance/store/seller-balance-list/actions';

export function sellerBalanceReducer(
  state = sellerBalanceInitialState,
  action: SellerBalanceListActionUnion,
): SellerBalanceState {
  switch (action.type) {
    case SellerBalanceListActionTypes.LOAD_SELLER_BALANCE_LIST:
    case SellerBalanceListActionTypes.DOWNLOAD_SELLER_BALANCE:
      return { ...state, loading: true };
    case SellerBalanceListActionTypes.LOAD_SELLER_BALANCE_LIST_SUCCESS:
      return {
        ...state,
        loading: false,
        payload: { ...action.payload.sellerBalance },
        error: null,
      };
    case SellerBalanceListActionTypes.LOAD_SELLER_BALANCE_LIST_FAILURE:
      return {
        ...state,
        loading: false,
        payload: {
          items: [],
          totalCount: 0,
          totals: null,
        },
        error: action.payload.error,
      };
    case SellerBalanceListActionTypes.DOWNLOAD_SELLER_BALANCE_SUCCESS:
      return {
        ...state,
        loading: false,
      };
    case SellerBalanceListActionTypes.DOWNLOAD_SELLER_BALANCE_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload.error,
      };
    default:
      return state;
  }
}
