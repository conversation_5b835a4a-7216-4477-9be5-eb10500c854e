import { environment } from '~env/environment';

export const REPORT_DATE_FORMAT = 'yyyy-MM-dd';
export const ACCOUNTING_API = `${environment.svcAccountingBaseUrl}/api/internal/reports/`;
export const TABLE_HEADER = 'table-header';
export const TABLE_CELL = 'table-cell';
export const PAYOUTS_DATATABLE_CONFIG = {
  paging: true,
  info: true,
  processing: false,
  searching: true,
  serverSide: true,
  pagingType: 'simple',
  oLanguage: {
    oPaginate: {
      sNext: '>',
      sPrevious: '<',
    },
    sInfo: '_START_ of _END_ out of _TOTAL_ Records',
    sInfoFiltered: '',
  },
  ajax: {
    url: `${environment.svcAccountingBaseUrl}/api/internal/payout/datatable`,
    method: 'post',
    data: {
      _dt: 'dt',
    },
  },
  order: [
    [0, 'desc'],
    [1, 'desc'],
    [3, 'asc'],
    [4, 'desc'],
  ],
  columnDefs: [
    {
      targets: 0,
      data: 'name',
      title: 'Seller Name',
      className: TABLE_HEADER,
      searchable: true,
      sortable: true,
    },
    {
      targets: 1,
      data: 'toDate',
      title: 'Date',
      className: TABLE_HEADER,
      searchable: true,
      sortable: true,
    },
    {
      targets: 2,
      data: 'type',
      title: 'Type',
      className: TABLE_HEADER,
      searchable: false,
      visible: false,
    },
    {
      targets: 3,
      data: 'metadata',
      title: 'Payout Amount',
      className: TABLE_HEADER,
      searchable: false,
      sortable: true,
    },
    {
      targets: 4,
      data: 'id',
      title: 'Action',
      className: TABLE_HEADER,
      searchable: false,
      sortable: false,
    },
  ],
  dom: '<"search-filter "f>' + 'rt' + '<"table-options" ip>',
  pageLength: 10,
  searchCols: [null, null, { search: 'payout-overview' }],
  aoColumns: [
    { sClass: TABLE_CELL },
    { sClass: TABLE_CELL },
    { sClass: TABLE_CELL },
    { sClass: TABLE_CELL },
    { sClass: TABLE_CELL },
  ],
};
