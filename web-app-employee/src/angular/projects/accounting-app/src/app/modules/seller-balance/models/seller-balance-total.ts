import { Field, Model, Name } from 'serialize-ts/dist';
import { Money } from '~shared/model';

@Model()
export class SellerBalanceTotal {
  @Field()
  @Name('payments.adyen.balance')
  adyenBalance: Money;

  @Field()
  @Name('payments.adyen.payableBalance')
  adyenPayableBalance: Money;

  @Field()
  @Name('payments.paypal.balance')
  paypalBalance: Money;

  @Field()
  @Name('payments.paypal.payableBalance')
  paypalPayableBalance: Money;
}
