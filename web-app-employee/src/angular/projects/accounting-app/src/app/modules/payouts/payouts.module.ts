import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { PayoutsListComponent } from './components/payouts-list/payouts-list.component';
import { PayoutsRoutingModule } from './payouts-routing.module';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  declarations: [PayoutsListComponent],
  imports: [CommonModule, PayoutsRoutingModule, TranslateModule],
  providers: [DatePipe],
})
export class PayoutsModule {}
