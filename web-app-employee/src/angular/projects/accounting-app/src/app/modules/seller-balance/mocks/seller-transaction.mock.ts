import * as faker from 'faker';
import { deserialize } from 'serialize-ts';
import {
  SellerInfo,
  SellerTransaction,
  SellerTransactionPaginatedResponse,
} from '@accounting-app/modules/seller-balance/models';

const totalCount = 20;
const currency = 'EUR';
const amount = () => ({
  amount: `${faker.random.number()}`,
  currency,
});

export const SELLER_TRANSACTION_MOCK: SellerTransactionPaginatedResponse<SellerTransaction, SellerInfo> = {
  items: Array(totalCount)
    .fill({})
    .map(() => {
      return {
        orderNumber: faker.random.word(),
        transactionDate: faker.date.recent(),
        pspReference: faker.random.word(),
        paymentProvider: faker.random.word(),
        salesChannel: faker.random.word(),
        totalAmount: amount(),
        sellerPortion: amount(),
        commissionCharged: amount(),
      };
    })
    .map(e => deserialize(e, SellerTransaction)),
  sellerInfo: {
    name: faker.random.word(),
    userFriendlyId: `SA21-${faker.random.number()}`,
  },
  totalCount,
  totalBalanceWithMM: amount(),
};
