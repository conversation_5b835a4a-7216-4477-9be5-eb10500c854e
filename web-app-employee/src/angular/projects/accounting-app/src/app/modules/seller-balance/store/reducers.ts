import { ActionReducerMap } from '@ngrx/store';
import { State } from '@accounting-app/modules/seller-balance/store';
import { sellerBalanceReducer } from '@accounting-app/modules/seller-balance/store/seller-balance-list';
import { sellerListReducer } from '@accounting-app/modules/seller-balance/store/seller-list/reducers';
import { sellerTransactionReducer } from '@accounting-app/modules/seller-balance/store/seller-transaction/reducers';

export const reducers: ActionReducerMap<State> = {
  sellerBalance: sellerBalanceReducer,
  sellerList: sellerListReducer,
  sellerTransaction: sellerTransactionReducer,
};
