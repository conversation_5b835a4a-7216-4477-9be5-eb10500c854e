import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';
import { downloadFile } from '~shared/utils/download.utils';
import { ACCOUNTING_API } from '../constants/defines';

@Injectable({
  providedIn: 'root',
})
export class PayoutsService {
  constructor(private http: HttpClient) {}

  loadPayoutsReport(reportId: string): Observable<{}> {
    const url = `${ACCOUNTING_API}${reportId}/download-link`;
    return this.http.get(url);
  }

  public downloadReport(url: string, fileName: string) {
    return this.http.get(url, { responseType: 'arraybuffer' }).pipe(map(file => downloadFile(file, `${fileName}.csv`)));
  }
}
