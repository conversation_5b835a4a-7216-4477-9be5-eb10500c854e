import { createFeatureSelector, createSelector } from '@ngrx/store';
import { SELLER_INVOICE_DETAILS_FEATURE_NAME } from '@accounting-app/modules/seller-invoice-details/store/feature-name';
import { InvoiceCorrectionState } from "@accounting-app/modules/seller-invoice-details/store/invoice-correction/invoice-correction.state";
import { State } from '@accounting-app/modules/seller-invoice-details/store/state';

const featureModuleState = createFeatureSelector(SELLER_INVOICE_DETAILS_FEATURE_NAME);

export const selectInvoiceCorrectionState = createSelector(featureModuleState, (state: State) => state.invoiceCorrection);

export const selectInvoiceCorrection = createSelector(
  selectInvoiceCorrectionState,
  (state: InvoiceCorrectionState) => state,
);
