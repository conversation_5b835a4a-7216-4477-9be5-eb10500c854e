import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { SellerBalanceListComponent } from './seller-balance-list.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MmFormFieldModule, MmIconModule, MmTableModule, MmTablePaginationModule } from '@metromarkets/components-17';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { TranslateFakeLoader, TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { reducers } from '../../store';
import { QaLocatorsModule } from '@metromarkets/sdk-17';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import * as fromSellerBalance from '@accounting-app/modules/seller-balance/store/seller-balance-list';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe.skip('SellerBalanceListComponent', () => {
  let component: SellerBalanceListComponent;
  let fixture: ComponentFixture<SellerBalanceListComponent>;
  let store: MockStore;

  function iconFactory() {
    return {};
  }

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SellerBalanceListComponent],
      providers: [provideMockStore({ initialState: fromSellerBalance.sellerBalanceInitialState })],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [
        BrowserAnimationsModule,
        MmTableModule,
        MatTableModule,
        HttpClientTestingModule,
        MmIconModule.forRoot(iconFactory),
        MatIconModule,
        TranslateModule,
        RouterTestingModule.withRoutes([]),
        TranslateModule.forRoot({
          loader: {
            provide: TranslateLoader,
            useClass: TranslateFakeLoader,
          },
        }),
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
        StoreModule.forFeature('sellerBalance', reducers.sellerBalance),
        MmTablePaginationModule,
        QaLocatorsModule.forRoot({
          shouldSetupLocators: true,
        }),
        MmFormFieldModule,
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SellerBalanceListComponent);
    component = fixture.componentInstance;
    store = TestBed.inject(MockStore);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
