import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import {
  MmAlertModule,
  MmButtonModule,
  MmDialogModule,
  MmFormFieldModule,
  MmSpinnerModule,
  MmTableModule
} from '@metromarkets/components-17';
import { QaLocatorsModule } from '@metromarkets/sdk-17';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { ConfirmDialogModule } from '~shared/modules/confirm-dialog/confirm-dialog.module';

import { SellerInvoiceDetailsComponent } from './components/seller-invoice-details.component';
import { SellerInvoiceDetailsRoutingModule } from './seller-invoice-details-routing.module';
import { reducers } from './store';
import {
  SELLER_INVOICE_DETAILS_FEATURE_NAME,
  INVOICE_CANCELLATION_FEATURE_NAME
} from './store/feature-name';
import { InvoiceDetailsEffects } from './store/invoice-details';
import { InvoiceCancellationEffects } from './store/invoice-cancellation';
import { BackButtonModule } from "~shared/modules/back-button/back-button.module";
import { InfoIconTooltipComponent } from "@employee-app/shared/components/info-icon-tooltip/info-icon-tooltip.component";
import { InvoiceCancellationDialogComponent } from '@accounting-app/modules/seller-invoice-details/components/invoice-cancellation-dialog/invoice-cancellation-dialog.component';
import { ReactiveFormsModule } from "@angular/forms";
import { InvoiceCorrectionDialogComponent } from "@accounting-app/modules/seller-invoice-details/components/invoice-correction-dialog/invoice-correction-dialog.component";
import {
  InvoiceCorrectionEffects
} from "@accounting-app/modules/seller-invoice-details/store/invoice-correction/invoice-correction.effects";
import { LoaderComponent } from "@accounting-app/modules/seller-invoice-details/components/loader/loader.component";

@NgModule({
  declarations: [
    SellerInvoiceDetailsComponent,
    InvoiceCorrectionDialogComponent,
    InvoiceCancellationDialogComponent,
    LoaderComponent
  ],
  imports: [
    CommonModule,
    SellerInvoiceDetailsRoutingModule,
    StoreModule.forFeature(SELLER_INVOICE_DETAILS_FEATURE_NAME, reducers),
    EffectsModule.forFeature([InvoiceDetailsEffects, InvoiceCorrectionEffects]),
    StoreModule.forFeature(INVOICE_CANCELLATION_FEATURE_NAME, reducers),
    EffectsModule.forFeature([InvoiceDetailsEffects]),
    EffectsModule.forFeature([InvoiceCancellationEffects]),
    MmTableModule,
    MmButtonModule,
    QaLocatorsModule,
    TranslateModule,
    MmAlertModule,
    MmDialogModule,
    ConfirmDialogModule,
    BackButtonModule,
    TranslateModule,
    InfoIconTooltipComponent,
    ReactiveFormsModule,
    MmSpinnerModule,
    MmFormFieldModule
  ],
})
export class SellerInvoiceDetailsModule {}
