import { EntityState } from '@ngrx/entity';
import { SellerInvoice } from '@accounting-app/modules/seller-invoices/models';
import { invoicesAdapter } from '@accounting-app/modules/seller-invoices/store/invoices/invoices.adapter';
import { ErrorResponse } from '~shared/model';

export interface InvoicesState extends EntityState<SellerInvoice> {
  error: ErrorResponse;
  totalCount: number;
  isGenerating: boolean;
}

export const invoicesInitialState: InvoicesState = invoicesAdapter.getInitialState({
  error: null,
  totalCount: 0,
  isGenerating: false,
});
