import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { select, Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { filter, takeUntil, tap } from 'rxjs/operators';
import { SellerInvoiceDetails } from '@accounting-app/modules/seller-invoice-details/models';
import * as fromInvoice from '@accounting-app/modules/seller-invoice-details/store/invoice-details';
import { ACCOUNTING_ROUTE, SELLER_INVOICES_ROUTE } from '~shared/constants';
import { loadInvoiceDetails } from '../store/invoice-details/actions/load-invoice-details.actions';
import { getCurrencyHtmlCode } from "~shared/utils/common";
import { InvoiceCancellationDialogComponent } from '@accounting-app/modules/seller-invoice-details/components/invoice-cancellation-dialog/invoice-cancellation-dialog.component';
import {
  InvoiceCorrectionDialogComponent
} from "@accounting-app/modules/seller-invoice-details/components/invoice-correction-dialog/invoice-correction-dialog.component";
import {
  MmDialogService,
  MmDialogSize,
  MmAlertMode,
  MmAlertSize,
  MmAlertType,
  MmAlertService
} from '@metromarkets/components-17';
import { IN_DUNNING } from "@accounting-app/modules/seller-invoice-details/constants"

@Component({
  selector: 'employee-app-seller-invoice-details',
  templateUrl: './seller-invoice-details.component.html',
  styleUrls: ['./seller-invoice-details.component.scss'],
})
export class SellerInvoiceDetailsComponent implements OnInit, OnDestroy {

  public invoice$: Observable<SellerInvoiceDetails>;
  public getCurrencyHtmlCode = getCurrencyHtmlCode;
  public currentInvoice: SellerInvoiceDetails;
  public displayedColumns = ['number', 'description', 'tax', 'total', 'date'];

  private readonly destroy$ = new Subject<void>();
  protected isLoading: boolean = false;
  protected shouldRefreshInvoice: boolean = false;

  constructor(
    private store: Store<fromInvoice.InvoiceDetailsState>,
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MmDialogService,
    private alertService: MmAlertService,
  ) {
  }

  ngOnInit() {
    this.route.paramMap
      .pipe(
        filter(param => !!param),
        takeUntil(this.destroy$),
      )
      .subscribe(param => this.loadInvoice(param.get('id')));

    this.invoice$ = this.store.pipe(
      select(fromInvoice.selectInvoiceDetails),
      filter(invoice => !!invoice),
      tap(invoice => {
        this.currentInvoice = invoice;
        this.isLoading = false;
        this.shouldRefreshInvoice = false;
      })
    );
  }

  loadInvoice(id: string) {
    this.isLoading = true;
    this.store.dispatch(loadInvoiceDetails({id}));
  }

  navigateToInvoiceList(): void {
    this.router.navigate([ACCOUNTING_ROUTE, SELLER_INVOICES_ROUTE]);
  }

  ngOnDestroy(): void {
    this.destroy$.next(void 0);
    this.destroy$.complete();
  }

  openCancelInvoice(): void {
    const dialogConfig = {
      dialogSize: MmDialogSize.large,
      data: { invoice: this.currentInvoice },
    };

    const dialogRef = this.dialog.open(InvoiceCancellationDialogComponent, dialogConfig);
    dialogRef.afterClosed().subscribe((result) => {
      if (result === "cancellationSuccessful") {
        this.notifyOfChanges("Invoice successfully cancelled!");
        this.loadInvoice(this.currentInvoice.id);
      }
    });
  }

  openCorrectInvoiceDialog(): void {
    const dialogConfig = {
      dialogSize: MmDialogSize.fit,
      data: {
        invoice: this.currentInvoice,
        invoiceDetailsRefresh: () => {
          this.shouldRefreshInvoice = true;
        }
      },
      maxHeight: '99%'
    };

    const dialogRef = this.dialog.open(InvoiceCorrectionDialogComponent, dialogConfig);
    dialogRef.afterClosed().subscribe((message: string) => {
      this.notifyOfChanges(message);
      if (this.shouldRefreshInvoice) {
        this.loadInvoice(this.currentInvoice.id);
      }
    });
  }

  disableCancellation(): boolean {
    return this.currentInvoice.membershipFee?.cancelledAt != null
      || this.currentInvoice.membershipFee?.status === IN_DUNNING
      || this.currentInvoice.membershipFee?.deletedAt != null
      || !!this.currentInvoice.correctionReportId;
  }

  getDisabledCancellationTooltipText(): string {
    const { membershipFee } = this.currentInvoice;

    if (membershipFee?.cancelledAt) return 'Invoice is already cancelled.';
    if (membershipFee?.status === IN_DUNNING) return 'Invoice is in dunning.';
    if (membershipFee?.deletedAt) return 'Membership Fee is deleted.';
    if (this.currentInvoice.correctionReportId) return "Membership fee has been corrected.";

    return '';
  }

  notifyOfChanges(message: string): void {
    if (message.length) {
      this.alertService.addAlert({
        type: MmAlertType.success,
        title: "",
        message: message,
        size: MmAlertSize.large,
        mode: MmAlertMode.banner,
        target: '#seller-invoice-details-alert-target',
        duration: 5000,
        disableCloseIcon: true
      });
    }
  }
}
