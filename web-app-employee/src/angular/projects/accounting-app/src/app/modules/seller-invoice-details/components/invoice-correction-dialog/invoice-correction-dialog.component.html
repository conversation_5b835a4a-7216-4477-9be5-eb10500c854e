<form
  [name]="sellerInvoiceForm"
  [formGroup]="this.sellerInvoiceForm"
  test-target="seller-invoice-form"
  (submit)="onSubmit()"
>
  <loader [isVisible]="this.isLoading$"></loader>
  <div class="section-title">
    <h3>{{ 'ACCOUNTING.REPORTS.INVOICE_CORRECTION.SELLER_TITLE' | translate }}</h3>
  </div>
  <div
    class="section-result {{ this.invoiceCorrectionResult ? '' : 'failed' }}"
    *ngIf="this.message">{{ message }}
  </div>
  <div class="section-content">
    <table>
      <tr>
        <td>{{ 'ACCOUNTING.REPORTS.INVOICE_CORRECTION.SELLER.COMPANY_NAME' | translate }}:</td>
        <td>{{ this.data.invoice.shopName }}</td>
      </tr>
      <tr>
        <td>{{ 'ACCOUNTING.REPORTS.INVOICE_CORRECTION.SELLER.COMPANY_ADDRESS' | translate }}:</td>
        <td>{{ this.data.invoice.address }}</td>
      </tr>
      <tr>
        <td>{{ 'ACCOUNTING.REPORTS.INVOICE_CORRECTION.SELLER.ID' | translate }}:</td>
        <td>{{ this.data.invoice.sellerId }}</td>
      </tr>
      <tr>
        <td>{{ 'ACCOUNTING.REPORTS.INVOICE_CORRECTION.VAT.NUMBER' | translate }}:</td>
        <td>{{ this.data.invoice.taxId }}</td>
      </tr>
    </table>
    <div class="section-download">
      <a href="{{ this.data.invoice.downloadLink }}" target="_blank">
        <button
          mm-button
          type="button"
          class="mm-button--primary mm-button--medium mm-button"
        >Download invoice<br/>to be corrected</button>
      </a>
    </div>
  </div>
  <div class="section-title">
    <h3>{{ 'ACCOUNTING.REPORTS.INVOICE_CORRECTION.INVOICE_TITLE' | translate }}</h3>
  </div>
  <div class="section-content">
    <table>
      <tr>
        <td>Netto, EUR:</td>
        <td>{{ parseInt(this.data.invoice.netAmount.amount) / 100 }} &euro;</td>
      </tr>
      <tr>
        <td>USr., %:</td>
        <td>{{ this.data.invoice.vatRate }} %</td>
      </tr>
      <tr>
        <td>Brutto, EUR:</td>
        <td>{{ parseInt(this.data.invoice.grossAmount.amount) / 100 }} &euro;</td>
      </tr>
      <tr>
        <td>USt., EUR:</td>
        <td>{{ parseInt(this.data.invoice.vatAmount.amount) / 100 }} &euro;</td>
      </tr>
    </table>
  </div>
  <div *ngIf="!this.isInvoiceCorrected()">
    <div class="label-input">{{ 'ACCOUNTING.REPORTS.INVOICE_CORRECTION.REASON.LABEL' | translate }}</div>
    <input
      mmInput
      type="text"
      class="mm-input"
      placeholder="{{ 'ACCOUNTING.REPORTS.INVOICE_CORRECTION.REASON.PLACEHOLDER' | translate }}"
      required="true"
      maxlength="200"
      formControlName="reason"
      name="reason"
    />
    <mm-error
      *ngIf="this.sellerInvoiceForm.invalid"
      fieldName="reason"
    >{{ 'ACCOUNTING.REPORTS.INVOICE_CORRECTION.REASON.REQUIRED' | translate }}
    </mm-error>
  </div>
  <div class="section-buttons">
    <button
      mm-button
      type="button"
      class="mm-button--ghost mm-button--medium mm-button"
      (click)="close()"
    >{{ 'ACCOUNTING.REPORTS.INVOICE_CORRECTION.CANCEL.LABEL' | translate }}
    </button>
    <button
      mm-button
      type="submit"
      class="mm-button--ghost mm-button--medium mm-button"
      *ngIf="!this.isInvoiceCorrected()"
    >{{ 'ACCOUNTING.REPORTS.INVOICE_CORRECTION.CONFIRM.LABEL' | translate }}
    </button>
  </div>
</form>
