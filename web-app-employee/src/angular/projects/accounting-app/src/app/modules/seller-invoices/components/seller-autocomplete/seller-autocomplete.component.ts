import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { ControlValueAccessor, UntypedFormControl, NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Seller } from '../../models';

@Component({
  selector: 'employee-app-seller-autocomplete',
  templateUrl: './seller-autocomplete.component.html',
  providers: [{ provide: NG_VALUE_ACCESSOR, useExisting: SellerAutocompleteComponent, multi: true }],
})
export class SellerAutocompleteComponent implements OnInit, OnDestroy, ControlValueAccessor {
  @Input() sellers: Seller[];

  @Output() sellerSearched = new EventEmitter<string>();
  @Output() selected = new EventEmitter<Seller>();

  sellerFormControl: UntypedFormControl;

  private readonly destroy$ = new Subject<void>();

  constructor() {
    this.sellerFormControl = new UntypedFormControl();
  }

  onChange = (val: any) => {};

  onTouched = () => {};

  ngOnInit(): void {
    this.sellerFormControl.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(val => this.onChange(val));
  }

  displayShopName(seller: Seller): string | undefined {
    return seller ? seller.shopName : undefined;
  }

  searchSellers(query: string): void {
    this.sellerSearched.emit(query);
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  writeValue(value: Seller): void {
    if (value) {
      this.sellerFormControl.setValue(value);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next(void 0);
    this.destroy$.complete();
  }

  onSelect({ option }: MatAutocompleteSelectedEvent) {
    this.selected.emit(option.value);
  }
}
