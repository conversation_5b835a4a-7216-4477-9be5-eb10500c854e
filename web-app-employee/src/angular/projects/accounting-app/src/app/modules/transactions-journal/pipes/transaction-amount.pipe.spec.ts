import { TransactionJournal, TransactionTypes } from '@accounting-app/modules/transactions-journal/models';
import { TransactionAmountPipe } from './transaction-amount.pipe';

describe('TransactionAmountPipe', () => {
  const pipe = new TransactionAmountPipe();
  const transaction: TransactionJournal = {
    type: TransactionTypes.Transfer,
    amount: '200',
  } as TransactionJournal;

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return an empty string when provide invalid value', () => {
    expect(pipe.transform(null, TransactionTypes.Accuran)).toBe('');
    expect(pipe.transform(undefined, TransactionTypes.Accuran)).toBe('');
  });

  it('should return transaction amount', () => {
    const expected = transaction.amount;
    expect(pipe.transform(transaction, TransactionTypes.Transfer)).toBe(expected);
  });

  it("should return empty string when provide type other than transaction's", () => {
    const expected = '';
    expect(pipe.transform(transaction, TransactionTypes.Accuran)).toBe(expected);
  });
});
