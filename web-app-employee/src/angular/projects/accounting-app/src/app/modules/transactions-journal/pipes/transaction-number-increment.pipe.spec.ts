import { TransactionNumberIncrementPipe } from './transaction-number-increment.pipe';

describe('TransactionNumberIncrementPipe', () => {
  const pipe = new TransactionNumberIncrementPipe();

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return provided value when provide invalid value', () => {
    expect(pipe.transform(undefined)).toBeUndefined();
    expect(pipe.transform(null)).toBeNull();
  });

  it('should calculated number', () => {
    expect(pipe.transform(5)).toBe(5);
    expect(pipe.transform(5, 2, 10)).toBe(25);
  });
});
