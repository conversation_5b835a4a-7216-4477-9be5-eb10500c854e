import { createFeatureSelector, createSelector } from '@ngrx/store';
import { State } from '@accounting-app/modules/transactions-journal/store';
import { TRANSACTIONS_JOURNAL_FEATURE_NAME } from '@accounting-app/modules/transactions-journal/store/feature-name';
import { transactionsJournalAdapter } from '@accounting-app/modules/transactions-journal/store/journal/transactions-journal.adapter';
import { TransactionsJournalState } from '@accounting-app/modules/transactions-journal/store/journal/transactions-journal.state';

const getFeatureState = createFeatureSelector(TRANSACTIONS_JOURNAL_FEATURE_NAME);

export const getTransactionsJournalState = createSelector(getFeatureState, (state: State) => state.transactionsJournal);

export const { selectAll } = transactionsJournalAdapter.getSelectors();

export const getTransactionsJournal = createSelector(getTransactionsJournalState, selectAll);

export const getTransactionsTotal = createSelector(
  getTransactionsJournalState,
  (state: TransactionsJournalState) => state.totalCount,
);
