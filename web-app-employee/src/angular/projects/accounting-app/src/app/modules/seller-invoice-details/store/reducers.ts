import { ActionReducerMap } from '@ngrx/store';
import { invoiceDetailsReducer } from './invoice-details';
import { invoiceCancellationReducer } from './invoice-cancellation';
import { State } from '@accounting-app/modules/seller-invoice-details/store/state';
import { invoiceCorrectionReducer } from "@accounting-app/modules/seller-invoice-details/store/invoice-correction";

export const reducers: ActionReducerMap<State> = {
  invoiceDetails: invoiceDetailsReducer,
  invoiceCorrection: invoiceCorrectionReducer,
  invoiceCancellation: invoiceCancellationReducer,
};
