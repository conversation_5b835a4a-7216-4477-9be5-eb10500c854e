import { PadStartPipe } from './pad-start.pipe';

describe('PadStartPipe', () => {
  const pipe = new PadStartPipe();

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return an empty string when provide invalid value', () => {
    expect(pipe.transform(null, 5)).toBe('');
    expect(pipe.transform(undefined, 5)).toBe('');
    expect(pipe.transform('', 5)).toBe('');
    expect(pipe.transform('1', null)).toBe('');
    expect(pipe.transform('1', undefined)).toBe('');
  });

  it('should return string with one zero before the provided value', () => {
    const expected = '01';

    expect(pipe.transform('1', 2, '0')).toBe(expected);
    expect(pipe.transform(1, 2, '0')).toBe(expected);
  });

  it('should return the same value as provided when fillString is not provided', () => {
    expect(pipe.transform('1', 2)).toBe('1');
  });
});
