import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { waitForAsync, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { EffectsModule } from '@ngrx/effects';
import { select, Store, StoreModule } from '@ngrx/store';
import { deserialize } from 'serialize-ts/dist';
import { ORGANIZATIONS_MOCK, ORGANIZATIONS_STATE_MOCK } from '@accounting-app/modules/organizations/mocks';
import { reducers } from '@accounting-app/modules/organizations/store';
import { ORGANIZATIONS_FEATURE_NAME } from '@accounting-app/modules/organizations/store/feature-name';
import { LoadOrganizationsActions } from '@accounting-app/modules/organizations/store/organizations/actions';
import { OrganizationsEffects } from '@accounting-app/modules/organizations/store/organizations/organizations.effects';
import { selectOrganizationsState } from '@accounting-app/modules/organizations/store/organizations/organizations.selectors';
import { OrganizationsState } from '@accounting-app/modules/organizations/store/organizations/organizations.state';
import { BaseHttpInterceptor } from '~core/interceptors/base-http-interceptor';
import { ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK } from '~shared/mock';
import { ErrorResponse } from '~shared/model';

describe('Organizations Store Integration', () => {
  const host = window['config'].serviceBaseUrls.employee;

  let httpMock: HttpTestingController;
  let store: Store<OrganizationsState>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        RouterTestingModule,
        StoreModule.forRoot({}, { runtimeChecks: { strictStateImmutability: true, strictActionImmutability: true } }),
        StoreModule.forFeature(ORGANIZATIONS_FEATURE_NAME, reducers),
        EffectsModule.forRoot([]),
        EffectsModule.forFeature([OrganizationsEffects]),
      ],
      providers: [{ provide: HTTP_INTERCEPTORS, useClass: BaseHttpInterceptor, multi: true }],
    });

    httpMock = TestBed.inject(HttpTestingController);
    store = TestBed.inject(Store);
  });

  describe('loadOrganizations', () => {
    const offset = 0;
    const limit = 30;
    const url = `${host}/api/v1/employee/organizations?offset=${offset}&limit=${limit}`;
    const action = LoadOrganizationsActions.loadOrganizations({ params: { offset, limit } });

    describe('loadOrganizationsSuccess', () => {
      it('should make a GET api call and return state with organizations', waitForAsync(() => {
        store.dispatch(action);

        const req = httpMock.expectOne(url);

        expect(req.request.method).toBe('GET');

        req.flush(ORGANIZATIONS_MOCK);

        store
          .pipe(select(selectOrganizationsState))
          .subscribe(state => expect(state).toEqual(ORGANIZATIONS_STATE_MOCK));
      }));
    });

    describe('loadOrganizationsFailure', () => {
      it('should make an api call and return state with error', waitForAsync(() => {
        const expected: OrganizationsState = {
          entities: {},
          ids: [],
          error: deserialize(ERROR_RESPONSE_MOCK, ErrorResponse),
          totalCount: 0,
          summary: null,
        };

        store.dispatch(action);

        const req = httpMock.expectOne(url);

        req.flush(ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK);

        store.pipe(select(selectOrganizationsState)).subscribe(state => expect(state).toEqual(expected));
      }));
    });
  });
});
