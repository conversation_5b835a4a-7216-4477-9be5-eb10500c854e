import { createAction, props } from '@ngrx/store';
import { ErrorResponse } from '~shared/model';
import { SellerInvoiceCorrection } from '@accounting-app/modules/seller-invoice-details/models';

export const loadInvoiceCorrection = createAction('[Invoice] Invoice correction load', props<{ id: string, reason: string }>());

export const loadInvoiceCorrectionSuccess = createAction(
  '[Invoice Correction] Invoice correction load success',
  props<{ invoiceCorrection: SellerInvoiceCorrection }>(),
);

export const loadInvoiceCorrectionFailure = createAction(
  '[Invoice Correction] Invoice correction load failure',
  props<{ error: ErrorResponse }>(),
);
