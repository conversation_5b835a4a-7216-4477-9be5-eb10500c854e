import { SELLER_INVOICES_MOCK } from '@accounting-app/modules/seller-invoices/mocks/seller-invoices.mock';
import { SellerInvoice } from '@accounting-app/modules/seller-invoices/models';
import { InvoicesState } from '@accounting-app/modules/seller-invoices/store/invoices';
import { StoreUtils } from '~shared/utils/store.utils';

const invoices = StoreUtils.deserializePaginatedCollection<SellerInvoice>(SELLER_INVOICES_MOCK, SellerInvoice);

export const SELLER_INVOICES_STATE_MOCK: InvoicesState = {
  entities: StoreUtils.fromArrayToDictionary<SellerInvoice>(invoices),
  ids: StoreUtils.getIdsFromArray<SellerInvoice>(invoices),
  error: null,
  totalCount: SELLER_INVOICES_MOCK.totalCount,
  isGenerating: false,
};
