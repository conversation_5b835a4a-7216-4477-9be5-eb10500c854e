import { SellerBalanceState } from '@accounting-app/modules/seller-balance/store/seller-balance-list';
import { SellerListState } from '@accounting-app/modules/seller-balance/store/seller-list/state';
import { SellerTransactionState } from '@accounting-app/modules/seller-balance/store/seller-transaction/state';

export interface State {
  sellerBalance: SellerBalanceState;
  sellerList: SellerListState;
  sellerTransaction: SellerTransactionState;
}
