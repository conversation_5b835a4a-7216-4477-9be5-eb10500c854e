import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
// eslint-disable-next-line import/no-extraneous-dependencies
import 'datatables.net';
import { PayoutsService } from '../../services/payouts.service';
import { REPORT_DATE_FORMAT, PAYOUTS_DATATABLE_CONFIG } from '../../constants/defines';
import { DatePipe } from '@angular/common';
import { ReportInfo } from '../../models/report.model';
import jquery from 'jquery';
import { FeatureFlagService } from '@root/core/modules/feature-flag';
import { Subject } from 'rxjs';
import { DomSanitizer } from '@angular/platform-browser';
import { OAuthService } from 'angular-oauth2-oidc';
import { SELLERS_MODIFIED_INVOICES_API_ROUTE } from '@accounting-app/modules/seller-invoices/constants';

@Component({
  selector: 'app-payouts-list',
  templateUrl: './payouts-list.component.html',
  styleUrls: ['./payouts-list.component.scss'],
})
export class PayoutsListComponent implements OnInit, OnDestroy {
  private dataTable: any;
  private convertedDateTo: string;
  private reportDateFormat = REPORT_DATE_FORMAT;
  private readonly unsubscribe$ = new Subject();
  public isNewPayoutsOverviewTableVisible = false;
  public iframeSourceUrl = null;

  constructor(
    private payoutsService: PayoutsService,
    public datePipe: DatePipe,
    private featureFlagService: FeatureFlagService,
    private domSanitizer: DomSanitizer,
    private oAuthService: OAuthService,
  ) {}

  ngOnInit() {
    this.getModifiedPayoutsSource();
    this.convertedDateTo = this.datePipe.transform(new Date(Date.now()), this.reportDateFormat);
    this.setDatatableOptions();
    this.downloadPayoutsReport();
  }

  public setDatatableOptions = (): void => {
    this.dataTable = jquery('#payoutsTable');
    this.dataTable.DataTable(PAYOUTS_DATATABLE_CONFIG);
  };

  public downloadPayoutsReport = (): void => {
    const that = this;
    jquery(document).on('click', '.report_id', function (e) {
      this.reportId = jquery(this).data('reportid');
      return that.payoutsService
        .loadPayoutsReport(this.reportId)
        .subscribe((res: ReportInfo) => that.downloadFile(res.link));
    });
  };

  public downloadFile = (link: string): void => {
    this.payoutsService.downloadReport(link, this.convertedDateTo).subscribe();
  };

  public ngOnDestroy(): void {
    this.unsubscribe$.next(void 0);
    this.unsubscribe$.complete();
  }

  public getModifiedPayoutsSource(): void {
    const userToken = this.oAuthService.getAccessToken();
    this.iframeSourceUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(
      `${SELLERS_MODIFIED_INVOICES_API_ROUTE}/reports?type=payoutOverview&token=${userToken}`,
    );
  }
}
