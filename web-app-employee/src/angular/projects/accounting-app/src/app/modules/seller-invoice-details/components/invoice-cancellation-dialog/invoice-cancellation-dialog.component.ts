import { select, Store } from '@ngrx/store';
import { Component, Inject, OnInit } from '@angular/core';
import {SellerInvoiceDetails} from "@accounting-app/modules/seller-invoice-details/models";
import { paidInvoice } from "@accounting-app/modules/seller-invoice-details/constants/mf-status";
import { FormBuilder, FormGroup, AbstractControl } from '@angular/forms';
import { Validators } from '@angular/forms';
import * as fromInvoiceCancellation from '@accounting-app/modules/seller-invoice-details/store/invoice-cancellation';
import { Observable } from 'rxjs';
import { ErrorResponse } from '~shared/model';
import { MmDialogData, MmDialogRefService } from '@metromarkets/components-17';
import { InvoiceCancellationRequest } from '@accounting-app/modules/seller-invoice-details/models/invoice-cancellation-request.model';

@Component({
  selector: 'employee-app-seller-invoice-cancellation-dialog',
  templateUrl: './invoice-cancellation-dialog.component.html',
  styleUrls: ['./invoice-cancellation-dialog.component.scss'],
})
export class InvoiceCancellationDialogComponent implements OnInit {
  public invoice: SellerInvoiceDetails;
  public invoiceCancellationForm: FormGroup;
  public invoiceCancellationError$: Observable<ErrorResponse>;
  public errorMessage: string;
  public error: ErrorResponse;

  get reasonControl(): AbstractControl<any, any> {
    return this.invoiceCancellationForm?.get('reason');
  }

  constructor(
    @Inject(MmDialogData) public data: { invoice: SellerInvoiceDetails },
    private store: Store<fromInvoiceCancellation.InvoiceCancellationState>,
    private dialogRef: MmDialogRefService<InvoiceCancellationDialogComponent>,
    private fb: FormBuilder,
    private invoiceCancellationStore: Store<fromInvoiceCancellation.InvoiceCancellationState>,
  ) {
    this.invoice = data.invoice;
    this.invoiceCancellationForm = this.fb.group(
      {
        reason: ['', [Validators.required]]
      }
    );
  }

  ngOnInit() {
    this.invoiceCancellationError$ = this.store.select(fromInvoiceCancellation.getInvoiceCancellationError);
    this.invoiceCancellationError$.subscribe(error => {
      this.error = error;
    });
  }

  invoiceIsPaid(): boolean {
    return paidInvoice.includes(this.invoice.membershipFee.status);
  }

  close() {
    this.dialogRef.close(false);
  }

  confirm() {
    this.dialogRef.close(true);
  }

  cancelInvoice() {

    const reasonControl = this.invoiceCancellationForm.get('reason');

    if (this.invoiceCancellationForm.invalid) {
      reasonControl.markAsTouched();
      return;
    }

    if (!this.invoice.membershipFee.id) {
      return;
    }

    const payload: InvoiceCancellationRequest = {
      invoiceId: this.invoice.id,
      membershipFeeId: this.invoice.membershipFee.id,
      reason: reasonControl.value
    };

    this.invoiceCancellationStore.dispatch(new fromInvoiceCancellation.LoadInvoiceCancellation(payload));

    this.store.pipe(select(fromInvoiceCancellation.getInvoiceCancellationLoading)).subscribe(loading => {

      if (!loading && !this.error) {
        this.dialogRef.close("cancellationSuccessful");
      }
    });
  }
}
