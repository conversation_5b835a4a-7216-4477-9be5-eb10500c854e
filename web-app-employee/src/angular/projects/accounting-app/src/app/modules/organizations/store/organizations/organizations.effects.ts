import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { Organizations } from '@accounting-app/modules/organizations/models';
import { OrganizationsService } from '@accounting-app/modules/organizations/services/organizations.service';
import {
  loadOrganizations,
  loadOrganizationsFailure,
  loadOrganizationsSuccess,
} from '@accounting-app/modules/organizations/store/organizations/actions/load-organizations.actions';
import { ErrorResponse } from '~shared/model';

@Injectable()
export class OrganizationsEffects {
  loadOrganizations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(loadOrganizations),
      switchMap(({ params }) =>
        this.organizationsService.loadOrganizations(params).pipe(
          map((organizations: Organizations) => loadOrganizationsSuccess({ organizations })),
          catchError((error: ErrorResponse) => of(loadOrganizationsFailure({ error }))),
        ),
      ),
    ),
  );

  constructor(private actions$: Actions, private organizationsService: OrganizationsService) {}
}
