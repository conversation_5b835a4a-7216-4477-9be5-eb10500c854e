import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { queryBuilder } from '@metromarkets/sdk-17';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { deserialize } from 'serialize-ts/dist';
import { SELLER_INVOICES_API_ROUTE } from '@accounting-app/modules/seller-invoices/constants';
import {
  InvoiceGeneratorModel,
  SellerInvoice,
  SellerInvoiceStatistic,
} from '@accounting-app/modules/seller-invoices/models';
import { PaginatedResponse, Period, RequestParams } from '~shared/model';

@Injectable({
  providedIn: 'root',
})
export class SellerInvoicesService {
  constructor(private httpClient: HttpClient) {}

  loadInvoices(requestParams: RequestParams): Observable<PaginatedResponse<SellerInvoice>> {
    const params = queryBuilder.toParams(requestParams) as HttpParams;

    return this.httpClient.get<PaginatedResponse<SellerInvoice>>(SELLER_INVOICES_API_ROUTE, { params }).pipe(
      map(({ items, totalCount }) => ({
        items: items.map(i => deserialize(i, SellerInvoice)),
        totalCount,
      })),
    );
  }

  createInvoice(invoiceGenerator: InvoiceGeneratorModel): Observable<SellerInvoice> {
    const url = `${SELLER_INVOICES_API_ROUTE}/generate`;

    return this.httpClient
      .post<SellerInvoice>(url, invoiceGenerator)
      .pipe(map(invoice => deserialize(invoice, SellerInvoice)));
  }

  sendInvoices(invoiceIds: string[] | number[]): Observable<{}> {
    const url = `${SELLER_INVOICES_API_ROUTE}/send`;

    return this.httpClient.post(url, { invoiceIds });
  }

  loadInvoicesStatistic(period: Period): Observable<SellerInvoiceStatistic> {
    const url = `${SELLER_INVOICES_API_ROUTE}/stats`;

    return this.httpClient
      .post<SellerInvoiceStatistic>(url, period)
      .pipe(map(stats => deserialize(stats, SellerInvoiceStatistic)));
  }
}
