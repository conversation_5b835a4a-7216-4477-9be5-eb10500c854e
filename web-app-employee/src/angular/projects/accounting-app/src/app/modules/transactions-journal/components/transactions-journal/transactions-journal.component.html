<div class="journal">
  <div class="journal__actions">
    <button mat-raised-button color="primary" (click)="navigateToPosting()">Create Posting</button>
  </div>
  <table mat-table [dataSource]="transactions$ | async" class="journal__table">
    <ng-container matColumnDef="number">
      <th mat-header-cell *matHeaderCellDef>Transaction #</th>
      <td mat-cell *matCellDef="let i = index">
        {{ i + 1 | transactionNumberIncrement: page:limit | padStart: 5:'0' }}
      </td>
    </ng-container>

    <ng-container matColumnDef="date">
      <th mat-header-cell *matHeaderCellDef>Date</th>
      <td mat-cell *matCellDef="let element">{{ element.createdAt | date }}</td>
    </ng-container>

    <ng-container matColumnDef="description">
      <th mat-header-cell *matHeaderCellDef>Description</th>
      <td mat-cell *matCellDef="let element">{{ element.description }}</td>
    </ng-container>

    <ng-container matColumnDef="account">
      <th mat-header-cell *matHeaderCellDef>Account</th>
      <td mat-cell *matCellDef="let element">{{ element.fromAccountName + ' -> ' + element.toAccountName }}</td>
    </ng-container>

    <ng-container matColumnDef="debit">
      <th mat-header-cell *matHeaderCellDef>Debit</th>
      <td mat-cell *matCellDef="let element">
        {{ element | transactionAmount: transactionTypes.Accuran | currency: element.currency }}
      </td>
    </ng-container>

    <ng-container matColumnDef="credit">
      <th mat-header-cell *matHeaderCellDef>Credit</th>
      <td mat-cell *matCellDef="let element">
        {{ element | transactionAmount: transactionTypes.Transfer | currency: element.currency }}
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>

  <mat-paginator [pageSize]="limit" [length]="totalCount$ | async" (page)="changePage($event)"></mat-paginator>
</div>
