import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SellerBalanceListComponent } from './components/seller-balance-list/seller-balance-list.component';
import { SellerBalanceRoutingModule } from '@accounting-app/modules/seller-balance/seller-balance-routing.module';
import {
  MmButtonModule,
  MmFlexLayoutModule,
  MmFormFieldModule,
  MmIconModule,
  MmSelectModule,
  MmSpinnerModule,
  MmTableModule,
  MmTablePaginationModule,
} from '@metromarkets/components-17';
import { StoreModule } from '@ngrx/store';
import { reducers } from '@accounting-app/modules/seller-balance/store';
import { EffectsModule } from '@ngrx/effects';
import { SELLER_BALANCE_FEATURE_NAME } from '@accounting-app/modules/seller-balance/constants';
import { SellerBalanceListEffects } from '@accounting-app/modules/seller-balance/store/seller-balance-list';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { SellerListEffects } from '@accounting-app/modules/seller-balance/store/seller-list/effects';
import { SellerListFilterComponent } from './components/seller-list-filter/seller-list-filter.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MultiselectAutocompleteModule } from '../../shared/multiselect-autocomplete';
import { MatChipsModule } from '@angular/material/chips';
import { SearchBarModule } from '~shared/modules/search-bar/search-bar.module';
import { SellerTransactionComponent } from './components/seller-transaction/seller-transaction.component';
import { SellerTransactionsEffects } from '@accounting-app/modules/seller-balance/store/seller-transaction';
import { BackButtonModule } from '~shared/modules/back-button/back-button.module';

@NgModule({
  declarations: [SellerBalanceListComponent, SellerListFilterComponent, SellerTransactionComponent],
    imports: [
        CommonModule,
        SellerBalanceRoutingModule,
        MmTableModule,
        MmSpinnerModule,
        MmTablePaginationModule,
        MatTableModule,
        StoreModule.forFeature(SELLER_BALANCE_FEATURE_NAME, reducers),
        EffectsModule.forFeature([SellerBalanceListEffects, SellerListEffects, SellerTransactionsEffects]),
        MmIconModule,
        MatIconModule,
        TranslateModule,
        MatDatepickerModule,
        MmFormFieldModule,
        MatNativeDateModule,
        MatAutocompleteModule,
        MatCheckboxModule,
        FormsModule,
        ReactiveFormsModule,
        MultiselectAutocompleteModule,
        MatChipsModule,
        MmFlexLayoutModule,
        SearchBarModule,
        MmSelectModule,
        MmButtonModule,
        BackButtonModule,
    ],
})
export class SellerBalanceModule {}
