import * as faker from 'faker';
import { SellerInvoiceStatesEnum } from '@accounting-app/modules/seller-invoices/models';

export const SELLER_INVOICE_DETAILS_MOCK = {
  id: faker.random.uuid(),
  sellerId: faker.random.uuid(),
  invoiceNumber: faker.random.alphaNumeric(),
  invoiceState: SellerInvoiceStatesEnum.Created,
  from: new Date().toISOString(),
  to: new Date().toISOString(),
  total: {
    amount: faker.finance.amount(),
    currency: faker.finance.currencyCode(),
  },
  createdAt: new Date().toISOString(),
  transactions: [
    {
      description: faker.random.words(),
      fee: {
        amount: faker.finance.amount(),
        currency: faker.finance.currencyCode(),
      },
      tax: {
        amount: faker.finance.amount(),
        currency: faker.finance.currencyCode(),
      },
      bookingDate: new Date().toISOString(),
    },
  ],
};
