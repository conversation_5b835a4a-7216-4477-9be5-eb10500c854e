import {HttpClient, HttpHeaders} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { deserialize } from 'serialize-ts/dist';
import { SELLERS_CORRECT_INVOICE_API_ROUTE } from '@accounting-app/modules/seller-invoice-details/constants';
import { SellerInvoiceCorrection } from '@accounting-app/modules/seller-invoice-details/models';
import { OAuthService } from "angular-oauth2-oidc";

@Injectable({
  providedIn: 'root',
})
export class SellerInvoiceCorrectionService {
  constructor(
    private httpClient: HttpClient,
    private oAuthService: OAuthService,
  ) {}

  correctInvoice(id: string, reason: string): Observable<SellerInvoiceCorrection> {
    const headers = new HttpHeaders({
      Authorization: `Bearer ${this.oAuthService.getAccessToken()}`,
      'Content-Type': 'application/json',
      Accept: 'application/json',
    });

    return this
      .httpClient
      .post<SellerInvoiceCorrection>(`${SELLERS_CORRECT_INVOICE_API_ROUTE}/${id}`, { reason }, { headers })
      .pipe(
        map(invoiceCorrectionStateResult => deserialize(invoiceCorrectionStateResult, SellerInvoiceCorrection))
      );
  }
}
