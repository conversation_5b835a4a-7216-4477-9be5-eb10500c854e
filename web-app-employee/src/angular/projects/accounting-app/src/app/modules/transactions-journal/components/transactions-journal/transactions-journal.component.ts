import { Component, OnInit } from '@angular/core';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { select, Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { DEFAULT_LIMIT } from '@accounting-app/modules/transactions-journal/constants';
import { TransactionJournal, TransactionTypes } from '@accounting-app/modules/transactions-journal/models';
import * as fromJournal from '@accounting-app/modules/transactions-journal/store/journal';
import { ACCOUNTS_ROUTE, BUYER_SUPPORT_ROUTE, FINANCIAL_POSTING_ROUTE } from '~shared/constants';
import { QueryParamsHandler } from '~shared/services/query-params-handler.service';

@Component({
  selector: 'employee-app-transactions-journal',
  templateUrl: './transactions-journal.component.html',
  styleUrls: ['./transactions-journal.component.scss'],
  providers: [QueryParamsHandler],
})
export class TransactionsJournalComponent implements OnInit {
  transactions$: Observable<TransactionJournal[]>;
  totalCount$: Observable<number>;
  page: number;

  displayedColumns = ['number', 'date', 'description', 'account', 'debit', 'credit'];
  limit = DEFAULT_LIMIT;
  transactionTypes = TransactionTypes;

  constructor(
    private store: Store<fromJournal.TransactionsJournalState>,
    private router: Router,
    private queryParamsHandler: QueryParamsHandler,
  ) {}

  ngOnInit() {
    this.transactions$ = this.store.pipe(select(fromJournal.getTransactionsJournal));
    this.totalCount$ = this.store.pipe(select(fromJournal.getTransactionsTotal));
  }

  changePage(pageEvent: PageEvent) {
    this.page = pageEvent.pageIndex;

    this.queryParamsHandler.changeQueryParams({ offset: this.page * this.limit, limit: this.limit });
  }

  navigateToPosting() {
    this.router.navigate([BUYER_SUPPORT_ROUTE, ACCOUNTS_ROUTE, FINANCIAL_POSTING_ROUTE]);
  }
}
