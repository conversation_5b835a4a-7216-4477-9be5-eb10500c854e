import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { SellerInvoiceCorrection } from '@accounting-app/modules/seller-invoice-details/models';
import { SellerInvoiceCorrectionService } from "@accounting-app/modules/seller-invoice-details/services/seller-invoice-correction.service";
import { ErrorResponse } from '~shared/model';
import { InvoiceCorrectionActions } from './actions';

@Injectable()
export class InvoiceCorrectionEffects {
  loadInvoiceCorrection$ = createEffect(() =>
    this.actions$.pipe(
      ofType(InvoiceCorrectionActions.loadInvoiceCorrection),
      switchMap(({id, reason}) =>
        this.invoiceCorrectionService
          .correctInvoice(id, reason)
          .pipe(
            map(
              (invoiceCorrection: SellerInvoiceCorrection) => InvoiceCorrectionActions.loadInvoiceCorrectionSuccess({ invoiceCorrection })
            ),
            catchError((error: ErrorResponse) => of(InvoiceCorrectionActions.loadInvoiceCorrectionFailure({ error }))),
          ),
      ),
    ),
  );

  constructor(
    private actions$: Actions,
    private invoiceCorrectionService: SellerInvoiceCorrectionService
  ) {}
}
