import { createFeatureSelector, createSelector } from '@ngrx/store';
import { INVOICE_CANCELLATION_FEATURE_NAME } from '@accounting-app/modules/seller-invoice-details/store/feature-name';
import { State } from '@accounting-app/modules/seller-invoice-details/store/state';
import { InvoiceCancellationState } from '@accounting-app/modules/seller-invoice-details/store/invoice-cancellation/invoice-cancellation.state';

const getModuleFeatureState = createFeatureSelector(INVOICE_CANCELLATION_FEATURE_NAME);

export const selectInvoiceCancellationState = createSelector(getModuleFeatureState, (state: State) => state.invoiceCancellation);

export const getInvoiceCancellationError = createSelector(
  selectInvoiceCancellationState,
  (state: InvoiceCancellationState) => state.error
);

export const getInvoiceCancellationLoading = createSelector(
  selectInvoiceCancellationState,
  (state: InvoiceCancellationState) => state.loading
);
