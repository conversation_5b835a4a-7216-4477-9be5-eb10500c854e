import { InvoiceDetailsState } from './invoice-details/invoice-details.state';
import { InvoiceCorrectionState } from "@accounting-app/modules/seller-invoice-details/store/invoice-correction";
import { InvoiceCancellationState } from './invoice-cancellation/invoice-cancellation.state';

export interface State {
  invoiceDetails: InvoiceDetailsState;
  invoiceCorrection: InvoiceCorrectionState;
  invoiceCancellation: InvoiceCancellationState
}
