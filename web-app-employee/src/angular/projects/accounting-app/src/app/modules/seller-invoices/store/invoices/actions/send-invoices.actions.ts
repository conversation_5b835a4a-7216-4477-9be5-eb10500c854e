import { Update } from '@ngrx/entity';
import { createAction, props } from '@ngrx/store';
import { SellerInvoice } from '@accounting-app/modules/seller-invoices/models';
import { ErrorResponse } from '~shared/model';

export const sendInvoices = createAction('[Seller Invoices] Invoices send', props<{ ids: string[] }>());

export const sendInvoicesSuccess = createAction(
  '[Seller Invoices] Invoices send success',
  props<{ invoices: Update<SellerInvoice>[] }>(),
);

export const sendInvoicesFailure = createAction(
  '[Seller Invoices] Invoices send failure',
  props<{ error: ErrorResponse }>(),
);
