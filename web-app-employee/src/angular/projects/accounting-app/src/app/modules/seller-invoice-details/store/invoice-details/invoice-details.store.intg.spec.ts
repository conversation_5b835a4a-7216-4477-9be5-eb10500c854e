import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed, waitForAsync } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { EffectsModule } from '@ngrx/effects';
import { select, Store, StoreModule } from '@ngrx/store';
import * as faker from 'faker';
import { deserialize } from 'serialize-ts/dist';
import { BaseHttpInterceptor } from '~core/interceptors/base-http-interceptor';
import { SELLER_INVOICE_DETAILS_MOCK, SELLER_INVOICE_DETAILS_STATE_MOCK,} from '@accounting-app/modules/seller-invoice-details/mocks';
import { reducers } from '@accounting-app/modules/seller-invoice-details/store';
import { SELLER_INVOICE_DETAILS_FEATURE_NAME } from '@accounting-app/modules/seller-invoice-details/store/feature-name';
import { InvoiceDetailsEffects } from '@accounting-app/modules/seller-invoice-details/store/invoice-details/invoice-details.effects';
import { selectInvoiceDetailsState } from '@accounting-app/modules/seller-invoice-details/store/invoice-details/invoice-details.selectors';
import { InvoiceDetailsState } from '@accounting-app/modules/seller-invoice-details/store/invoice-details/invoice-details.state';
import { ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK } from '~shared/mock';
import { ErrorResponse } from '~shared/model';
import { loadInvoiceDetails } from './actions/load-invoice-details.actions';
import { OAuthService } from 'angular-oauth2-oidc';

const host = window['config'].serviceBaseUrls.modifiedInvoicing;

describe('Invoice Details Store integration', () => {
  let httpMock: HttpTestingController;
  let store: Store<InvoiceDetailsState>;
  let oauthService: jest.Mocked<OAuthService>;

  beforeEach(() => {

    const oauthServiceMock = {
      getAccessToken: jest.fn(),
    } as unknown as jest.Mocked<OAuthService>;

    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        RouterTestingModule,
        StoreModule.forRoot({}, { runtimeChecks: { strictStateImmutability: true, strictActionImmutability: true } }),
        StoreModule.forFeature(SELLER_INVOICE_DETAILS_FEATURE_NAME, reducers),
        EffectsModule.forRoot([]),
        EffectsModule.forFeature([InvoiceDetailsEffects]),
      ],
      providers: [
        { provide: HTTP_INTERCEPTORS, useClass: BaseHttpInterceptor, multi: true },
        { provide: OAuthService, useValue: oauthServiceMock },
      ],
    });

    httpMock = TestBed.inject(HttpTestingController);
    store = TestBed.inject(Store);
    oauthService = TestBed.inject(OAuthService) as jest.Mocked<OAuthService>;
  });

  describe('Load Invoice Details', () => {
    const id = faker.random.uuid();
    const url = `${host}/api/v1/internal/reports/${id}`;
    const action = loadInvoiceDetails({ id });

    describe('loadInvoiceDetailsSuccess', () => {
      it('should make an api call and return state with invoice details', waitForAsync(() => {
        store.dispatch(action);

        const req = httpMock.expectOne(url);
        expect(req.request.method).toBe('GET');

        req.flush(SELLER_INVOICE_DETAILS_MOCK);

        store
          .pipe(select(selectInvoiceDetailsState))
          .subscribe(state => expect(state).toEqual(SELLER_INVOICE_DETAILS_STATE_MOCK));
      }));
    });

    describe('loadInvoiceDetailsFailure', () => {
      it('should make an api call and return state with error', waitForAsync(() => {
        const expected: InvoiceDetailsState = {
          payload: null,
          loading: false,
          error: deserialize(ERROR_RESPONSE_MOCK, ErrorResponse),
        };
        store.dispatch(action);

        const req = httpMock.expectOne(url);
        expect(req.request.method).toBe('GET');

        req.flush(ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK);

        store.pipe(select(selectInvoiceDetailsState)).subscribe(state => expect(state).toEqual(expected));
      }));
    });
  });
});
