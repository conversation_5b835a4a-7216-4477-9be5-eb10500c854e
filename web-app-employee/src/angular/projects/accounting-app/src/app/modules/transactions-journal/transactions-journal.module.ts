import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatTableModule } from '@angular/material/table';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { reducers } from '@accounting-app/modules/transactions-journal/store';
import { TRANSACTIONS_JOURNAL_FEATURE_NAME } from '@accounting-app/modules/transactions-journal/store/feature-name';
import { TransactionsJournalEffects } from '@accounting-app/modules/transactions-journal/store/journal';

import { TransactionsJournalRoutingModule } from './transactions-journal-routing.module';
import { TransactionsJournalComponent } from './components/transactions-journal/transactions-journal.component';
import { TransactionAmountPipe } from './pipes/transaction-amount.pipe';
import { PadStartPipe } from './pipes/pad-start.pipe';
import { TransactionNumberIncrementPipe } from './pipes/transaction-number-increment.pipe';

@NgModule({
  declarations: [TransactionsJournalComponent, TransactionAmountPipe, PadStartPipe, TransactionNumberIncrementPipe],
  imports: [
    CommonModule,
    StoreModule.forFeature(TRANSACTIONS_JOURNAL_FEATURE_NAME, reducers),
    EffectsModule.forFeature([TransactionsJournalEffects]),
    MatTableModule,
    MatPaginatorModule,
    MatButtonModule,
    TransactionsJournalRoutingModule,
  ],
})
export class TransactionsJournalModule {}
