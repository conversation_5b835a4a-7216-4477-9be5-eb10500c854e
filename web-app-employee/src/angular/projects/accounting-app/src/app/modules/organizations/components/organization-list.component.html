<div class="organization-list" [mmTestId]="'ORGANIZATIONS.ORGANIZATIONS_ACCOUNTS'">
  <div class="organization-list__heading">
    <h2>Organizations</h2>
  </div>
  <div class="organizations-summary" *ngIf="summary$ | async as summary">
    <div class="organizations-summary__item">
      <b>Grand Total</b>&nbsp;<span>{{ summary.total?.amount | currency: summary.total?.currency }}</span>
    </div>
    <div class="organizations-summary__item">
      <b>Grand VAT</b>&nbsp;<span>{{ summary.vat?.amount | currency: summary.vat?.currency }}</span>
    </div>
    <div class="organizations-summary__item">
      <b>Grand NET</b>&nbsp;<span>{{ summary.net?.amount | currency: summary.net?.currency }}</span>
    </div>
    <div class="organizations-summary__item">
      <b>Grand fee</b>&nbsp;<span>{{ summary.fee?.amount | currency: summary.fee?.currency }}</span>
    </div>
  </div>
  <table mm-table [dataSource]="organizations$ | async" class="seller-invoices__table">
    <ng-container mmColumnDef="id">
      <th mmHeaderCell *mmHeaderCellDef>Seller id</th>
      <td mmCell *mmCellDef="let element">
        {{ element.number }}
      </td>
    </ng-container>

    <ng-container mmColumnDef="name">
      <th mmHeaderCell *mmHeaderCellDef>Name</th>
      <td mmCell *mmCellDef="let element">
        {{ element.name }}
      </td>
    </ng-container>

    <ng-container mmColumnDef="total">
      <th mmHeaderCell *mmHeaderCellDef>Total processed</th>
      <td mmCell *mmCellDef="let element">
        {{ element.total?.amount | currency: element.total?.currency }}
      </td>
    </ng-container>

    <ng-container mmColumnDef="VAT">
      <th mmHeaderCell *mmHeaderCellDef>VAT</th>
      <td mmCell *mmCellDef="let element">{{ element.vat?.amount | currency: element.vat?.currency }}</td>
    </ng-container>

    <ng-container mmColumnDef="NET">
      <th mmHeaderCell *mmHeaderCellDef>NET</th>
      <td mmCell *mmCellDef="let element">{{ element.net?.amount | currency: element.net?.currency }}</td>
    </ng-container>

    <ng-container mmColumnDef="fee">
      <th mmHeaderCell *mmHeaderCellDef>Commission fee</th>
      <td mmCell *mmCellDef="let element">{{ element.fee?.amount | currency: element.fee?.currency }}</td>
    </ng-container>

    <tr mm-header-row *mmHeaderRowDef="displayedColumns; sticky: true"></tr>
    <tr mm-row *mmRowDef="let row; columns: displayedColumns"></tr>
  </table>
  <div class="paginator-container">
    <mat-paginator
      [mmTestTarget]="'organizations-paginator'"
      [length]="totalCount$ | async"
      [pageSize]="defaultLimit"
      (page)="onPageChange($event)"
    ></mat-paginator>
  </div>
</div>
