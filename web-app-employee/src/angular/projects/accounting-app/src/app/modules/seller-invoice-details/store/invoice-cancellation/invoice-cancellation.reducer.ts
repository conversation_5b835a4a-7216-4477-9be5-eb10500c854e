import {
  InvoiceCancellationActionTypes,
  InvoiceCancellationActions
} from '@accounting-app/modules/seller-invoice-details/store/invoice-cancellation/invoice-cancellation.actions';
import {
  InvoiceCancellationState,
  invoiceCancellationInitialState
} from '@accounting-app/modules/seller-invoice-details/store/invoice-cancellation/invoice-cancellation.state';

export function invoiceCancellationReducer(state = invoiceCancellationInitialState, action: InvoiceCancellationActions): InvoiceCancellationState {
  switch (action.type) {
    case InvoiceCancellationActionTypes.LOAD_INVOICE_CANCELLATION:
      return { ...state, loading: true };
    case InvoiceCancellationActionTypes.LOAD_INVOICE_CANCELLATION_SUCCESS:
      return {
        ...state,
        loading: false,
        error: null,
      };
    case InvoiceCancellationActionTypes.LOAD_INVOICE_CANCELLATION_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload.error,
      };
    default:
      return state;
  }
}
