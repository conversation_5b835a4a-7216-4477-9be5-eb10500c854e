import { Action } from '@ngrx/store';
import { ErrorResponse, RequestParams } from '~shared/model';
import {
  PaymentBalance,
  SellerBalance,
  SellerBalancePaginatedResponse,
} from '@accounting-app/modules/seller-balance/models';

export enum SellerBalanceListActionTypes {
  LOAD_SELLER_BALANCE_LIST = '[Seller Balance List] Load seller balance list',
  LOAD_SELLER_BALANCE_LIST_SUCCESS = '[Seller Balance List] Load seller balance list success',
  LOAD_SELLER_BALANCE_LIST_FAILURE = '[Seller Balance List] Load seller balance list failure',
  DOWNLOAD_SELLER_BALANCE = '[Seller Seller Balance] Download seller balance',
  DOWNLOAD_SELLER_BALANCE_SUCCESS = '[Seller Seller Balance] Download seller balance Success',
  DOWNLOAD_SELLER_BALANCE_FAILURE = '[Seller Seller Balance] Download seller balance Failure',
}

export class LoadSellerBalanceList implements Action {
  readonly type = SellerBalanceListActionTypes.LOAD_SELLER_BALANCE_LIST;

  constructor(public payload: { params: RequestParams }) {}
}

export class LoadSellerBalanceListSuccess implements Action {
  readonly type = SellerBalanceListActionTypes.LOAD_SELLER_BALANCE_LIST_SUCCESS;

  constructor(public payload: { sellerBalance: SellerBalancePaginatedResponse<SellerBalance, PaymentBalance> }) {}
}

export class LoadSellerBalanceFailure implements Action {
  readonly type = SellerBalanceListActionTypes.LOAD_SELLER_BALANCE_LIST_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export class DownloadSellerBalance implements Action {
  readonly type = SellerBalanceListActionTypes.DOWNLOAD_SELLER_BALANCE;

  constructor(public payload: { sellerId?: string; fileName: string }) {}
}

export class DownloadSellerBalanceSuccess implements Action {
  readonly type = SellerBalanceListActionTypes.DOWNLOAD_SELLER_BALANCE_SUCCESS;
}

export class DownloadSellerBalanceFailure implements Action {
  readonly type = SellerBalanceListActionTypes.DOWNLOAD_SELLER_BALANCE_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export type SellerBalanceListActionUnion =
  | LoadSellerBalanceList
  | LoadSellerBalanceListSuccess
  | LoadSellerBalanceFailure
  | DownloadSellerBalance
  | DownloadSellerBalanceSuccess
  | DownloadSellerBalanceFailure;
