import { Component, EventEmitter, Output, ViewChild, OnInit } from '@angular/core';
import { ControlValueAccessor, UntypedFormControl, NG_VALUE_ACCESSOR } from '@angular/forms';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { MatDatepicker, MatDatepickerInputEvent } from '@angular/material/datepicker';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { Period } from '~shared/model';
import { PERIOD_DATE_FORMATS } from '../../constants';
import { PeriodDateAdapter } from '../../services/period-adapter';

dayjs.extend(utc);

@Component({
  selector: 'employee-app-invoice-period',
  templateUrl: './invoice-period.component.html',
  providers: [
    { provide: NG_VALUE_ACCESSOR, useExisting: InvoicePeriodComponent, multi: true },
    {
      provide: DateAdapter,
      useClass: PeriodDateAdapter,
    },
    { provide: MAT_DATE_FORMATS, useValue: PERIOD_DATE_FORMATS },
  ],
})
export class InvoicePeriodComponent implements ControlValueAccessor, OnInit {
  @Output() periodChanged = new EventEmitter<Period>();
  @ViewChild('periodPicker') periodPicker: MatDatepicker<Date>;

  public maxDate: Object;
  public periodControl: UntypedFormControl;

  constructor() {
    this.periodControl = new UntypedFormControl();
  }

  onChange = (val: any) => null;
  onTouched = () => null;

  ngOnInit(): void {
    this.calculateMaxDate();
  }

  private get period(): Period {
    const period: Date = this.periodControl.value;
    const dateFormat = 'YYYY-MM-DD';
    const from = dayjs(period).utc(true).startOf('month').format(dateFormat);
    const to = dayjs(period).utc(true).endOf('month').format(dateFormat);

    return { from, to };
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  writeValue(value: Date): void {
    if (value) {
      this.periodControl.setValue(value);
      this.periodChanged.emit(this.period);
    }
  }

  onMonthSelect(date: Date) {
    this.periodPicker.select(date);
    this.periodPicker.close();
  }

  onDateChange({ value }: MatDatepickerInputEvent<Date>) {
    this.periodChanged.emit(this.period);
    this.onChange(value);
  }

  calculateMaxDate(): void {
    const currDate = new Date();
    this.maxDate = new Date(currDate.getFullYear(), currDate.getMonth() - 1, 15);
  }
}
