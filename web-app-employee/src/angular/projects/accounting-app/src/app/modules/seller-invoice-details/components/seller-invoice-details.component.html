<div class="invoice" *ngIf="invoice$ | async as invoice" [mmTestId]="'SELLER_INVOICE.SELLER_INVOICE_DETAILS'">
  <div id="seller-invoice-details-alert-target" class="invoice__alert-container"></div>
  <loader [isVisible]="this.isLoading"></loader>
  <div class="invoice__back-button">
    <app-back-button
      backUrl="../"
      linkText="{{ 'ACCOUNTING.REPORTS.INVOICE_LIST.INVOICE_TITLE' | translate }}"
    ></app-back-button>
  </div>
  <div class="invoice__heading">
    <h2 class="invoice__title">
      {{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.NUMBER' | translate }}: {{ invoice.invoiceNumber }}
    </h2>
  </div>
  <div class="invoice-details">
    <div>
      <button
        mm-button
        (click)="openCorrectInvoiceDialog()"
        [disabled]="this.currentInvoice.membershipFee?.deletedAt || !!invoice.correctionReportId"
      >
        {{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.BUTTONS.CORRECT.LABEL' | translate }}
      </button>
      <button
        mm-button color="warning"
        (click)="openCancelInvoice()"
        [disabled]="disableCancellation()"
      >
        {{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.BUTTONS.CANCEL.LABEL' | translate }}
      </button>
      <info-icon-tooltip *ngIf="disableCancellation()">
        {{ getDisabledCancellationTooltipText() }}
      </info-icon-tooltip>
      <span *ngIf="this.invoice.correctionReportId">
        Corrected by
        <a href="/accounting/invoices/{{ this.invoice.correctionReportId }}">
          {{ this.invoice.correctionReportName }}
        </a>
      </span>
    </div>
    <table>
      <thead>
      <tr>
        <th class="mm-header-cell">{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PERIOD' | translate }}</th>
        <th class="mm-header-cell">{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.SELLER.TITLE' | translate }}</th>
        <th class="mm-header-cell">{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.AMOUNT.TITLE' | translate }}</th>
        <th class="mm-header-cell">
          {{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.STATUS' | translate }}
          <info-icon-tooltip>
            {{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS.INFO.TITLE' | translate }}<br/><br/>
            <ul>
              <li>
                <b>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS.INFO.PENDING_LABEL' | translate }}</b>
                - {{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS.INFO.PENDING_DETAILS' | translate }}
              </li>
              <li>
                <b>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS.INFO.DUNNING_LABEL' | translate }}</b>
                - {{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS.INFO.DUNNING_DETAILS' | translate }}
              </li>
              <li>
                <b>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS.INFO.PAID_EXTERNALLY_LABEL' | translate }}</b>
                - {{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS.INFO.PAID_EXTERNALLY_DETAILS' | translate }}
              </li>
              <li>
                <b>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS.INFO.PAID_LABEL' | translate }}</b>
                - {{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS.INFO.PAID_DETAILS' | translate }}
              </li>
              <li>
                <b>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS.INFO.CHARGE_EXEMPT_LABEL' | translate }}</b>
                - {{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS.INFO.CHARGE_EXEMPT_DETAILS' | translate }}
              </li>
            </ul>
          </info-icon-tooltip>
        </th>
      </tr>
      </thead>
      <tr>
        <td>
          <div>
            <h4>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.RELATED_PERIOD' | translate }}: </h4> {{ invoice.relatedPeriod }}
          </div>
          <div>
            <h4>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.MONTH' | translate }}: </h4> {{ invoice.month }}
          </div>
        </td>
        <td>
          <div>
            <h4>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.SELLER.SHOP_NAME' | translate }}: </h4>
            {{ invoice.shopName }}
          </div>
          <div>
            <h4>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.SELLER.NUMBER' | translate }}: </h4>
            <a href="/seller-support/seller-accounts/{{ invoice.organizationId }}/general">{{ invoice.sellerId }}</a>
          </div>
          <div>
            <h4>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.SELLER.ADDRESS' | translate }}: </h4>
            {{ invoice.address }}
          </div>
        </td>
        <td>
          <div>
            <h4>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.AMOUNT.TOTAL' | translate }}: </h4>
            {{ invoice.grossAmount.amount / 100 }}
            <span [innerHTML]="getCurrencyHtmlCode(invoice.netAmount.currency)"></span>
          </div>
          <div>
            <h4>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.AMOUNT.NET' | translate }}: </h4>
            {{ invoice.netAmount.amount / 100 }}
            <span [innerHTML]="getCurrencyHtmlCode(invoice.netAmount.currency)"></span>
          </div>
          <div>
            <h4>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.AMOUNT.VAT' | translate }}: </h4>
            {{ invoice.vatAmount.amount / 100 }}
            <span [innerHTML]="getCurrencyHtmlCode(invoice.netAmount.currency)"></span>
          </div>
          <div>
            <h4>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.AMOUNT.VAT_RATE' | translate }}: </h4>
            {{ invoice.vatRate }}&#37;
          </div>
        </td>
        <td>
          <div>
            <h4>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.PAYMENT_STATUS' | translate }}: </h4>
            {{ invoice.paymentStatus }}
          </div>
          <div>
            <h4>{{ 'ACCOUNTING.REPORTS.INVOICE_DETAILS.INVOICE_STATUS' | translate }}: </h4>
            {{ invoice.status }}
          </div>
          <div *ngIf="invoice.membershipFee?.cancelledAt" class="with-margin">
            <h4>Cancelled at: </h4>
            {{ invoice.membershipFee?.cancelledAt }} (GMT +0)
          </div>
          <div *ngIf="invoice.membershipFee?.cancelledBy">
            <h4>Cancelled by: </h4>
            {{ invoice.membershipFee?.cancelledBy }}
          </div>
          <div *ngIf="invoice.membershipFee?.cancellationReason">
            <h4>Cancellation reason: </h4>
            {{ invoice.membershipFee?.cancellationReason }}
          </div>
          <div *ngIf="invoice.membershipFee?.refundedAt" class="with-margin">
            <h4>Refunded at: </h4>
            {{ invoice.membershipFee?.refundedAt }} (GMT +0)
          </div>
          <div *ngIf="invoice.membershipFee?.refundedPsp">
            <h4>Refunded PSP reference: </h4>
            {{ invoice.membershipFee?.refundedPsp }}
          </div>
        </td>
      </tr>
    </table>
  </div>
</div>
