import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { TestBed, waitForAsync } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { MmAlertService } from '@metromarkets/components-17';
import { EffectsModule } from '@ngrx/effects';
import { select, Store, StoreModule } from '@ngrx/store';
import * as faker from 'faker';
import { deserialize } from 'serialize-ts/dist';
import { BaseHttpInterceptor } from '~core/interceptors/base-http-interceptor';
import { SELLER_INVOICES_MOCK, SELLER_INVOICES_STATE_MOCK } from '@accounting-app/modules/seller-invoices/mocks';
import { reducers } from '@accounting-app/modules/seller-invoices/store';
import { SELLER_INVOICES_FEATURE_NAME } from '@accounting-app/modules/seller-invoices/store/feature-name';
import * as fromInvoices from '@accounting-app/modules/seller-invoices/store/invoices';
import { InvoicesState } from '@accounting-app/modules/seller-invoices/store/invoices/invoices.state';
import { ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK } from '~shared/mock';
import { ErrorResponse } from '~shared/model';
import { createInvoice } from './actions/create-invoice.actions';

const host = window['config'].serviceBaseUrls.invoicing;

describe('Invoices Store Integration', () => {
  let httpMock: HttpTestingController;
  let store: Store<InvoicesState>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        StoreModule.forRoot({}, { runtimeChecks: { strictStateImmutability: true, strictActionImmutability: true } }),
        StoreModule.forFeature(SELLER_INVOICES_FEATURE_NAME, reducers),
        EffectsModule.forRoot([]),
        EffectsModule.forFeature([fromInvoices.InvoicesEffects]),
        HttpClientTestingModule,
        RouterTestingModule,
      ],
      providers: [
        { provide: HTTP_INTERCEPTORS, useClass: BaseHttpInterceptor, multi: true },
        { provide: MmAlertService, useValue: { addAlert: jest.fn() } },
      ],
    });

    httpMock = TestBed.inject(HttpTestingController);
    store = TestBed.inject(Store);
  });

  describe('Load Invoices', () => {
    const offset = 0;
    const limit = 30;
    const url = `${host}/api/v1/invoices?offset=${offset}&limit=${limit}`;
    const action = fromInvoices.LoadInvoicesActions.loadInvoices({ params: { offset, limit } });

    describe('loadInvoicesSuccess', () => {
      it('should make an api call and return entity state with invoices', waitForAsync(() => {
        store.dispatch(action);

        const req = httpMock.expectOne(url);

        expect(req.request.method).toBe('GET');
        req.flush(SELLER_INVOICES_MOCK);

        store
          .pipe(select(fromInvoices.selectInvoicesState))
          .subscribe(state => expect(state).toEqual(SELLER_INVOICES_STATE_MOCK));
      }));
    });

    describe('loadInvoicesFailure', () => {
      it('should make an api call and return entity state with error', waitForAsync(() => {
        const expected: InvoicesState = {
          entities: {},
          ids: [],
          error: deserialize(ERROR_RESPONSE_MOCK, ErrorResponse),
          totalCount: 0,
          isGenerating: false,
        };
        store.dispatch(action);

        const req = httpMock.expectOne(url);

        expect(req.request.method).toBe('GET');
        req.flush(ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK);

        store.pipe(select(fromInvoices.selectInvoicesState)).subscribe(state => expect(state).toEqual(expected));
      }));
    });
  });

  describe('Create Invoice', () => {
    const url = `${host}/api/v1/invoices/generate`;
    const body = {
      sellerId: faker.random.uuid(),
      from: faker.date.past().toISOString(),
      to: faker.date.future().toISOString(),
    };
    const action = createInvoice({ invoiceGenerator: body });

    describe('createInvoiceSuccess', () => {
      it('should make an api call and add newly created invoice to the list', waitForAsync(() => {
        const invoice = {
          id: faker.random.uuid(),
        };

        store.dispatch(action);

        const req = httpMock.expectOne(url);

        expect(req.request.method).toBe('POST');
        expect(req.request.body).toEqual(body);

        req.flush(invoice);

        store.pipe(select(fromInvoices.selectInvoices)).subscribe(invoices => expect(invoices[0]).toEqual(invoice));

        store.pipe(select(fromInvoices.isGenerating)).subscribe(actual => expect(actual).toBeFalsy());
      }));
    });

    describe('createInvoiceFailure', () => {
      it('should make an api call and return state with error without modifying entities', waitForAsync(() => {
        const expected: fromInvoices.InvoicesState = {
          entities: {},
          ids: [],
          totalCount: 0,
          isGenerating: false,
          error: deserialize(ERROR_RESPONSE_MOCK, ErrorResponse),
        };

        store.dispatch(action);

        const req = httpMock.expectOne(url);

        expect(req.request.method).toBe('POST');
        expect(req.request.body).toEqual(body);

        req.flush(ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK);

        store.pipe(select(fromInvoices.selectInvoicesState)).subscribe(state => expect(state).toEqual(expected));
      }));
    });
  });
});
