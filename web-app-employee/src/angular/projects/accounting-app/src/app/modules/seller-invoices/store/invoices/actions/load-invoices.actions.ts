import { createAction, props } from '@ngrx/store';
import { SellerInvoice } from '@accounting-app/modules/seller-invoices/models';
import { ErrorResponse, PaginatedResponse, RequestParams } from '~shared/model';

export const loadInvoices = createAction('[Seller Invoices] Invoices load', props<{ params: RequestParams }>());

export const loadInvoicesSuccess = createAction(
  '[Seller Invoices] Invoices load success',
  props<{ invoicesPaginated: PaginatedResponse<SellerInvoice> }>(),
);

export const loadInvoicesFailure = createAction(
  '[Seller Invoices] Invoices load failure',
  props<{ error: ErrorResponse }>(),
);
