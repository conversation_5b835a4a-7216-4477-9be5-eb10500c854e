import {
  sellerTransactionInitialState,
  SellerTransactionState,
} from '@accounting-app/modules/seller-balance/store/seller-transaction/state';
import {
  SellerTransactionsActionTypes,
  SellerTransactionsActionUnion,
} from '@accounting-app/modules/seller-balance/store/seller-transaction/actions';

export function sellerTransactionReducer(
  state = sellerTransactionInitialState,
  action: SellerTransactionsActionUnion,
): SellerTransactionState {
  switch (action.type) {
    case SellerTransactionsActionTypes.LOAD_TRANSACTIONS:
    case SellerTransactionsActionTypes.DOWNLOAD_TRANSACTIONS:
      return { ...state, loading: true };

    case SellerTransactionsActionTypes.LOAD_TRANSACTIONS_SUCCESS:
      return {
        ...state,
        loading: false,
        payload: { ...action.payload.transactions },
        error: null,
      };

    case SellerTransactionsActionTypes.LOAD_TRANSACTIONS_FAILURE:
      return {
        ...state,
        payload: {
          items: [],
          totalCount: 0,
          totalBalanceWithMM: null,
          sellerInfo: null,
        },
        error: action.payload.error,
      };

    case SellerTransactionsActionTypes.DOWNLOAD_TRANSACTIONS_SUCCESS:
      return {
        ...state,
        loading: false,
        error: null,
      };

    case SellerTransactionsActionTypes.DOWNLOAD_TRANSACTIONS_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload.error,
      };

    default:
      return state;
  }
}
