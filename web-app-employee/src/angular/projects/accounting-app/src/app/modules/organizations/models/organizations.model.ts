import { ArraySerializer, Field, Name, Type } from 'serialize-ts/dist';
import { ModelMetadataSerializer } from 'serialize-ts/dist/serializers/model-metadata.serializer';
import { Organization } from '@accounting-app/modules/organizations/models/organization.model';
import { OrganizationsSummary } from '@accounting-app/modules/organizations/models/organizations-summary.model';

export class Organizations {
  @Field()
  @Name('overview')
  summary: OrganizationsSummary;

  @Field()
  @Type(new ArraySerializer(new ModelMetadataSerializer(Organization)))
  items: Organization[];

  @Field()
  totalCount: number;
}
