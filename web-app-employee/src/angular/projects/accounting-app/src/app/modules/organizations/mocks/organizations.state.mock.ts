import { deserialize } from 'serialize-ts/dist';
import { ORGANIZATIONS_MOCK } from '@accounting-app/modules/organizations/mocks/organizations.mock';
import { Organization, Organizations } from '@accounting-app/modules/organizations/models';
import { OrganizationsState } from '@accounting-app/modules/organizations/store/organizations';
import { StoreUtils } from '~shared/utils/store.utils';

const organizations = deserialize(ORGANIZATIONS_MOCK, Organizations);

export const ORGANIZATIONS_STATE_MOCK: OrganizationsState = {
  entities: StoreUtils.fromArrayToDictionary<Organization>(organizations.items),
  ids: StoreUtils.getIdsFromArray<Organization>(organizations.items),
  error: null,
  totalCount: organizations.totalCount,
  summary: organizations.summary,
};
