const baseConfig = require('../../jest.config');

module.exports = {
  ...baseConfig,
  name: 'accounting-app',
  coverageDirectory: '../../coverage/accounting-app',
  moduleNameMapper: {
    '^@root/(.*)$': '<rootDir>/../../src/app/$1',
    '^@accounting-app/(.*)$': '<rootDir>/src/app/$1',
    '^~shared/(.*)$': '<rootDir>/../../src/app/shared/$1',
    '^@employee-app/(.*)$': '<rootDir>/src/app/$1',
    '^~core/(.*)$': '<rootDir>/../../src/app/core/$1',
    '^~env/environment(.*)$': '<rootDir>/../../src/environments/environment.testing.ts',
    '^@employee-app/(.*)$': '<rootDir>../employee-app/src/app/$1',
  },
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/projects/accounting-app/tsconfig.spec.json',
    },
  },
  collectCoverageFrom: [
    // included
    '**/src/app/**/{services,pipes,utils}/*.{js,ts}',
    '**/src/app/modules/*/components/**/*.{js,ts}',

    // excluded
    '!**/src/app/**/index.{js,ts}',
    '!**/src/app/**/*.{mock,enum,constants,module,interceptors,constant,guard,class,directive,model,helper,animations}.{js,ts}',
    '!**/node_modules/**',
  ],
};
