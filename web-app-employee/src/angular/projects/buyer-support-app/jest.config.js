const baseConfig = require('../../jest.config');

module.exports = {
  ...baseConfig,
  name: 'buyer-support-app',
  moduleNameMapper: {
    '^@root/(.*)$': '<rootDir>/../../src/app/$1',
    '^@buyer-support-app/(.*)$': '<rootDir>/src/app/$1',
    '^~shared/(.*)$': '<rootDir>/../../src/app/shared/$1',
    '^@employee-app/(.*)$': '<rootDir>../employee-app/src/app/$1',
    '^~core/(.*)$': '<rootDir>/../../src/app/core/$1',
    '^~env/environment(.*)$': '<rootDir>/../../src/environments/environment.testing.ts',
  },
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/projects/buyer-support-app/tsconfig.spec.json',
    },
  },
  collectCoverageFrom: [
    // included
    '**/src/app/**/{store,services,pipes,utils}/*.{js,ts}',

    // excluded
    '!**/src/app/**/index.{js,ts}',
    '!**/src/app/**/*.{mock,enum,constants,component,module,interceptors,constant,guard,class,directive,model,helper,animations}.{js,ts}',
    '!**/node_modules/**',
  ],
};
