import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ACCOUNTS_ROUTE } from '@root/shared/constants';

const routes: Routes = [
  { path: '', redirectTo: ACCOUNTS_ROUTE, pathMatch: 'full' },
  {
    path: ACCOUNTS_ROUTE,
    loadChildren: () => import('./modules/accounts/accounts.module').then(m => m.AccountsModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BuyerSupportRoutingModule {}
