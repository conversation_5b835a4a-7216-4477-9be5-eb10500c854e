import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { waitForAsync, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { EffectsModule } from '@ngrx/effects';
import { Dictionary } from '@ngrx/entity';
import { select, Store, StoreModule } from '@ngrx/store';
import * as faker from 'faker';
import { deserialize } from 'serialize-ts/dist';
import { ACCOUNT_LIST_MOCK, ACCOUNTS_STATE_MOCK, FINANCIAL_POSTING_MOCK } from '../../mocks';
import { FinancialAccount } from '@buyer-support-app/modules/accounts/models';
import { AccountsService } from '@buyer-support-app/modules/accounts/services/accounts.service';
import { reducers } from '@buyer-support-app/modules/accounts/store';
import * as fromAccounts from '@buyer-support-app/modules/accounts/store/accounts';
import { ACCOUNTS_FEATURE_NAME } from '@buyer-support-app/modules/accounts/store/feature-name';
import { TRANSACTIONS_MOCK } from '@employee-app/modules/transactions/mocks';
import { BaseHttpInterceptor } from '~core/interceptors/base-http-interceptor';
import { environment } from '~env/environment';
import { ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK } from '~shared/mock';
import { ErrorResponse } from '~shared/model';

describe('Accounts store integration', () => {
  let httpMock: HttpTestingController;
  let store: Store<fromAccounts.AccountsState>;
  let service: AccountsService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        StoreModule.forRoot({}, { runtimeChecks: { strictStateImmutability: true, strictActionImmutability: true } }),
        StoreModule.forFeature(ACCOUNTS_FEATURE_NAME, reducers),
        EffectsModule.forRoot([]),
        EffectsModule.forFeature([fromAccounts.AccountsEffects]),
        RouterTestingModule,
      ],
      providers: [{ provide: HTTP_INTERCEPTORS, useClass: BaseHttpInterceptor, multi: true }, AccountsService],
    });

    httpMock = TestBed.inject(HttpTestingController);
    store = TestBed.inject(Store);
    service = TestBed.inject(AccountsService);
  });

  describe('Create account', () => {
    const account = deserialize(ACCOUNT_LIST_MOCK.items[0], FinancialAccount);
    const url = `${environment.svcEmployeeBaseUrl}/api/v1/employee/accounts`;
    const body: Partial<FinancialAccount> = {
      accountNumber: account.accountNumber,
      accountName: account.accountName,
      balance: account.balance,
    };

    describe('Create account success case', () => {
      it('should return created account entity state', waitForAsync(() => {
        const entities: Dictionary<FinancialAccount> = {
          [account.id]: { ...account },
        };
        const expected: fromAccounts.AccountsState = {
          entities,
          error: null,
          postingSending: false,
          totalCount: 1,
          ids: [account.id],
        };
        const action = new fromAccounts.CreateAccount({ account: body });

        store.dispatch(action);

        const req = httpMock.expectOne(url);

        expect(req.request.body).toEqual(body);
        expect(req.request.method).toBe('POST');

        req.flush(ACCOUNT_LIST_MOCK.items[0]);

        store.pipe(select(fromAccounts.getAccountsState)).subscribe(actual => expect(actual).toEqual(expected));
      }));
    });

    describe('Create account failure', () => {
      it('should return an error when create account', waitForAsync(() => {
        const expected: fromAccounts.AccountsState = {
          entities: {},
          ids: [],
          error: deserialize(ERROR_RESPONSE_MOCK, ErrorResponse),
          postingSending: false,
          totalCount: 0,
        };
        const action = new fromAccounts.CreateAccount({ account: body });

        store.dispatch(action);
        const req = httpMock.expectOne(url);

        expect(req.request.body).toEqual(body);
        expect(req.request.method).toBe('POST');

        req.flush(ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK);

        store.pipe(select(fromAccounts.getAccountsState)).subscribe(actual => expect(actual).toEqual(expected));
      }));
    });
  });

  describe('Load accounts', () => {
    const host = window['config'].serviceBaseUrls.employee;
    const offset = 2;
    const limit = 10;
    const url = `${host}/api/v1/employee/accounts?offset=${offset}&limit=${limit}`;
    const action = new fromAccounts.LoadAccounts({ params: { offset, limit } });

    describe('Load accounts success', () => {
      it('should return accounts entity state when router navigated', waitForAsync(() => {
        const expected = ACCOUNTS_STATE_MOCK;

        store.dispatch(action);
        const req = httpMock.expectOne(url);

        expect(req.request.method).toBe('GET');

        req.flush(ACCOUNT_LIST_MOCK);

        store.pipe(select(fromAccounts.getAccountsState)).subscribe(actual => expect(actual).toEqual(expected));
      }));
    });

    describe('Load accounts failure', () => {
      it('should return error when load accounts', waitForAsync(() => {
        const expected: fromAccounts.AccountsState = {
          entities: {},
          ids: [],
          error: deserialize(ERROR_RESPONSE_MOCK, ErrorResponse),
          totalCount: 0,
          postingSending: false,
        };

        store.dispatch(action);

        const req = httpMock.expectOne(url);

        req.flush(ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK);

        store.pipe(select(fromAccounts.getAccountsState)).subscribe(actual => expect(actual).toEqual(expected));
      }));
    });
  });

  describe('Filter accounts', () => {
    it('should return accounts entity state', fakeAsync(() => {
      const accountName = faker.finance.accountName();
      const url = `${environment.svcEmployeeBaseUrl}/api/v1/employee/accounts?filter[name]=${accountName}`;
      const expected = ACCOUNTS_STATE_MOCK;

      store.dispatch(new fromAccounts.FilterAccounts({ filter: { name: accountName } }));
      tick(300);

      const req = httpMock.expectOne(encodeURI(url));
      expect(req.request.method).toBe('GET');
      req.flush(ACCOUNT_LIST_MOCK);
      tick();

      store.pipe(select(fromAccounts.getAccountsState)).subscribe(actual => expect(actual).toEqual(expected));
      tick();
    }));
  });

  describe('Create financial posting', () => {
    const host = window['config'].serviceBaseUrls.employee;
    const url = `${host}/api/v1/employee/accounts/transactions`;
    const action = new fromAccounts.CreatePosting({ posting: FINANCIAL_POSTING_MOCK });

    describe('Create posting success', () => {
      it('should return empty response', waitForAsync(() => {
        const expected: fromAccounts.AccountsState = {
          entities: {},
          ids: [],
          postingSending: false,
          error: null,
          totalCount: 0,
        };

        store.dispatch(action);

        const req = httpMock.expectOne(url);

        expect(req.request.method).toBe('POST');
        expect(req.request.body).toEqual(FINANCIAL_POSTING_MOCK);

        req.flush({});

        store.pipe(select(fromAccounts.getAccountsState)).subscribe(state => expect(state).toEqual(expected));
      }));
    });

    describe('Create posting failure', () => {
      it('should return state with error', waitForAsync(() => {
        const expected: fromAccounts.AccountsState = {
          entities: {},
          ids: [],
          totalCount: 0,
          postingSending: false,
          error: deserialize(ERROR_RESPONSE_MOCK, ErrorResponse),
        };

        store.dispatch(action);
        httpMock.expectOne(url).flush(ERROR_RESPONSE_MOCK, ERROR_RESPONSE_OPT_MOCK);

        store.pipe(select(fromAccounts.getAccountsState)).subscribe(state => expect(state).toEqual(expected));
      }));
    });
  });

  describe('Clear accounts state', () => {
    const offset = 2;
    const limit = 10;
    const host = window['config'].serviceBaseUrls.employee;
    const url = `${host}/api/v1/employee/accounts?offset=${offset}&limit=${limit}`;
    const action = new fromAccounts.LoadAccounts({ params: { offset, limit } });

    it('should reset state to initial state', waitForAsync(() => {
      const expected: fromAccounts.AccountsState = {
        entities: {},
        ids: [],
        postingSending: false,
        error: null,
        totalCount: 0,
      };
      store.dispatch(action);
      httpMock.expectOne(url).flush(TRANSACTIONS_MOCK);

      store.dispatch(new fromAccounts.ClearAccountsState());

      store.pipe(select(fromAccounts.getAccountsState)).subscribe(state => {
        expect(state).toEqual(expected);
      });
    }));
  });
});
