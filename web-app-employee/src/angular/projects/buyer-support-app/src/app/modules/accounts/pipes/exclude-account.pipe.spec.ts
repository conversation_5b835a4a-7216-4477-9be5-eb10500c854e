import { FinancialAccount } from '@buyer-support-app/modules/accounts/models';
import { ExcludeAccountPipe } from './exclude-account.pipe';

describe('ExcludeAccountPipe', () => {
  const pipe = new ExcludeAccountPipe();

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return an empty array', () => {
    const expected = [];

    expect(pipe.transform(undefined, 'id')).toEqual(expected);
    expect(pipe.transform(null, 'id')).toEqual(expected);
  });

  it('should return filtered array without item which id is provided as an argument', () => {
    const accountId = '2';
    const accounts: FinancialAccount[] = Array(3)
      .fill({})
      .map(
        (_, i) =>
          ({
            id: i.toString(),
          } as FinancialAccount),
      );
    const expected = accounts.filter(a => a.id !== accountId);

    expect(pipe.transform(accounts, accountId)).toEqual(expected);
  });

  it('should return not filtered array if accountId is not provided as an argument', () => {
    const accounts: FinancialAccount[] = Array(3)
      .fill({})
      .map(
        (_, i) =>
          ({
            id: i.toString(),
          } as FinancialAccount),
      );

    expect(pipe.transform(accounts, null)).toEqual(accounts);
    expect(pipe.transform(accounts, undefined)).toEqual(accounts);
    expect(pipe.transform(accounts, '')).toEqual(accounts);
  });
});
