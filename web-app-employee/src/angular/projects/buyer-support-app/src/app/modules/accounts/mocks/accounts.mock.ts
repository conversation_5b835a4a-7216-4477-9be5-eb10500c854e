import * as faker from 'faker';
import { FinancialAccount } from '@buyer-support-app/modules/accounts/models';
import { PaginatedResponse } from '~shared/model';

const totalCount = 5;

export const ACCOUNT_LIST_MOCK: PaginatedResponse<FinancialAccount> = {
  items: Array(totalCount)
    .fill({})
    .map(() => {
      return {
        id: faker.random.uuid(),
        accountName: faker.random.word(),
        accountNumber: faker.random.alphaNumeric(),
        balance: {
          amount: faker.finance.amount(),
          currency: 'EUR',
        },
        debit: {
          amount: faker.finance.amount(),
          currency: 'EUR',
        },
        credit: {
          amount: faker.finance.amount(),
          currency: 'EUR',
        },
        createdAt: new Date().toISOString(),
      };
    }),
  totalCount,
};
