import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { queryBuilder } from '@metromarkets/sdk-17';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { deserialize } from 'serialize-ts/dist';
import { ACCOUNTS_API } from '@buyer-support-app/modules/accounts/constants/api';
import { FinancialAccount, FinancialPosting } from '@buyer-support-app/modules/accounts/models';
import { PaginatedResponse, RequestParams } from '~shared/model';

@Injectable({
  providedIn: 'root',
})
export class AccountsService {
  constructor(private http: HttpClient) {}

  getAccounts(requestParams: RequestParams): Observable<PaginatedResponse<FinancialAccount>> {
    const params = queryBuilder.toParams(requestParams) as HttpParams;

    return this.http.get<PaginatedResponse<FinancialAccount>>(ACCOUNTS_API, { params }).pipe(
      map(({ items, totalCount }) => ({
        totalCount,
        items: items.map(i => deserialize(i, FinancialAccount)),
      })),
    );
  }

  createAccount(account: Partial<FinancialAccount>): Observable<FinancialAccount> {
    return this.http
      .post<FinancialAccount>(ACCOUNTS_API, account)
      .pipe(map(response => deserialize(response, FinancialAccount)));
  }

  createPosting(posting: FinancialPosting): Observable<null> {
    const url = `${ACCOUNTS_API}/transactions`;

    return this.http.post<null>(url, posting);
  }
}
