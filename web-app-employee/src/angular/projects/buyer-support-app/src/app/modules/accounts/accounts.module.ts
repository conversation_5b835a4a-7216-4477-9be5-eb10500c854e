import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatTableModule } from '@angular/material/table';
import { MmFormFieldModule } from '@metromarkets/components-17';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { CanDeactivateGuard } from '@buyer-support-app/modules/accounts/services/can-deactivate.guard';
import { AccountsEffects } from '@buyer-support-app/modules/accounts/store/accounts/accounts.effects';
import { ACCOUNTS_FEATURE_NAME } from '@buyer-support-app/modules/accounts/store/feature-name';
import { reducers } from '@buyer-support-app/modules/accounts/store/reducer';
import { FormErrorsModule } from '~shared/modules/form-errors/form-errors.module';

import { AccountsRoutingModule } from '@buyer-support-app/modules/accounts/accounts-routing.module';
import { AccountListComponent } from '@buyer-support-app/modules/accounts/components/account-list/account-list.component';
import { CreateAccountModalComponent } from '@buyer-support-app/modules/accounts/components/create-account-modal/create-account-modal.component';
import { FinancialPostingComponent } from '@buyer-support-app/modules/accounts/components/financial-posting/financial-posting.component';
import { ExcludeAccountPipe } from '@buyer-support-app/modules/accounts/pipes/exclude-account.pipe';
import { PostingSummaryPipe } from '@buyer-support-app/modules/accounts/pipes/posting-summary.pipe';

@NgModule({
  declarations: [
    AccountListComponent,
    CreateAccountModalComponent,
    FinancialPostingComponent,
    PostingSummaryPipe,
    ExcludeAccountPipe,
  ],
  imports: [
    CommonModule,
    MatButtonModule,
    MatMenuModule,
    MatTableModule,
    MatPaginatorModule,
    MatAutocompleteModule,
    MatIconModule,
    MmFormFieldModule,
    ReactiveFormsModule,
    FormsModule,
    FormErrorsModule,
    MatDialogModule,
    StoreModule.forFeature(ACCOUNTS_FEATURE_NAME, reducers),
    EffectsModule.forFeature([AccountsEffects]),
    AccountsRoutingModule,
  ],
  exports: [AccountListComponent],
  providers: [CanDeactivateGuard],
})
export class AccountsModule {}
