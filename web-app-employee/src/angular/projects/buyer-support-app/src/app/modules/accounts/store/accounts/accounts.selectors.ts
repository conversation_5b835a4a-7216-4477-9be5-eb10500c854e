import { createFeatureSelector, createSelector } from '@ngrx/store';
import { State } from '@buyer-support-app/modules/accounts/store/state';
import { AccountsState } from '@buyer-support-app/modules/accounts/store/accounts/accounts-state';
import { accountsAdapter } from '@buyer-support-app/modules/accounts/store/accounts/accounts.adapter';
import { ACCOUNTS_FEATURE_NAME } from '@buyer-support-app/modules/accounts/store/feature-name';
import { ErrorResponse } from '~shared/model';

const getAccountsFeatureState = createFeatureSelector(ACCOUNTS_FEATURE_NAME);

const { selectAll } = accountsAdapter.getSelectors();

export const getAccountsState = createSelector(getAccountsFeatureState, (state: State) => state.accounts);

export const getAccounts = createSelector(getAccountsState, selectAll);

export const getTotalCount = createSelector(getAccountsState, (state: AccountsState) => state.totalCount);

export const getError = createSelector(getAccountsState, (state: AccountsState) => state && state.error);

export const getInvalidParams = createSelector(getError, (error: ErrorResponse) => error && error.invalidParams);

export const isPostingSending = createSelector(getAccountsState, (state: AccountsState) => state.postingSending);
