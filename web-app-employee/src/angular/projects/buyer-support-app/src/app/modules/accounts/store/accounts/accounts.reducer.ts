import { accountsInitialState, AccountsState } from './accounts-state';
import { AccountsActionsUnion, AccountsActionTypes } from './accounts.actions';
import { accountsAdapter } from './accounts.adapter';

export function accountsReducer(state = accountsInitialState, action: AccountsActionsUnion): AccountsState {
  switch (action.type) {
    case AccountsActionTypes.LOAD_ACCOUNTS_SUCCESS:
      return accountsAdapter.setAll(action.payload.accounts.items, {
        ...state,
        error: null,
        totalCount: action.payload.accounts.totalCount,
      });
    case AccountsActionTypes.CREATE_ACCOUNT_SUCCESS:
      return accountsAdapter.upsertOne(action.payload.account, {
        ...state,
        error: null,
        totalCount: +state.totalCount + 1,
      });
    case AccountsActionTypes.LOAD_ACCOUNTS_FAILURE:
      return accountsAdapter.removeAll({
        ...state,
        error: action.payload.error,
        totalCount: 0,
      });
    case AccountsActionTypes.CREATE_ACCOUNT_FAILURE:
      return {
        ...state,
        error: action.payload.error,
      };
    case AccountsActionTypes.CREATE_POSTING:
      return { ...state, postingSending: true };
    case AccountsActionTypes.CREATE_POSTING_SUCCESS:
      return { ...state, postingSending: false };
    case AccountsActionTypes.CREATE_POSTING_FAILURE:
      return {
        ...state,
        postingSending: false,
        error: action.payload.error,
      };
    case AccountsActionTypes.CLEAR_STATE:
      return accountsInitialState;
    default:
      return state;
  }
}
