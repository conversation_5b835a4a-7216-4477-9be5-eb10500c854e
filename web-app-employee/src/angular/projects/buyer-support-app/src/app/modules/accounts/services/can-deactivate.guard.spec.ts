import { waitForAsync, TestBed } from '@angular/core/testing';
import { CanDeactivateGuard } from '@buyer-support-app/modules/accounts/services/can-deactivate.guard';
import { CanDeactivateGuarded } from '~shared/model';

class TestComponent implements CanDeactivateGuarded {
  returnValue: boolean;

  canDeactivate() {
    return this.returnValue;
  }
}

describe('CanDeactivateGuard', () => {
  let component: TestComponent;
  let guard: CanDeactivateGuard;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [TestComponent, CanDeactivateGuard],
    });

    component = TestBed.inject(TestComponent);
    guard = TestBed.inject(CanDeactivateGuard);
  });

  it("should call component's canDeactivate fn", waitForAsync(() => {
    const spy = jest.spyOn(component, 'canDeactivate');
    component.returnValue = true;
    const actual = guard.canDeactivate(component, {}, {}, {});

    expect(spy).toHaveBeenCalled();
    expect(actual).toBeTruthy();
  }));

  it("should return true if component doesn't have canDeactivate method", () => {
    const actual = guard.canDeactivate({}, {}, {}, {});
    expect(actual).toBeTruthy();
  });
});
