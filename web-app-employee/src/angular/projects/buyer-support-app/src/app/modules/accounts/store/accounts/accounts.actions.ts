import { Action } from '@ngrx/store';
import { FinancialAccount, FinancialPosting } from '@buyer-support-app/modules/accounts/models';
import { ErrorResponse, PaginatedResponse, RequestParams } from '~shared/model';

export const enum AccountsActionTypes {
  LOAD_ACCOUNTS = '[Accounts] Accounts load',
  LOAD_ACCOUNTS_SUCCESS = '[Accounts] Accounts load success',
  LOAD_ACCOUNTS_FAILURE = '[Accounts] Accounts load failure',

  CREATE_ACCOUNT = '[Accounts] Account create',
  CREATE_ACCOUNT_SUCCESS = '[Accounts] Account create success',
  CREATE_ACCOUNT_FAILURE = '[Accounts] Account create failure',

  FILTER_ACCOUNTS = '[Accounts] Account search',

  CREATE_POSTING = '[Accounts] Posting create',
  CREATE_POSTING_SUCCESS = '[Accounts] Posting create success',
  CREATE_POSTING_FAILURE = '[Accounts] Posting create failure',

  CLEAR_STATE = '[Accounts] Clear accounts state',
}

export class LoadAccounts implements Action {
  readonly type = AccountsActionTypes.LOAD_ACCOUNTS;

  constructor(public payload: { params: RequestParams }) {}
}

export class LoadAccountsSuccess implements Action {
  readonly type = AccountsActionTypes.LOAD_ACCOUNTS_SUCCESS;

  constructor(public payload: { accounts: PaginatedResponse<FinancialAccount> }) {}
}

export class LoadAccountsFailure implements Action {
  readonly type = AccountsActionTypes.LOAD_ACCOUNTS_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export class CreateAccount implements Action {
  readonly type = AccountsActionTypes.CREATE_ACCOUNT;

  constructor(public payload: { account: Partial<FinancialAccount> }) {}
}

export class CreateAccountSuccess implements Action {
  readonly type = AccountsActionTypes.CREATE_ACCOUNT_SUCCESS;

  constructor(public payload: { account: FinancialAccount }) {}
}

export class CreateAccountFailure implements Action {
  readonly type = AccountsActionTypes.CREATE_ACCOUNT_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export class FilterAccounts implements Action {
  readonly type = AccountsActionTypes.FILTER_ACCOUNTS;

  constructor(public payload: { filter: { [key: string]: any } }) {}
}

export class CreatePosting implements Action {
  readonly type = AccountsActionTypes.CREATE_POSTING;

  constructor(public payload: { posting: FinancialPosting }) {}
}

export class CreatePostingSuccess implements Action {
  readonly type = AccountsActionTypes.CREATE_POSTING_SUCCESS;
}

export class CreatePostingFailure implements Action {
  readonly type = AccountsActionTypes.CREATE_POSTING_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export class ClearAccountsState implements Action {
  readonly type = AccountsActionTypes.CLEAR_STATE;
}

export type AccountsActionsUnion =
  | LoadAccounts
  | LoadAccountsSuccess
  | LoadAccountsFailure
  | CreateAccount
  | CreateAccountSuccess
  | CreateAccountFailure
  | FilterAccounts
  | CreatePosting
  | CreatePostingSuccess
  | CreatePostingFailure
  | ClearAccountsState;
