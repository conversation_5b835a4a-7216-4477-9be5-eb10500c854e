import { deserialize } from 'serialize-ts/dist';
import { ACCOUNT_LIST_MOCK } from '@buyer-support-app/modules/accounts/mocks/accounts.mock';
import { FinancialAccount } from '@buyer-support-app/modules/accounts/models';
import { AccountsState } from '@buyer-support-app/modules/accounts/store/accounts';
import { StoreUtils } from '~shared/utils/store.utils';

const accounts = ACCOUNT_LIST_MOCK.items.map(a => deserialize(a, FinancialAccount));

export const ACCOUNTS_STATE_MOCK: AccountsState = {
  entities: StoreUtils.fromArrayToDictionary<FinancialAccount>(accounts),
  ids: ACCOUNT_LIST_MOCK.items.map(a => a.id),
  error: null,
  totalCount: ACCOUNT_LIST_MOCK.totalCount,
  postingSending: false,
};
