import { ACCOUNT_LIST_MOCK, ACCOUNTS_STATE_MOCK } from '../../mocks';
import * as fromAccounts from '@buyer-support-app/modules/accounts/store/accounts';
import { ERROR_RESPONSE_MOCK } from '~shared/mock';

describe('Accounts Selectors', () => {
  const accountsDefaultMockState = ACCOUNTS_STATE_MOCK;

  describe('getAccounts', () => {
    it('should return accounts', () => {
      const actual = fromAccounts.getAccounts.projector(accountsDefaultMockState);
      expect(actual).toEqual(ACCOUNT_LIST_MOCK.items);
    });
  });

  describe('getTotalCount', () => {
    it('should return total count', () => {
      const actual = fromAccounts.getTotalCount.projector(accountsDefaultMockState);
      expect(actual).toBe(ACCOUNTS_STATE_MOCK.totalCount);
    });

    describe('getError', () => {
      const state = { ...accountsDefaultMockState, error: ERROR_RESPONSE_MOCK };

      it('should return error', () => {
        const actual = fromAccounts.getError.projector(state);
        expect(actual).toEqual(ERROR_RESPONSE_MOCK);
      });
    });

    describe('getInvalidParams', () => {
      it('should return invalidParams field from error', () => {
        const actual = fromAccounts.getInvalidParams.projector(ERROR_RESPONSE_MOCK);
        expect(actual).toEqual(ERROR_RESPONSE_MOCK.invalidParams);
      });
    });

    describe('isPostingSending', () => {
      it('should return isPostingSending value', () => {
        const actual = fromAccounts.isPostingSending.projector(accountsDefaultMockState);
        expect(actual).toBe(accountsDefaultMockState.postingSending);
      });
    });
  });
});
