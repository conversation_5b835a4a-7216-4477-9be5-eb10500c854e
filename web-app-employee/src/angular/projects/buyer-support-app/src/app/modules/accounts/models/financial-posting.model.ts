import { ArraySerializer, Field, Type } from 'serialize-ts/dist';
import { ModelMetadataSerializer } from 'serialize-ts/dist/serializers/model-metadata.serializer';
import { CreditAccountPosting } from '@buyer-support-app/modules/accounts/models/credit-account-posting.model';

export class FinancialPosting {
  @Field()
  debitAccountId: string;

  @Field()
  @Type(new ArraySerializer(new ModelMetadataSerializer(CreditAccountPosting)))
  creditAccountList: CreditAccountPosting[];
}
