import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, debounceTime, map, switchMap } from 'rxjs/operators';
import { FinancialAccount } from '@buyer-support-app/modules/accounts/models';
import { AccountsService } from '@buyer-support-app/modules/accounts/services/accounts.service';
import {
  AccountsActionTypes,
  CreateAccount,
  CreateAccountFailure,
  CreateAccountSuccess,
  CreatePosting,
  CreatePostingFailure,
  CreatePostingSuccess,
  FilterAccounts,
  LoadAccounts,
  LoadAccountsFailure,
  LoadAccountsSuccess,
} from '../../store/accounts/accounts.actions';
import { FILTER_DEBOUNCE_TIME } from '../../constants';

@Injectable()
export class AccountsEffects {
  loadAccounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountsActionTypes.LOAD_ACCOUNTS),
      map((action: LoadAccounts) => action.payload),
      switchMap(({ params }) =>
        this.accountsService.getAccounts(params).pipe(
          map(accounts => new LoadAccountsSuccess({ accounts })),
          catchError(error => of(new LoadAccountsFailure({ error }))),
        ),
      ),
    ),
  );

  filterAccounts$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountsActionTypes.FILTER_ACCOUNTS),
      debounceTime(FILTER_DEBOUNCE_TIME),
      map((action: FilterAccounts) => action.payload),
      map(payload => new LoadAccounts({ params: { filter: payload.filter } })),
    ),
  );

  createAccount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountsActionTypes.CREATE_ACCOUNT),
      map((action: CreateAccount) => action.payload),
      switchMap(({ account }) =>
        this.accountsService.createAccount(account).pipe(
          map((createdAccount: FinancialAccount) => new CreateAccountSuccess({ account: createdAccount })),
          catchError(error => of(new CreateAccountFailure({ error }))),
        ),
      ),
    ),
  );

  createPosting$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AccountsActionTypes.CREATE_POSTING),
      map((action: CreatePosting) => action.payload),
      switchMap(({ posting }) =>
        this.accountsService.createPosting(posting).pipe(
          map(() => new CreatePostingSuccess()),
          catchError(error => of(new CreatePostingFailure({ error }))),
        ),
      ),
    ),
  );

  constructor(private actions$: Actions, private accountsService: AccountsService) {}
}
