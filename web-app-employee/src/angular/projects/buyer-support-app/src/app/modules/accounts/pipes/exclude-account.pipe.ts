import { Pipe, PipeTransform } from '@angular/core';
import { FinancialAccount } from '@buyer-support-app/modules/accounts/models';

@Pipe({
  name: 'excludeAccount',
})
export class ExcludeAccountPipe implements PipeTransform {
  transform(accounts: FinancialAccount[], accountId: string): FinancialAccount[] {
    if (!accounts) {
      return [];
    } else if (!accountId) {
      return accounts;
    }

    return accounts.filter(account => account.id !== accountId);
  }
}
