import { EntityState } from '@ngrx/entity';
import { accountsAdapter } from './accounts.adapter';
import { ErrorResponse } from '~shared/model';
import { FinancialAccount } from '@buyer-support-app/modules/accounts/models';

export interface AccountsState extends EntityState<FinancialAccount> {
  error: ErrorResponse;
  totalCount: number;
  postingSending: boolean;
}

export const accountsInitialState: AccountsState = accountsAdapter.getInitialState({
  error: null,
  totalCount: 0,
  postingSending: false,
});
