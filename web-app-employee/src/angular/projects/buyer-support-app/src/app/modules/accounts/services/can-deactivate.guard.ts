import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { FinancialPostingComponent } from '@buyer-support-app/modules/accounts/components/financial-posting/financial-posting.component';

@Injectable()
export class CanDeactivateGuard {
  canDeactivate(
    component: FinancialPostingComponent,
    currentRoute: ActivatedRouteSnapshot,
    currentState: RouterStateSnapshot,
    nextState: RouterStateSnapshot,
  ): boolean {
    return component.canDeactivate ? component.canDeactivate() : true;
  }
}
