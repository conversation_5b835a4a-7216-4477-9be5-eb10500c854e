import { CurrencyPipe } from '@angular/common';
import * as faker from 'faker';
import { CreditAccountFormGroup } from '@buyer-support-app/modules/accounts/models';
import { PostingSummaryPipe } from './posting-summary.pipe';

describe('PostingSummaryPipe', () => {
  const pipe = new PostingSummaryPipe();

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return an empty string when value is empty', () => {
    expect(pipe.transform([])).toBe('');
    expect(pipe.transform(null)).toBe('');
    expect(pipe.transform(undefined)).toBe('');
  });

  it('should return concatenated string with provided values', () => {
    const accounts: CreditAccountFormGroup[] = Array(2)
      .fill({})
      .map(
        () =>
          ({
            account: {
              accountName: faker.finance.accountName(),
            },
            description: faker.random.words(),
            amount: faker.finance.amount(),
            currency: faker.finance.currencyCode(),
          } as CreditAccountFormGroup),
      );
    const arrowRight = '&#8594;';
    const amount1 = getAmount(accounts[0].amount, accounts[0].currency);
    const amount2 = getAmount(accounts[1].amount, accounts[1].currency);
    const account1 = `${arrowRight} ${amount1} ${accounts[0].description} ${arrowRight} ${accounts[0].account.accountName}`;
    const account2 = `${arrowRight} ${amount2} ${accounts[1].description} ${arrowRight} ${accounts[1].account.accountName}`;
    const expected = `${account1}<br />${account2}`;

    expect(pipe.transform(accounts)).toBe(expected);
  });
});

function getAmount(amount, currency) {
  return new CurrencyPipe('en').transform(amount, currency);
}
