import { CurrencyPipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';
import { isEmpty } from 'lodash';
import { CreditAccountFormGroup } from '@buyer-support-app/modules/accounts/models';

@Pipe({
  name: 'postingSummary',
})
export class PostingSummaryPipe implements PipeTransform {
  transform(accounts: CreditAccountFormGroup[]): string {
    if (isEmpty(accounts)) {
      return '';
    }
    const arrowRight = '&#8594;';
    return accounts
      .map(a => {
        const amount = new CurrencyPipe('en').transform(a.amount, a.currency);

        return `${arrowRight} ${amount} ${a.description} ${arrowRight} ${a.account.accountName}`;
      })
      .join('<br />');
  }
}
