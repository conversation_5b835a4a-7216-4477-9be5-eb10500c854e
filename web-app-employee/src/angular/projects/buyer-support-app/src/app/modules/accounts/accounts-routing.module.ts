import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AccountListComponent } from '@buyer-support-app/modules/accounts/components/account-list/account-list.component';
import { FinancialPostingComponent } from '@buyer-support-app/modules/accounts/components/financial-posting/financial-posting.component';
import { CanDeactivateGuard } from '@buyer-support-app/modules/accounts/services/can-deactivate.guard';
import { FINANCIAL_POSTING_ROUTE } from '~shared/constants';

const routes: Routes = [
  { path: '', component: AccountListComponent },
  { path: FINANCIAL_POSTING_ROUTE, component: FinancialPostingComponent, canDeactivate: [CanDeactivateGuard] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountsRoutingModule {}
