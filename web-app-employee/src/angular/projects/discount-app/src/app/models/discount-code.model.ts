import { Field, Name, PrimitiveSerializer, Type, ArraySerializer } from 'serialize-ts/dist';
import { AmountType } from './amount-type.enum';
import { DiscountReasonType } from './discount-reason-type.enum';
import { DiscountStatus } from './discount-status.enum';
import { CodeType } from './code-type.enum';

class DiscountCodeBase {
  @Field()
  id?: string;

  @Field()
  codeType?: CodeType;

  @Field()
  country: string;

  @Field()
  @Name('type')
  amountType: AmountType;

  @Field()
  amount: number;

  @Field()
  minSpendingAmount: number;

  @Field()
  maxDiscountAmount: number;

  @Field()
  sellerId: string;

  @Field()
  deliveryType: number;

  @Field()
  maxUsage: number;

  @Field()
  usagePerCustomer: number;

  @Field()
  startTime: string;

  @Field()
  endTime: string;

  @Field()
  purpose: DiscountReasonType;

  @Field()
  reason: number;

  @Field()
  reasonDetails: string;

  @Field()
  codeGroup: string;

  @Field()
  createdBy?: string;

  @Field()
  createdByEmail?: string;

  @Field()
  updatedBy?: string;

  @Field()
  @Type(new ArraySerializer(new PrimitiveSerializer()))
  categories: string[];

  @Field()
  @Type(new ArraySerializer(new PrimitiveSerializer()))
  buyerCategories: string[];

  @Field()
  status?: DiscountStatus;

  @Field()
  allowPromo: number;

  @Field()
  @Type(new ArraySerializer(new PrimitiveSerializer()))
  brands?: string[];

  @Field()
  brandAction: string;

  @Field()
  vasAction?: string;

  @Field()
  @Type(new ArraySerializer(new PrimitiveSerializer()))
  buyerTypes?: string[]|number[];
}

export class DiscountCodeSerial extends DiscountCodeBase {
  @Field()
  codeLength?: number;

  @Field()
  @Name('prefix')
  codePrefix: string;

  @Field()
  @Name('numberOfCodes')
  codeCount: number;

  @Field()
  code?: string;
}
export class DiscountCodeSingular extends DiscountCodeBase {
  @Field()
  code: string;
}
export type DiscountCode = DiscountCodeSingular | DiscountCodeSerial;
