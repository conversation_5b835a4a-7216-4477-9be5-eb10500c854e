import { Field, Model } from 'serialize-ts/dist';
import { ArraySerializer, PrimitiveSerializer, Type } from 'serialize-ts';
import {
  BuyerCategoryListComponent
} from "@discount-app/modules/discount-code/components/buyer-category-list/buyer-category-list.component";

@Model()
export class BuyerCategory {
  @Field()
  id: string;

  @Field()
  name?: string;

  @Field()
  @Type(new ArraySerializer(new PrimitiveSerializer()))
  children?: BuyerCategory[];
}

export class WrappedBuyerCategory {
  protected id: string;
  protected name: string;
  protected isExpanded?: boolean = false;
  protected isChecked?: boolean = false;
  protected wereChildrenLoaded?: boolean = false;
  protected children: string[] = [];
  protected ancestors: string[] = [];
  protected listComponent: BuyerCategoryListComponent;

  constructor(
    category: BuyerCategory,
    listComponent: BuyerCategoryListComponent,
    parent: WrappedBuyerCategory | null = null,
    wereChildrenLoaded: boolean | null = null
  ) {
    this.id = category.id;
    this.name = category.name;
    this.listComponent = listComponent;
    this.setChildren(category.children, wereChildrenLoaded);

    if (parent instanceof WrappedBuyerCategory) {
      this.setAncestors([parent.id, ...parent.ancestors]);
    }

    if (listComponent.isSelectedCategory(this.id)) {
      this.setChecked(true);
      if (parent instanceof WrappedBuyerCategory) {
        parent.setExpanded(true);
      }
    } else if (listComponent.isAncestorOfSelectedCategory(this.id)) {
      this.setExpanded(true);
    }

    listComponent.getFlattenedCategories().set(this.id, this);
  }

  getName(): string {
    return this.name;
  }

  getId(): string {
    return this.id;
  }

  setChildren(children: BuyerCategory[], wereChildrenLoaded: boolean | null = null) {
    this.children = children.map((child) => {
        if (!this.listComponent.getCategoryInstance(child.id)) {
          new WrappedBuyerCategory(child, this.listComponent, this);
        }
        return child.id;
      },
    );

    //we are using the length logic because when we forst load the categories - 2nd level come empty by default
    this.setWereChildrenLoaded(wereChildrenLoaded ?? (this.children.length > 0));
  }

  getChildren(): string[] {
    return this.children;
  }

  setChecked(isChecked: boolean): WrappedBuyerCategory {
    this.isChecked = isChecked;

    return this;
  }

  getIsChecked(): boolean {
    return this.isChecked;
  }

  setExpanded(isExpanded: boolean): WrappedBuyerCategory {
    this.isExpanded = isExpanded;

    return this;
  }

  getIsExpanded(): boolean {
    return this.isExpanded;
  }

  setWereChildrenLoaded(isWereChildrenLoaded: boolean): WrappedBuyerCategory {
    this.wereChildrenLoaded = isWereChildrenLoaded;

    return this;
  }

  getWereChildrenLoaded = (): boolean => {
    return this.wereChildrenLoaded;
  };

  setAncestors(ancestors: string[]): WrappedBuyerCategory {
    this.ancestors = ancestors;

    return this;
  }

  getAncestors(): string[] {
    return this.ancestors;
  }

  getParent(): string | null {
    return this.ancestors[0] ?? null;
  }

  getIsDisabled() {
    return this.getWereChildrenLoaded() && this.children.length === 0;
  }
}

export class SelectedBuyerCategory {
  @Field()
  category: BuyerCategory;

  @Field()
  @Type(new ArraySerializer(new PrimitiveSerializer()))
  parentPath?: string[];
}
