import { Field, Model } from 'serialize-ts/dist';
import { ArraySerializer, PrimitiveSerializer, Type } from 'serialize-ts';

@Model()
export class Category {
  @Field()
  id: string;
  @Field()
  name?: string;
  @Field()
  hasChild?: string;
}

export class SelectedCategory {
  @Field()
  category: Category;

  @Field()
  @Type(new ArraySerializer(new PrimitiveSerializer()))
  parentPath: string[];
}
