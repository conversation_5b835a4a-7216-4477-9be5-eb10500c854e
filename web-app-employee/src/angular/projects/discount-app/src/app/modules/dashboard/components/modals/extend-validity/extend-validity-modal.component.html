<div class="extend-modal" test-target="extend-modal">
  <div class="header" fxLayout="row" fxLayoutAlign="space-between center">
    <h3>Extend discount code</h3>
    <mat-icon class="close" (click)="closeDialogue()">close</mat-icon>
  </div>
  <mm-table-loader *ngIf="isSending$ | async"></mm-table-loader>
  <div class="body">
    <div class="modal-body">
      <div fxLayout="row" fxLayoutAlign="space-between center" class="item">
        <label>Discount code</label>
        <span>{{ codeOrPrefix }}</span>
      </div>
      <div fxLayout="row" fxLayoutAlign="space-between center" class="item">
        <label>Current end date</label>
        <span test-target="current-end-date-discount-code">{{ endTime }}</span>
      </div>
      <div fxLayout="row" fxLayoutAlign="space-between center" class="item">
        <label>Set new end date</label>
      </div>
      <div class="extend-form">
        <form [formGroup]="extendForm">
          <div class="date-group">
            <label class="field-label">End Date / Time <span class="required">*</span></label>
            <div class="field-group" fxLayout="row" fxFill fxflex="100">
              <app-date-picker placeholder="Select end date" controlName="endDate" fxflex="50"></app-date-picker>
              <input
                fxflex="50"
                class="time-field"
                type="time"
                placeholder="hh:mm"
                matInput
                formControlName="endTime"
                [matAutocomplete]="auto"
                test-target="extend-code-time-select"
              />
              <mat-autocomplete #auto="matAutocomplete">
                <mat-option
                  test-target="extend-validity-time-selection"
                  class="time-option"
                  *ngFor="let option of TimeOptions"
                  [value]="option"
                >
                  {{ option }}
                </mat-option>
              </mat-autocomplete>
            </div>
            <span class="info">All the dates should be in sales channel time.</span>
            <div
              test-target="discount-code-extend-network-error"
              class="error-message"
              *ngIf="(error$ | async)?.detail; let error"
            >
              {{ error }}
            </div>
            <div
              test-target="discount-code-extend-form-error"
              class="error-message"
              *ngIf="extendForm.errors && extendForm.errors['endTimeError']"
            >
              New end date should be greater than the old one.
            </div>
            <div
              test-target="discount-code-extend-form-error"
              class="error-message"
              *ngIf="extendForm.errors && extendForm.errors['endDateInPastError']"
            >
              End Date should be in the future.
            </div>
          </div>
          <div class="confirmation" fxFlexFill fxLayout="row" *ngIf="showConfirm && !extendForm.errors">
            <div class="confirmation__icon">
              <mm-icon name="info" type="fluid"></mm-icon>
            </div>
            <div fxFlex class="extend-message" test-target="discount-code-extend-message">
              You are extending the discount code {{ discount.code }} from <br />
              <br />
              <b>Date: {{ originalEndDateAndTime.date }}, Time: {{ originalEndDateAndTime.time }}</b> to

              <b>Date:{{ updatedEndDateAndTime.date }}, Time: {{ updatedEndDateAndTime.time }}.</b>
              <br />
              <br />
              Please note: The extension cannot be undone, please check the details and confirm.
            </div>
          </div>
          <div class="button-container" fxLayout="row" fxLayoutAlign="end center">
            <button
              mm-button
              primary
              (click)="extend()"
              test-target="extend-code-modal-button"
              [disabled]="extendForm.pristine || extendForm.invalid"
            >
              Confirm
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
