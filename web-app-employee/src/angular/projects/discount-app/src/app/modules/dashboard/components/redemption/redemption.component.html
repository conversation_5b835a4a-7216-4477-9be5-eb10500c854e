<div class="wrapper">
  <app-search-bar
    (addFilterEvent)="searchByFields($event)"
    (removeFilterEvent)="onClearSearchField($event)"
    [searchQuery]=""
    [filterTitleMap]="filterTitleMap"
    [options]="searchOptions"
    test-target="discount-code-redemption-filters"
    (clearAllFiltersEvent)="onClearAllFilters()"
  >
  </app-search-bar>

  <table
    *ngIf="(redemptionHistory$ | async).length > 0"
    mm-table
    [dataSource]="redemptionHistory$ | async"
    test-target="discount-redemption-table"
  >
    <ng-container mmColumnDef="code">
      <th mmHeaderCell *mmHeaderCellDef>Discount code</th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        <a href="/discount/view/{{ element.discountCode.id }}" target="_blank">{{ element.discountCode.code }}</a>
      </td>
    </ng-container>
    <ng-container mmColumnDef="redemptionDate">
      <th mmHeaderCell *mmHeaderCellDef>Redemption Date (GMT +2)</th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        {{ element.redeemedAt | date: 'dd.MM.yyyy HH:mm':'+2' }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="orderNumber">
      <th mmHeaderCell *mmHeaderCellDef>Order Number</th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        <a href="/sales-orders/orders/{{ element.orderId }}" target="_blank">{{ element.orderNumber }}</a>
      </td>
    </ng-container>
    <ng-container mmColumnDef="customerId">
      <th mmHeaderCell *mmHeaderCellDef>Customer ID</th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        {{ element.customerId }}
      </td>
    </ng-container>
    <tr mm-header-row *mmHeaderRowDef="COLUMNS; sticky: true"></tr>
    <tr mm-row *mmRowDef="let row; columns: COLUMNS"></tr>
  </table>
  <mm-table-loader *ngIf="isLoading"></mm-table-loader>
  <mm-table-pagination
    #paginator
    (page)="changePage($event)"
    [itemsPerPageLabel]="'PAGINATOR.ITEMS_PER_PAGE' | translate"
    [pageSizeOptions]="pageSizeOptions"
    [pageSizeSelectedOption]="limit"
    [pageSizeTotal]="totalCount$ | async"
    [rangeLabel]="'PAGINATOR.OF_RANGE' | translate"
    [enableJumpToPage]="enableJumpToPage"
    [enablePageLinks]="enablePageLinks"
    [attr.test-target]="'discount-redemption-paginator'"
  >
  </mm-table-pagination>
</div>
