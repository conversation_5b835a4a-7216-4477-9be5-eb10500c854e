import { Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { DEFAULT_LIMIT } from '@discount-app/constants';
import { DUPLICATE, EDIT, VIEW } from '@discount-app/constants/routes';
import { AmountType } from '@discount-app/models';
import { DiscountCode, DiscountCodeSerial, DiscountCodeSingular } from '@discount-app/models/discount-code.model';
import { DiscountStatus } from '@discount-app/models/discount-status.enum';
import { DashboardService } from '@discount-app/modules/dashboard/services/dashboard.service';
import * as fromDiscountCodeDeactivate from '@discount-app/modules/dashboard/store/deactivate';
import * as fromDiscountCodeDownload from '@discount-app/modules/dashboard/store/download';
import * as fromDiscountCodeOverview from '@discount-app/modules/dashboard/store/overview';
import { select, Store } from '@ngrx/store';
import { AuthService } from '@root/modules/user-auth/services/auth.service';
import { DISCOUNT_ROUTE, ITEMS_PER_PAGE } from '@root/shared/constants';
import { RequestParams, UserRolesEnum } from '@root/shared/model';
import { QueryParamsHandler } from '@root/shared/services/query-params-handler.service';
import { Observable, Subject, takeUntil, tap } from 'rxjs';
import { Timezones } from '@discount-app/modules/discount-code/constants/country-timezones';
import { MmTablePaginationComponent } from '@metromarkets/components-17';
import { Filter } from '~shared/modules/search-bar/models';
import {
  DISCOUNT_BACK_URL_QUERY_PARAMS,
  DISCOUNT_CODE_FILTER_TITLES,
  DISCOUNT_CODE_SEARCH_OPTIONS,
} from '@discount-app/modules/dashboard/constants/discount-code-overview';
import { first } from 'rxjs/operators';
import { SetSalesChannelDropdownService } from '~shared/services/set-saleschannel-dropdown.service';
import { MatDialog } from '@angular/material/dialog';
import { ExtendValidityModalComponent } from '@discount-app/modules/dashboard/components/modals/extend-validity/extend-validity-modal.component';
import * as fromDiscountCodeExtend from '@discount-app/modules/dashboard/store/extend';
import moment from 'moment-timezone';
import { DateUtil } from '@discount-app/utils/date.util';
import { INIT_QUERY_PARAMS } from '@sales-orders-app/modules/orders/constants';
import { isEmpty } from 'lodash';

const ENABLE_PAGE_LINKS = true;
const ENABLE_JUMP_TO_PAGE = true;

@Component({
  selector: 'discount-app-overview',
  templateUrl: './overview.component.html',
  styleUrls: ['./overview.component.scss'],
  providers: [QueryParamsHandler],
})
export class OverviewComponent implements OnInit, OnDestroy {
  @ViewChild(MmTablePaginationComponent) paginator: MmTablePaginationComponent;

  public discountCodes$: Observable<DiscountCode[]>;
  public totalCount$: Observable<number>;
  public isLoading: boolean;
  public currentPage = 0;
  public shouldLoadMore: boolean;
  limit = DEFAULT_LIMIT;
  public COLUMNS = ['createdByEmail', 'code', 'date', 'country', 'amount', 'numOfUsage', 'status', 'actions'];
  public pageSizeOptions = [
    ITEMS_PER_PAGE.SIZE_10,
    ITEMS_PER_PAGE.SIZE_25,
    ITEMS_PER_PAGE.SIZE_50,
    ITEMS_PER_PAGE.SIZE_100,
  ];
  public enableJumpToPage = ENABLE_JUMP_TO_PAGE;
  public enablePageLinks = ENABLE_PAGE_LINKS;
  public searchOptions = DISCOUNT_CODE_SEARCH_OPTIONS;
  public filterTitleMap = DISCOUNT_CODE_FILTER_TITLES;
  public searchQuery$: Observable<string | Params>;
  public canUserExtendDiscountDuration = false;
  public canUserDeactivate = false;
  public canUserEdit = false;
  public isSuperAdmin = false;
  public discountStatus = DiscountStatus;
  public canUserCreate = false;
  public displayAllCodes = false;
  private destroy$ = new Subject();
  public backButtonQueryParams = DISCOUNT_BACK_URL_QUERY_PARAMS;

  constructor(
    private store: Store<fromDiscountCodeOverview.OverviewState>,
    private authService: AuthService,
    private router: Router,
    private discountCodeService: DashboardService,
    private paramsHandler: QueryParamsHandler,
    private setSalesChannelDropdownService: SetSalesChannelDropdownService,
    private dialog: MatDialog,
    private activatedRoute: ActivatedRoute,
  ) {}

  ngOnDestroy(): void {
    this.destroy$.next(void 0);
    this.destroy$.complete();
  }

  duplicate(id: string) {
    this.router.navigate([ `${ DISCOUNT_ROUTE }/${ DUPLICATE }/${ id }` ], { queryParams: this.backButtonQueryParams });
  }

  edit(id: string) {
    this.router.navigate([ `${ DISCOUNT_ROUTE }/${ EDIT }/${ id }` ], { queryParams: this.backButtonQueryParams });
  }

  view(id: string) {
    this.router.navigate([ `${ DISCOUNT_ROUTE }/${ VIEW }/${ id }` ], { queryParams: this.backButtonQueryParams });
  }

  streamingCsv(discountCode: DiscountCode): void {
    this.store.dispatch(new fromDiscountCodeDownload.DownloadDiscountCode({ discountCode }));
  }

  deactivate(discountCode: string) {
    this.store.dispatch(
      new fromDiscountCodeDeactivate.DeactivateDiscountCode({
        discountCode,
      }),
    );
  }

  canEdit(discount: DiscountCode): boolean {
    return (
      this.authService.userRoles.includes(UserRolesEnum.METROMARKETS_DISCOUNT_CODE_EDIT) &&
      discount.status === DiscountStatus.Pending
    );
  }

  canDeactivate(discount: DiscountCode): boolean {
    return (
      this.authService.userRoles.includes(UserRolesEnum.METROMARKETS_DISCOUNT_CODE_DEACTIVATE) &&
      (discount.status === DiscountStatus.Active || discount.status === DiscountStatus.Pending)
    );
  }

  canDownload(discount: DiscountCode): boolean {
    return discount instanceof DiscountCodeSerial;
  }

  getDiscountAmount(discount: DiscountCode): string {
    if (discount.amountType === AmountType.Percentage) {
      return `${discount.amount} %`;
    }
    return `${discount.amount} €`;
  }

  ngOnInit() {
    this.discountCodes$ = this.store.pipe(select(fromDiscountCodeOverview.getDiscountCodes));
    this.store.pipe(select(fromDiscountCodeOverview.getIsLoading)).subscribe(val => {
      this.isLoading = val;
    });
    this.totalCount$ = this.store.pipe(select(fromDiscountCodeOverview.getTotalCount));

    this.setSalesChannelDropdownService.setSalesChannelFilter(
      this.searchOptions,
      UserRolesEnum.METROMARKETS_DISCOUNT_CODE_VIEW,
      'countries',
    );
    this.searchQuery$ = this.store.pipe(select(fromDiscountCodeOverview.getFilterQuery), first());
    this.setPermissions();
    this.store
      .pipe(select(fromDiscountCodeOverview.getDisplayAllCodes), takeUntil(this.destroy$))
      .subscribe(displayCodes => (this.displayAllCodes = !displayCodes));
    this.searchQuery$.subscribe(filter => {
      if (!isEmpty(filter)) {
        this.loadAllCodes();
      }
    });
    this.activatedRoute.queryParams
      .pipe(
        tap((queryParams: RequestParams) => {
          this.backButtonQueryParams = { ...queryParams, ...DISCOUNT_BACK_URL_QUERY_PARAMS };
        })
      ).subscribe();
  }

  changePage(pageEvent): void {
    const { currentPageIndex, pageSizeSelectedOption } = pageEvent;
    this.limit = pageSizeSelectedOption;
    const nextPaging = {
      offset: currentPageIndex * pageSizeSelectedOption,
      limit: pageSizeSelectedOption,
    };
    this.paramsHandler.mergeQueryParams(nextPaging);
  }

  shortenEmail(email: string): string {
    return email.split('@')[0];
  }

  getDiscountCodeOrPrefix(discountCode: DiscountCode): string {
    if (discountCode instanceof DiscountCodeSingular) {
      return discountCode.code;
    }
    return discountCode.codePrefix + '*'.repeat(discountCode.code.replace(discountCode.codePrefix, '').length);
  }

  getTime(time: string, country: string) {
    return DateUtil.convertUTCTimeStringToReadableString(time, country);
  }

  public searchByFields(filters: Array<Filter>): void {
    const queryParams = {};
    for (const filter of filters) {
      queryParams[filter.name] = filter.value;
    }
    const offset = 0;
    this.paramsHandler.changeQueryParams(
      {
        offset: 0,
        limit: this.limit,
        filter: queryParams,
      },
      'merge',
    );
    this.resetPaginator();
    this.loadDiscountsWithFilter();
  }

  onClearSearchField(field: string): void {
    this.paramsHandler.changeQueryParams(
      {
        offset: 0,
        limit: this.limit,
        filter: { [field]: null },
      },
      'merge',
    );
    this.resetPaginator();
  }

  setPermissions(): void {
    const createDiscountCodeRoles = [
      UserRolesEnum.METROMARKETS_DISCOUNT_CODE_CREATE_LVL_1,
      UserRolesEnum.METROMARKETS_DISCOUNT_CODE_CREATE_LVL_2,
      UserRolesEnum.METROMARKETS_DISCOUNT_CODE_CREATE_LVL_3,
    ];

    this.canUserExtendDiscountDuration = this.canUserCreate = createDiscountCodeRoles.some(val =>
      this.authService.userRoles.includes(val),
    );
    this.canUserDeactivate = this.authService.userRoles.includes(UserRolesEnum.METROMARKETS_DISCOUNT_CODE_DEACTIVATE);
    this.canUserEdit = this.authService.userRoles.includes(UserRolesEnum.METROMARKETS_DISCOUNT_CODE_EDIT);
    this.isSuperAdmin = this.authService.userRoles.includes(UserRolesEnum.METROMARKETS_DISCOUNT_CODE_CREATE_LVL_1);
  }

  extendTheCode(discount: DiscountCode): void {
    const dialogRef = this.dialog.open(ExtendValidityModalComponent, {
      data: { discount, codeOrPrefix: this.getDiscountCodeOrPrefix(discount) },
      width: '525px',
      height: 'auto',
    });
    const country = discount.country.toLowerCase();
    moment.tz.setDefault(country);
    dialogRef
      .afterClosed()
      .subscribe((result: { success: boolean; discount: DiscountCode; endDate: string; endTime: string }) => {
        if (result && result.success) {
          const timeData = {
            hours: Number(result.endTime.split(':')[0]),
            minutes: Number(result.endTime.split(':')[1]),
          };
          const _endTime = moment.tz(new Date(result.endDate).toDateString(), Timezones[country]);
          _endTime.set({ h: timeData.hours, m: timeData.minutes });
          this.store.dispatch(new fromDiscountCodeExtend.Reset());
          this.store.dispatch(
            new fromDiscountCodeOverview.UpdateDiscountEndTime({
              discount: result.discount,
              endTime: _endTime.utc().format(),
            }),
          );
        }
        moment.tz.setDefault();
      });
  }

  loadAllCodes() {
    this.store.dispatch(new fromDiscountCodeOverview.ToggleDisplayAllCodes({ value: true }));
    this.paramsHandler.changeQueryParams(
      {
        ...INIT_QUERY_PARAMS,
      },
      'merge',
    );
    this.paramsHandler.refresh();
  }

  private resetPaginator(): void {
    this.paginator.firstPage();
  }

  private loadDiscountsWithFilter() {
    this.store.dispatch(new fromDiscountCodeOverview.ToggleDisplayAllCodes({ value: true }));
  }

}
