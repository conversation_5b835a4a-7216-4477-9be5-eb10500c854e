import { State } from '@discount-app/modules/dashboard/store';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DISCOUNT_FEATURE_NAME } from '../feature-name';
import { ExtendState } from '@discount-app/modules/dashboard/store/extend/extend.state';

export const getModuleFeatureState = createFeatureSelector(DISCOUNT_FEATURE_NAME);

export const getExtendState = createSelector(getModuleFeatureState, (state: State) => state.extend);

export const getIsSending = createSelector(getExtendState, (state: ExtendState) => state.isSending);

export const getIsSuccess = createSelector(getExtendState, (state: ExtendState) => state.isSuccess);

export const getErrors = createSelector(getExtendState, (state: ExtendState) => state.error);
