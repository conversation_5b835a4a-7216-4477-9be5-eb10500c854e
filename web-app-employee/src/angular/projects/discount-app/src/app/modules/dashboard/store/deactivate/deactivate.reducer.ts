import { DeactivateActionTypes, DiscountCodeActionsUnion } from './deactivate.actions';
import { deactivateInitialState, DeactivateState } from './deactivate.state';

export function deactivateReducer(state = deactivateInitialState, action: DiscountCodeActionsUnion): DeactivateState {
  switch (action.type) {
    case DeactivateActionTypes.DEACTIVATE_DISCOUNT_CODE:
      return {
        ...state,
        error: null,
        isSuccess: false,
        isSending: true,
      };
    case DeactivateActionTypes.DEACTIVATE_DISCOUNT_CODE_SUCCESS:
      return {
        ...state,
        error: null,
        isSending: false,
        isSuccess: true,
      };
    case DeactivateActionTypes.DEACTIVATE_DISCOUNT_CODE_FAILURE:
      return {
        ...state,
        isSending: false,
        isSuccess: false,
        error: action.payload.error,
      };
    default:
      return state;
  }
}
