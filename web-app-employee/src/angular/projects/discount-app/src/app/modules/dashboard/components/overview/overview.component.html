<div class="wrapper" [ngClass]="{ 'no-data': (discountCodes$ | async).length === 0 }">
  <app-search-bar
    (addFilterEvent)="searchByFields($event)"
    (removeFilterEvent)="onClearSearchField($event)"
    [searchQuery]="searchQuery$ | async"
    [filterTitleMap]="filterTitleMap"
    [options]="searchOptions"
    test-target="discount-code-overview-filters"
  >
  </app-search-bar>
  <table
    *ngIf="(discountCodes$ | async).length > 0"
    mm-table
    [dataSource]="discountCodes$ | async"
    test-target="discount-overview-table"
  >
    <ng-container mmColumnDef="createdByEmail">
      <th mmHeaderCell *mmHeaderCellDef>Created By</th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        {{ shortenEmail(element.createdByEmail) }} <br />
        {{ element.createdAt | date: 'dd.MM.yyyy HH:mm' }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="code">
      <th mmHeaderCell *mmHeaderCellDef>Discount Code</th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        {{ getDiscountCodeOrPrefix(element) }} <br />
        <span *ngIf="canDownload(element)">({{ element.codeCount }})</span>
      </td>
    </ng-container>
    <ng-container mmColumnDef="date">
      <th mmHeaderCell *mmHeaderCellDef>
        Start Date / End Date
        <div class="info-icon" [mmTooltip]="tooltipContent">
          <span class="info-icon__text">i</span>
        </div>
        <ng-template #tooltipContent>
          <p>All the dates are in sales channel time.</p>
        </ng-template>
      </th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        <span test-target="discount-code-start-time">{{ getTime(element.startTime, element.country) }}</span
        ><br />
        <span test-target="discount-code-end-time">{{ getTime(element.endTime, element.country) }}</span>
      </td>
    </ng-container>
    <ng-container mmColumnDef="country">
      <th mmHeaderCell *mmHeaderCellDef>Country / Seller</th>
      <th mmHeaderCell *mmHeaderCellDef>Country / Seller</th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        {{ element.country.toUpperCase() }} <br />
        Metro
      </td>
    </ng-container>
    <ng-container mmColumnDef="amount">
      <th mmHeaderCell *mmHeaderCellDef>Amount/No of Usage</th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        {{ getDiscountAmount(element) }} <br />
        {{ element.purpose }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="numOfUsage">
      <th mmHeaderCell *mmHeaderCellDef>No of Usage</th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        {{ element.maxUsage }}
      </td>
    </ng-container>
    <ng-container mmColumnDef="status">
      <th mmHeaderCell *mmHeaderCellDef>Status</th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        <discount-app-discount-status [status]="element.status"></discount-app-discount-status>
      </td>
    </ng-container>
    <ng-container mmColumnDef="actions">
      <th mmHeaderCell *mmHeaderCellDef>Action</th>
      <td mmCell [mmTextAlign]="center" *mmCellDef="let element">
        <button mat-icon-button [matMenuTriggerFor]="menu" test-target="overview-menu-button">
          <mat-icon>more_horiz</mat-icon>
        </button>
        <mat-menu test-target="actions-menu" class="actions-menu" #menu="matMenu">
          <button mat-menu-item (click)="view(element.id)" test-target="view-details-discount-code">
            View Details
          </button>
          <button
            mat-menu-item
            *ngIf="
             element.status !== discountStatus.Deactivated &&
              ((canUserExtendDiscountDuration &&
              (element.status === discountStatus.Redeemed || element.status === discountStatus.Active)) || isSuperAdmin)
            "
            (click)="extendTheCode(element)"
            test-target="extend-discount-code"
          >
            Extend Discount Duration
          </button>
          <button
            *ngIf="canDownload(element)"
            mat-menu-item
            (click)="streamingCsv(element)"
            test-target="download-discount-code"
          >
            Download
          </button>
          <button
            *ngIf="isSuperAdmin || (canUserEdit && element.status === discountStatus.Pending)"
            (click)="edit(element.id)"
            mat-menu-item
            test-target="edit-discount-code"
          >
            Edit
          </button>
          <button
            *ngIf="
              canUserDeactivate &&
              (element.status === discountStatus.Active || element.status === discountStatus.Pending)
            "
            (click)="deactivate(element.code)"
            class="actions-menu__deactivate"
            mat-menu-item
            test-target="deactivate-discount-code"
          >
            Deactivate
          </button>
          <button
            *ngIf="canUserCreate"
            class="actions-menu__deactivate"
            mat-menu-item
            test-target="duplicate-discount-code"
            (click)="duplicate(element.id)"
          >
            Duplicate
          </button>
        </mat-menu>
      </td>
    </ng-container>
    <tr mm-header-row *mmHeaderRowDef="COLUMNS; sticky: true"></tr>
    <tr mm-row *mmRowDef="let row; columns: COLUMNS"></tr>
  </table>
  <mm-table-loader *ngIf="isLoading"></mm-table-loader>
  <mm-table-pagination
    #paginator
    (page)="changePage($event)"
    [itemsPerPageLabel]="'PAGINATOR.ITEMS_PER_PAGE' | translate"
    [pageSizeOptions]="pageSizeOptions"
    [pageSizeSelectedOption]="limit"
    [pageSizeTotal]="totalCount$ | async"
    [rangeLabel]="'PAGINATOR.OF_RANGE' | translate"
    [enableJumpToPage]="enableJumpToPage"
    [enablePageLinks]="enablePageLinks"
    [attr.test-target]="'discount-overview-paginator'"
  >
  </mm-table-pagination>
  <div *ngIf="displayAllCodes" class="button-container">
    <button mm-button (click)="loadAllCodes()">Show all Discount Codes</button>
  </div>
</div>
