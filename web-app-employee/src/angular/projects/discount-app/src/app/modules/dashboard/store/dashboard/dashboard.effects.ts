import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action } from '@ngrx/store';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { OverviewActionTypes, LoadDiscountCodesFailure } from '../overview/overview.actions';
import { ResetAlert, SetAlert } from './dashboard.actions';
import { AlertType } from '@discount-app/models/alert.type.enum';
import { ErrorResponse } from '@root/shared/model';
import isNil from 'lodash/isNil';
import { CreateActionTypes, CreateDiscountCodeSuccess } from '@discount-app/modules/discount-code/store/create';
import { EditActionTypes, UpdateDiscountCodeSuccess } from '@discount-app/modules/discount-code/store/edit';
import { DeactivateActionTypes, DeactivateDiscountCodeSuccess } from '@discount-app/modules/dashboard/store/deactivate';
import { ROUTER_NAVIGATED } from '@ngrx/router-store';

@Injectable()
export class DashboardEffects {
  resetAlertOnNavigation$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(ROUTER_NAVIGATED),
      map(() => new ResetAlert()),
    ),
  );

  loadDiscountCodesError$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(OverviewActionTypes.LOAD_DISCOUNT_CODES_FAILURE),
      map((action: LoadDiscountCodesFailure) => action.payload),
      map(
        ({ error }) =>
          new SetAlert({
            alert: { type: AlertType.Error, message: DashboardEffects.getErrorMessage(error) },
          }),
      ),
    ),
  );

  updateDiscountCode$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(EditActionTypes.UPDATE_DISCOUNT_CODE_SUCCESS),
      map((action: UpdateDiscountCodeSuccess) => action.payload),
      map(({ message }) => DashboardEffects.setAlertMessage(message)),
    ),
  );

  createDiscountCode$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(CreateActionTypes.CREATE_DISCOUNT_CODE_SUCCESS),
      map((action: CreateDiscountCodeSuccess) => action.payload),
      map(({ message }) => DashboardEffects.setAlertMessage(message)),
    ),
  );

  deactivateDiscountCode$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(DeactivateActionTypes.DEACTIVATE_DISCOUNT_CODE_SUCCESS),
      map((action: DeactivateDiscountCodeSuccess) => action.payload),
      map(({ message }) => DashboardEffects.setAlertMessage(message)),
    ),
  );

  constructor(private actions$: Actions) {}

  private static getErrorMessage(error: ErrorResponse) {
    if (!isNil(error.title)) {
      return error.title;
    }
    return 'Something went wrong!';
  }

  private static setAlertMessage(message: string) {
    return new SetAlert({
      alert: { type: AlertType.Success, message },
    });
  }
}
