import { Action } from '@ngrx/store';
import { ErrorResponse } from '~shared/model/error-response.model';
import { DiscountCode } from '@discount-app/models/discount-code.model';

export enum DownloadActionTypes {
  DOWNLOAD_DISCOUNT_CODE = '[Discount Code] Discount Code download',
  DOWNLOAD_DISCOUNT_CODE_SUCCESS = '[Discount Code] Discount Code download success',
  DOWNLOAD_DISCOUNT_CODE_FAILURE = '[Discount Code] Discount Code download failure',
}
export class DownloadDiscountCode implements Action {
  readonly type = DownloadActionTypes.DOWNLOAD_DISCOUNT_CODE;

  constructor(public payload: { discountCode: DiscountCode }) {}
}
export class DownloadDiscountCodeSuccess implements Action {
  readonly type = DownloadActionTypes.DOWNLOAD_DISCOUNT_CODE_SUCCESS;
}

export class DownloadDiscountCodeFailure implements Action {
  readonly type = DownloadActionTypes.DOWNLOAD_DISCOUNT_CODE_FAILURE;
  constructor(public payload: { error: ErrorResponse }) {}
}
export type DiscountCodeActionsUnion = DownloadDiscountCode | DownloadDiscountCodeSuccess | DownloadDiscountCodeFailure;
