import { HttpClient, HttpEvent, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  DISCOUNT_API,
  DISCOUNT_CODE_EXTEND_VALIDITY_API,
  DISCOUNT_DOWNLOAD_SERIALIZED_API,
} from '@discount-app/constants/api';
import { DiscountCodeRedemption } from '@discount-app/models/discount-code-redemption';
import { DiscountCode, DiscountCodeSerial, DiscountCodeSingular } from '@discount-app/models/discount-code.model';
import { queryBuilder } from '@metromarkets/sdk-17';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { PaginatedResponse, RequestParams } from '~shared/model';
import isNil from 'lodash/isNil';
import { deserialize } from 'serialize-ts';
import { ExtendCodeValidityRequest } from '@discount-app/models';
import { MIN_CHARACTER_COUNT_FOR_FILTER } from '@discount-app/constants';

@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  constructor(private http: HttpClient) {}

  getDiscountCodes(requestParams: RequestParams): Observable<PaginatedResponse<DiscountCode>> {
    const params = queryBuilder.toParams(requestParams) as HttpParams;
    this.validateDiscountCodeParams(params);
    return this.http
      .get<PaginatedResponse<DiscountCode>>(`${DISCOUNT_API}/discount-code`, {
        params,
      })
      .pipe(
        map(response => {
          return {
            items: response.items.map(code => {
              if (isNil(code['prefix'])) {
                return deserialize(code, DiscountCodeSingular);
              }
              return deserialize(code, DiscountCodeSerial);
            }),
            totalCount: response.totalCount,
          };
        }),
      );
  }

  loadDiscountCodeHistory(requestParams: RequestParams): Observable<PaginatedResponse<DiscountCodeRedemption>> {
    const params = queryBuilder.toParams(requestParams) as HttpParams;
    return this.http
      .get<PaginatedResponse<DiscountCodeRedemption>>(`${DISCOUNT_API}/redemption-history`, {
        params,
      })
      .pipe(
        map(response => ({
          totalCount: response.totalCount,
          items: response.items.map(i => deserialize(i, DiscountCodeRedemption)),
        })),
      );
  }

  deactivateDiscountCode(discountCode: string): Observable<string> {
    return this.http.post<string>(`${DISCOUNT_API}/deactivate-code`, {
      discountCode,
    });
  }

  downloadSerializedCode(discountCode: DiscountCode): Observable<HttpEvent<any>> {
    const params = queryBuilder.toParams(
      queryBuilder.toParams({
        responseType: 'blob',
        observe: 'response',
      }),
    ) as HttpParams;

    return this.http.get(`${DISCOUNT_DOWNLOAD_SERIALIZED_API}/${discountCode.codeGroup}`, {
      params,
      responseType: 'blob',
      reportProgress: true,
      observe: 'events',
      headers: new HttpHeaders({ 'Content-Type': 'application/text' }),
    });
  }

  extendDiscountCode(codeGroup: string, request: ExtendCodeValidityRequest): Observable<DiscountCode> {
    return this.http.put<DiscountCode>(`${DISCOUNT_CODE_EXTEND_VALIDITY_API}/${codeGroup}`, {
      ...request,
    });
  }

  private validateDiscountCodeParams(params: HttpParams) {
    const checkParams = ['filter[code]', 'filter[createdBy]'];
    Object.keys(params).forEach(key => {
      if (checkParams.indexOf(key) !== -1 && params[key].length < MIN_CHARACTER_COUNT_FOR_FILTER) {
        delete params[key];
      }
    });
  }
}
