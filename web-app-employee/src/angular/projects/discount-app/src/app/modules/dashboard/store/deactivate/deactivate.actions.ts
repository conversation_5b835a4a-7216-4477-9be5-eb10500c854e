import { Action } from '@ngrx/store';
import { ErrorResponse } from '~shared/model/error-response.model';

export enum DeactivateActionTypes {
  DEACTIVATE_DISCOUNT_CODE = '[Discount Code] Discount Code deactivate',
  DEACTIVATE_DISCOUNT_CODE_SUCCESS = '[Discount Code] Discount Code deactivate success',
  DEACTIVATE_DISCOUNT_CODE_FAILURE = '[Discount Code] Discount Code deactivate failure',
}
export class DeactivateDiscountCode implements Action {
  readonly type = DeactivateActionTypes.DEACTIVATE_DISCOUNT_CODE;

  constructor(public payload: { discountCode: string }) {}
}
export class DeactivateDiscountCodeFailure implements Action {
  readonly type = DeactivateActionTypes.DEACTIVATE_DISCOUNT_CODE_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export class DeactivateDiscountCodeSuccess implements Action {
  readonly type = DeactivateActionTypes.DEACTIVATE_DISCOUNT_CODE_SUCCESS;

  constructor(public payload: { message: string }) {}
}
export type DiscountCodeActionsUnion =
  | DeactivateDiscountCode
  | DeactivateDiscountCodeSuccess
  | DeactivateDiscountCodeFailure;
