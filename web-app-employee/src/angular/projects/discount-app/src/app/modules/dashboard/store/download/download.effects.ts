import { HttpEvent, HttpEventType, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action } from '@ngrx/store';
import { downloadFile } from '@root/shared/utils/download.utils';
import { Observable, of } from 'rxjs';
import { catchError, filter, map, switchMap } from 'rxjs/operators';
import { DashboardService } from '../../services/dashboard.service';
import {
  DownloadActionTypes,
  DownloadDiscountCode,
  DownloadDiscountCodeFailure,
  DownloadDiscountCodeSuccess,
} from './download.actions';

@Injectable()
export class DownloadEffects {
  downloadDiscountCode$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(DownloadActionTypes.DOWNLOAD_DISCOUNT_CODE),
      map((action: DownloadDiscountCode) => action.payload.discountCode),
      switchMap(discountCode =>
        this.dashboardService.downloadSerializedCode(discountCode).pipe(
          filter((event: HttpEvent<any>) => event.type === HttpEventType.Response),
          map(
            (event: HttpResponse<any>) => {
              downloadFile(event.body, 'discount-codes-export', 'text/csv');
              return new DownloadDiscountCodeSuccess();
            },
            catchError(error => of(new DownloadDiscountCodeFailure({ error }))),
          ),
        ),
      ),
    ),
  );
  constructor(private actions$: Actions, private dashboardService: DashboardService) {}
}
