import { State } from '@discount-app/modules/dashboard/store';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DISCOUNT_FEATURE_NAME } from '../feature-name';
import { DownloadState } from './download.state';

export const getModuleFeatureState = createFeatureSelector(DISCOUNT_FEATURE_NAME);

export const getDownloadState = createSelector(getModuleFeatureState, (state: State) => state.download);

export const getIsDownloading = createSelector(getDownloadState, (state: DownloadState) => state.isDownloading);

export const getIsSuccess = createSelector(getDownloadState, (state: DownloadState) => state.isSuccess);

export const getErrors = createSelector(getDownloadState, (state: DownloadState) => state.error);
