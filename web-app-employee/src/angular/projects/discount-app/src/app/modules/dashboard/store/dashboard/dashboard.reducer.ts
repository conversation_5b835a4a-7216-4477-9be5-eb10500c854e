import { DiscountCodeActionTypes, DiscountCodeActionsUnion } from './dashboard.actions';
import { dashboardInitialState, DashboardState } from './dashboard.state';

export function dashboardReducer(state = dashboardInitialState, action: DiscountCodeActionsUnion): DashboardState {
  switch (action.type) {
    case DiscountCodeActionTypes.DASHBOARD_SET_ALERT:
      return { ...state, alert: action.payload.alert };
    case DiscountCodeActionTypes.DASHBOARD_RESET_ALERT:
      return {
        ...state,
        alert: null,
      };
    default:
      return state;
  }
}
