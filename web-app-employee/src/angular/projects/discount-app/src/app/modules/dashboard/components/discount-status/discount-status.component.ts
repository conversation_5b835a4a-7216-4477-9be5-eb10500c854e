import { Component, ChangeDetectionStrategy, Input } from '@angular/core';
import { DiscountStatus } from '@discount-app/models/discount-status.enum';

@Component({
  selector: 'discount-app-discount-status',
  templateUrl: './discount-status.component.html',
  styleUrls: ['./discount-status.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DiscountStatusComponent {
  @Input()
  status: DiscountStatus;

  getStatusColor() {
    switch (this.status) {
      case DiscountStatus.Deactivated:
        return '#d7796a';
      case DiscountStatus.Active:
        return '#85d9b4';
      case DiscountStatus.Expired:
        return '#f2aea4';
      case DiscountStatus.Pending:
        return '#e9ab77';
      case DiscountStatus.Redeemed:
        return '#fff466';
    }

    return '';
  }
}
