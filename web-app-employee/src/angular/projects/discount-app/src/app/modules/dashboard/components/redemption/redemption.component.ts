import { Component, OnInit, ViewChild } from '@angular/core';

import { DEFAULT_LIMIT } from '@discount-app/constants';

import { DiscountCodeRedemption } from '@discount-app/models/discount-code-redemption';
import { select, Store } from '@ngrx/store';
import * as fromDiscountCodeRedemption from '@discount-app/modules/dashboard/store/redemption';
import { Observable } from 'rxjs';
import { SEARCH_OPTIONS } from '@discount-app/modules/dashboard/constants/redemption-search-options';
import { FILTER_TITLES } from '@discount-app/modules/dashboard/constants/redemption-title-filter';
import { Filter } from '~shared/modules/search-bar/models';
import { ITEMS_PER_PAGE } from '~shared/constants';
import { QueryParamsHandler } from '~shared/services/query-params-handler.service';
import { MmTablePaginationComponent } from '@metromarkets/components-17';

const ENABLE_PAGE_LINKS = true;
const ENABLE_JUMP_TO_PAGE = true;

@Component({
  selector: 'discount-app-redemption',
  templateUrl: './redemption.component.html',
  styleUrls: ['./redemption.component.scss'],
  providers: [QueryParamsHandler],
})
export class RedemptionComponent implements OnInit {
  @ViewChild(MmTablePaginationComponent) paginator: MmTablePaginationComponent;
  public redemptionHistory$: Observable<DiscountCodeRedemption[]>;
  totalCount$: Observable<number>;
  public isLoading: boolean;
  public currentPage = 0;
  public shouldLoadMore: boolean;
  limit = DEFAULT_LIMIT;
  public COLUMNS = ['code', 'redemptionDate', 'orderNumber', 'customerId'];
  public searchOptions = SEARCH_OPTIONS;
  public filterTitleMap = FILTER_TITLES;
  public pageSizeOptions = [
    ITEMS_PER_PAGE.SIZE_10,
    ITEMS_PER_PAGE.SIZE_25,
    ITEMS_PER_PAGE.SIZE_50,
    ITEMS_PER_PAGE.SIZE_100,
  ];
  public enableJumpToPage = ENABLE_JUMP_TO_PAGE;
  public enablePageLinks = ENABLE_PAGE_LINKS;
  private queryParams = {};

  constructor(
    private store: Store<fromDiscountCodeRedemption.RedemptionState>,
    private paramsHandler: QueryParamsHandler,
  ) {}

  ngOnInit() {
    this.redemptionHistory$ = this.store.pipe(select(fromDiscountCodeRedemption.getDiscountCodeHistory));
    this.store.pipe(select(fromDiscountCodeRedemption.getIsLoading)).subscribe(val => {
      this.isLoading = val;
    });
    this.totalCount$ = this.store.pipe(select(fromDiscountCodeRedemption.getTotalCount));
  }

  changePage(pageEvent): void {
    const { currentPageIndex, pageSizeSelectedOption } = pageEvent;
    this.limit = pageSizeSelectedOption;
    const nextPaging = {
      offset: currentPageIndex * pageSizeSelectedOption,
      limit: pageSizeSelectedOption,
      ...this.queryParams,
    };
    this.paramsHandler.mergeQueryParams(nextPaging);
  }

  searchByFields(filters: Array<Filter>): void {
    this.currentPage = 0;
    for (const filter of filters) {
      this.queryParams[filter.name] = filter.value;
    }
    const offset = 0;
    this.paramsHandler.changeQueryParams(
      {
        offset,
        limit: this.limit,
        ...this.queryParams,
      },
      'merge',
    );
    this.resetPaginator();
  }

  onClearSearchField(field: string): void {
    this.queryParams = {
      ...this.queryParams,
      [field]: null,
    };
    const offset = 0;
    this.paramsHandler.changeQueryParams({ offset, limit: this.limit, ...this.queryParams }, 'merge');
    this.resetPaginator();
  }

  public onClearAllFilters() {
    this.resetQueryParams();
    this.currentPage = 0;
    const offset = 0;
    this.paramsHandler.changeQueryParams({ offset, limit: this.limit, ...this.queryParams }, 'merge');
    this.resetPaginator();
  }

  private resetPaginator(): void {
    this.paginator.firstPage();
  }

  private resetQueryParams(): void {
    this.queryParams = {
      orderNumber: null,
      discountCode: null,
      redeemedAtStartDate: null,
      redeemedAtEndDate: null,
    };
  }
}
