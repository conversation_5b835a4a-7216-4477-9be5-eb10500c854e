import * as faker from 'faker';
import { deserialize } from 'serialize-ts';
import { DiscountCodeRedemption } from '@discount-app/models/discount-code-redemption';
const totalCount = 10;
const minCodeLength = 10;
const maxCodeLength = 20;
const orderNumberLength = 12;
export const REDEMPTION_HISTORY_MOCK = Array(totalCount)
  .fill({})
  .map(() => {
    return {
      discountCode: {
        id: faker.random.uuid(),
        code: faker.random.alphaNumeric(faker.random.number({ min: minCodeLength, max: maxCodeLength })),
      },
      orderId: faker.random.uuid(),
      orderNumber: `O20-${faker.random.alphaNumeric(orderNumberLength)}`,
      customerId: faker.random.uuid(),
      redeemedAt: faker.date.recent(),
    };
  })
  .map(e => deserialize(e, DiscountCodeRedemption));
