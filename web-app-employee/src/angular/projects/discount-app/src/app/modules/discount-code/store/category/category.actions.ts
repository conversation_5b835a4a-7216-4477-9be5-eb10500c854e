import { Action } from '@ngrx/store';
import { ErrorResponse } from '~shared/model/error-response.model';
import { PaginatedResponse, RequestParams } from '~shared/model';
import { Category, SelectedCategory } from '@discount-app/models/category.model';
import { Country } from '@discount-app/models/countries.enum';

export enum CategoryActionTypes {
  LOAD_CATEGORIES_ROOT = '[Category] Root Categories load',
  LOAD_CATEGORIES_CHILD = '[Category] Child Categories load',
  LOAD_CATEGORY_PARENT_PATH = '[Category] Parent path load',
  LOAD_CATEGORY_PARENT_PATH_SUCCESS = '[Category] Parent path load success',
  LOAD_CATEGORY_PARENT_PATH_FAILURE = '[Category] Parent path load failure',
  LOAD_CATEGORIES_ROOT_SUCCESS = '[Category] Categories root load success',
  LOAD_CATEGORIES_CHILD_SUCCESS = '[Category] Categories child load success',
  LOAD_CATEGORIES_FAILURE = '[Category] Categories load failure',
  SELECT_CATEGORIES = '[Category] Categories select',
  RESET_STORE = '[Category] Reset categories'
}

export class LoadRootCategories implements Action {
  readonly type = CategoryActionTypes.LOAD_CATEGORIES_ROOT;

  constructor(public payload: { params: RequestParams; country: Country }) {}
}

export class LoadChildCategories implements Action {
  readonly type = CategoryActionTypes.LOAD_CATEGORIES_CHILD;

  constructor(public payload: { params: RequestParams; categoryId: string; country: Country }) {}
}

export class LoadRootCategoriesSuccess implements Action {
  readonly type = CategoryActionTypes.LOAD_CATEGORIES_ROOT_SUCCESS;

  constructor(
    public payload: {
      categories: PaginatedResponse<Category>;
    },
  ) {}
}

export class LoadChildCategoriesSuccess implements Action {
  readonly type = CategoryActionTypes.LOAD_CATEGORIES_CHILD_SUCCESS;

  constructor(
    public payload: {
      categories: PaginatedResponse<Category>;
      categoryId: string;
    },
  ) {}
}

export class LoadCategoriesFailure implements Action {
  readonly type = CategoryActionTypes.LOAD_CATEGORIES_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export class LoadCategoryParentPath implements Action {
  readonly type = CategoryActionTypes.LOAD_CATEGORY_PARENT_PATH;

  constructor(
    public payload: {
      params: RequestParams;
      categoryId: string;
      country: Country;
    },
  ) {}
}

export class LoadCategoryParentPathSuccess implements Action {
  readonly type = CategoryActionTypes.LOAD_CATEGORY_PARENT_PATH_SUCCESS;

  constructor(
    public payload: {
      childrenParentPaths;
      categoryId: string;
    },
  ) {}
}

export class LoadCategoryParentPathFailure implements Action {
  readonly type = CategoryActionTypes.LOAD_CATEGORY_PARENT_PATH_FAILURE;

  constructor(public payload: { error: ErrorResponse }) {}
}

export class SelectCategories implements Action {
  readonly type = CategoryActionTypes.SELECT_CATEGORIES;

  constructor(public selectedCategories: SelectedCategory[] | Category[], public isAllSelected: boolean) {}
}

export class ResetCategories implements Action {
  readonly type = CategoryActionTypes.RESET_STORE;

  constructor () { }
}
export type CategoryActionsUnion =
  | LoadRootCategories
  | LoadChildCategories
  | LoadRootCategoriesSuccess
  | LoadChildCategoriesSuccess
  | LoadCategoriesFailure
  | SelectCategories
  | LoadCategoryParentPath
  | LoadCategoryParentPathSuccess
  | LoadCategoryParentPathFailure
  | ResetCategories;
