import { State } from '@discount-app/modules/dashboard/store';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DISCOUNT_FEATURE_NAME } from '../feature-name';
import { DashboardState } from './dashboard.state';

export const getModuleFeatureState = createFeatureSelector(DISCOUNT_FEATURE_NAME);

export const getDashboardState = createSelector(getModuleFeatureState, (state: State) => state.dashboard);

export const getAlert = createSelector(getDashboardState, (state: DashboardState) => state.alert);
