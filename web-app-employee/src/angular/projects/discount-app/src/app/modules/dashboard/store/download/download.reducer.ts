import { DownloadActionTypes, DiscountCodeActionsUnion } from './download.actions';
import { downloadInitialState, DownloadState } from './download.state';

export function downloadReducer(state = downloadInitialState, action: DiscountCodeActionsUnion): DownloadState {
  switch (action.type) {
    case DownloadActionTypes.DOWNLOAD_DISCOUNT_CODE:
      return {
        ...state,
        error: null,
        isSuccess: false,
        isDownloading: true,
      };
    case DownloadActionTypes.DOWNLOAD_DISCOUNT_CODE_SUCCESS:
      return {
        ...state,
        error: null,
        isDownloading: false,
        isSuccess: true,
      };
    case DownloadActionTypes.DOWNLOAD_DISCOUNT_CODE_FAILURE:
      return {
        ...state,
        isDownloading: false,
        isSuccess: false,
        error: action.payload.error,
      };
    default:
      return state;
  }
}
