import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CREATE, OVERVIEW, REDEMPTION } from '@discount-app/constants/routes';
import { Alert } from '@discount-app/models/alert.model';
import { AlertType } from '@discount-app/models/alert.type.enum';
import * as fromDiscountCodeDashboard from '@discount-app/modules/dashboard/store/dashboard';
import { MmAlertConfig, MmAlertMode, MmAlertSize, MmAlertType } from '@metromarkets/components-17';
import { select, Store } from '@ngrx/store';
import { DISCOUNT_ROUTE } from '@root/shared/constants';
import { AuthService } from '@root/modules/user-auth/services/auth.service';
import { UserRolesEnum } from '@root/shared/model';
import { isEmpty } from '~shared/utils/common';

@Component({
  selector: 'discount-app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit {
  public alert: Alert;
  public TABS = [
    { url: OVERVIEW, title: 'Overview' },
    { url: REDEMPTION, title: 'Redemption' },
  ];
  isOpenAlert = false;
  private timeout = 5000;

  constructor(
    private router: Router,
    private store: Store<fromDiscountCodeDashboard.DashboardState>,
    private authService: AuthService,
  ) {}

  ngOnInit() {
    this.setStoreListeners();
  }

  isActive(tabUrl: string): boolean {
    const url = this.router.url;
    return tabUrl === url.substring(url.lastIndexOf('/') + 1, url.lastIndexOf('/') + tabUrl.length + 1);
  }

  setStoreListeners(): void {
    this.store.pipe(select(fromDiscountCodeDashboard.getAlert)).subscribe(val => {
      this.alert = val;
      if (!isEmpty(val)) {
        this.isOpenAlert = true;
        this.setTimeoutCloseAlert();
      }
    });
  }

  createDiscountCode(): void {
    this.router.navigate([`${DISCOUNT_ROUTE}/${CREATE}`]);
  }

  getAlertConfig(): MmAlertConfig {
    const title = this.alert.type === AlertType.Success ? 'Success' : 'Error';
    return {
      type: this.alert.type === AlertType.Success ? MmAlertType.success : MmAlertType.error,
      title,
      message: this.alert.message,
      size: MmAlertSize.large,
      mode: MmAlertMode.banner,
      target: 'alert',
    };
  }

  canCreate(): boolean {
    const createDiscountCodeRoles = [
      UserRolesEnum.METROMARKETS_DISCOUNT_CODE_CREATE_LVL_1,
      UserRolesEnum.METROMARKETS_DISCOUNT_CODE_CREATE_LVL_2,
      UserRolesEnum.METROMARKETS_DISCOUNT_CODE_CREATE_LVL_3,
    ];

    return createDiscountCodeRoles.some(val => this.authService.userRoles.includes(val));
  }

  public onCloseAlert() {
    this.isOpenAlert = false;
  }

  setTimeoutCloseAlert() {
    setTimeout(() => {
      this.isOpenAlert = false;
    }, this.timeout);
  }
}
