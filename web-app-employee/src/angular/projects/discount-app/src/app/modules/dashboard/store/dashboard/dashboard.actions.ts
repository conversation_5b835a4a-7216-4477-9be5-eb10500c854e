import { Alert } from '@discount-app/models/alert.model';
import { Action } from '@ngrx/store';

export enum DiscountCodeActionTypes {
  DASHBOARD_SET_ALERT = '[Discount Code] Discount Code dashboard set alert',
  DASHBOARD_RESET_ALERT = '[Discount Code] Discount Code dashboard reset alert',
}

export class SetAlert implements Action {
  readonly type = DiscountCodeActionTypes.DASHBOARD_SET_ALERT;

  constructor(public payload: { alert: Alert }) {}
}

export class ResetAlert implements Action {
  readonly type = DiscountCodeActionTypes.DASHBOARD_RESET_ALERT;
}

export type DiscountCodeActionsUnion = SetAlert | ResetAlert;
