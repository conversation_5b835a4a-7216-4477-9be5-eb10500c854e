import { State } from '@discount-app/modules/dashboard/store';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DISCOUNT_FEATURE_NAME } from '../feature-name';
import { DeactivateState } from './deactivate.state';

export const getModuleFeatureState = createFeatureSelector(DISCOUNT_FEATURE_NAME);

export const getDeactivateState = createSelector(getModuleFeatureState, (state: State) => state.deactivate);

export const getIsSending = createSelector(getDeactivateState, (state: DeactivateState) => state.isSending);

export const getIsSuccess = createSelector(getDeactivateState, (state: DeactivateState) => state.isSuccess);

export const getErrors = createSelector(getDeactivateState, (state: DeactivateState) => state.error);
