import { Injectable } from '@angular/core';
import { DEACTIVATE_SUCCESS, DEFAULT_LIMIT, DEFAULT_OFFSET } from '@discount-app/constants';
import { LoadDiscountCodes } from '@discount-app/modules/dashboard/store/overview/overview.actions';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { DashboardService } from '../../services/dashboard.service';
import {
  DeactivateActionTypes,
  DeactivateDiscountCode,
  DeactivateDiscountCodeFailure,
  DeactivateDiscountCodeSuccess,
} from './deactivate.actions';
import { QueryParamsService } from '~shared/services/query-params.service';
import { ActivatedRoute } from '@angular/router';
import { isEmpty } from 'lodash';

@Injectable()
export class DeactivateEffects {
  deactivateDiscountCode$: Observable<Action> = createEffect(() =>
    this.actions$.pipe(
      ofType(DeactivateActionTypes.DEACTIVATE_DISCOUNT_CODE),
      map((action: DeactivateDiscountCode) => action.payload.discountCode),
      switchMap((discountCode: string) =>
        this.dashboardService.deactivateDiscountCode(discountCode).pipe(
          switchMap(() => {
            const { queryParams } = this.activatedRoute.snapshot;
            return [
              new DeactivateDiscountCodeSuccess({ message: DEACTIVATE_SUCCESS }),
              new LoadDiscountCodes({
                params: !isEmpty(queryParams)
                  ? this.queryParamsService.buildFilterQueryParamsFromRoute(queryParams)
                  : {
                      offset: DEFAULT_OFFSET,
                      limit: DEFAULT_LIMIT,
                    },
              }),
            ];
          }),
          catchError(error => of(new DeactivateDiscountCodeFailure({ error }))),
        ),
      ),
    ),
  );
  constructor(
    private actions$: Actions,
    private dashboardService: DashboardService,
    private readonly queryParamsService: QueryParamsService,
    private readonly activatedRoute: ActivatedRoute,
  ) {}
}
