<div class="dashboard">
  <div *ngIf="alert" class="dashboard__alert">
    <mm-alert [alertConfig]="getAlertConfig()" *ngIf="isOpenAlert" (close)="onCloseAlert()"></mm-alert>
  </div>
  <div class="dashboard__header">
    <h2>Discount Code Dashboard</h2>
    <button
      *ngIf="canCreate()"
      class="cancel-button button"
      mm-button
      color="primary"
      size="large"
      type="button"
      (click)="createDiscountCode()"
      test-target="create-discount-code"
    >
      Create Discount Code
    </button>
  </div>
  <nav mat-tab-nav-bar test-target="discount-tabs" mat-stretch-tabs="false">
    <a
      *ngFor="let tab of TABS"
      [routerLink]="[tab.url]"
      [active]="isActive(tab.url)"
      mat-tab-link
      [attr.test-target]="'discount-tab-' + tab.url"
      >{{ tab.title }}</a
    >
  </nav>
  <router-outlet></router-outlet>
</div>
