import { DropdownSearchDate, DropdownSearchDropdown, DropdownSearchSingle } from '~shared/modules/search-bar/models';
import { DropdownInput } from '~shared/model/dropdown-input.model';
import { DiscountReasonType } from '@discount-app/models';
import { DiscountStatus } from '@discount-app/models/discount-status.enum';
import { MIN_CHARACTER_COUNT_FOR_FILTER } from '@discount-app/constants';
import {
  BACK_URL_QUERY_PARAM,
  URL_QUERY_PARAMS_BACK_BUTTON_REDIRECT
} from '~shared/modules/back-button/components/constants/definitions';

const reasons = [
  new DropdownInput(DiscountReasonType.Compensation, 'Compensation'),
  new DropdownInput(DiscountReasonType.Promotion, 'Promotion'),
];

const statuses = [
  new DropdownInput(DiscountStatus.Active, 'Active'),
  new DropdownInput(DiscountStatus.Deactivated, 'Deactivated'),
  new DropdownInput(DiscountStatus.Expired, 'Expired'),
  new DropdownInput(DiscountStatus.Pending, 'Pending'),
];

export const DISCOUNT_CODE_SEARCH_OPTIONS = [
  new DropdownSearchSingle('code', 'Discount Code', MIN_CHARACTER_COUNT_FOR_FILTER),
  new DropdownSearchSingle('createdBy', 'Created By', MIN_CHARACTER_COUNT_FOR_FILTER),
  new DropdownSearchSingle('prefix', 'Prefix', MIN_CHARACTER_COUNT_FOR_FILTER),
  new DropdownSearchDate('startTime', 'Start Time', 'fromStartTime', 'toStartTime'),
  new DropdownSearchDate('endTime', 'End Time', 'fromEndTime', 'toEndTime'),
  new DropdownSearchDropdown('countries', 'Sales Channel', [], true),
  new DropdownSearchDropdown('purpose', 'Reason', reasons),
  new DropdownSearchDropdown('status', 'Status', statuses),
];

export const DISCOUNT_CODE_FILTER_TITLES = {
  createdBy: 'Created By',
  code: 'Discount Code',
  fromStartTime: 'Start Time From',
  toStartTime: 'Start Time Till',
  fromEndTime: 'End Time From',
  toEndTime: 'End Time Till',
  countries: 'Sales Channel',
  purpose: 'Reason',
  status: 'Status',
  prefix: 'Prefix',
};

export const DISCOUNT_BACK_URL_QUERY_PARAMS = {[BACK_URL_QUERY_PARAM]: URL_QUERY_PARAMS_BACK_BUTTON_REDIRECT.discountOverview};
