@import 'node_modules/@metromarkets/components-17/src/theme';

.extend-modal {
  .header {
    padding: 20px 15px;
    box-shadow: 0 1px 3px 0 rgba(0, 20, 50, 0.2);
  }

  .body {
    background-color: map-get($baseColors, grey-tint-95);
    padding: 16px 32px;
  }

  .modal-body {
    background: map-get($baseColors, white);
    box-shadow: 0px 1px 3px rgba(0, 20, 50, 0.2);
    border-radius: 2px;
    padding: 16px;
    overflow-y: scroll;

    .item {
      color: map-get($baseColors, primary);
      margin-bottom: 10px;

      label {
        font-weight: 800;
        font-size: 14px;
        color: map-get($baseColors, blue-shade-60);
      }

      span {
        font-weight: 400;
        font-size: 14px;
      }
    }

    .field-label {
      color: map-get($baseColors, blue-shade-60);
    }
  }

  .footer {
    margin-top: 18px;
  }

  .label {
    color: map-get($baseColors, grey-tint-60);
    font-size: 12px;
    margin-top: 8px;
  }

  .close {
    cursor: pointer;
  }

  .error {
    color: map-get($baseColors, red);
  }

  .date-group {
    width: 100%;
  }

  .time-field {
    height: 40px;
    color: #002d72;
    background-color: #ffffff;
    border-radius: 2px;
    border: 1px solid #ccd0d6;
    border-left-width: 0;
    font-size: 1.14rem;
    min-height: 40px;
    align-items: center;
    width: 50%;
    cursor: text;

    &:focus {
      outline: none;
    }
  }

  .info {
    color: map-get($baseColors, blue-tint-40);
    font-size: 13px;
    font-weight: bold;
  }

  .confirmation {
    background-color: map-get($baseColors, blue-tint-80);
    padding: 8px;
    color: map-get($baseColors, blue-shade-60);
    font-size: 14px;

    &__icon {
      height: 14px;
      width: 14px;
      margin-top: 5px;
      margin-left: 5px;
    }
  }

  .extend-message {
    padding-left: 10px;
  }

  .button-container {
    margin-top: 16px;
  }

  .error-message {
    color: map-get($baseColors, warning);
    font-size: 13px;
  }
}

input[type='time'] {
  font-family: inherit;
  color: #002d72;
  font-size: 1.14rem;
}

input[type='time']::-webkit-calendar-picker-indicator {
  display: none;
}

::ng-deep .cdk-overlay-pane {
  .time-option {
    background-color: #fff;
    padding: 8px 10px;
    color: #002d72;
    font-size: 1.14rem;

    &.mat-mdc-selected {
      color: #002d72 !important;
      font-weight: 900;
      background-color: #f2f7ff !important;
    }

    &:hover,
    &.mat-mdc-selected:hover {
      background-color: #f2f7ff;
    }
  }
}
