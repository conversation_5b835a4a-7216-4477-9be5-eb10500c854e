import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DiscountCode } from '@discount-app/models';
import { TimeOptions } from '@discount-app/constants';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import * as fromDiscountCodeExtend from '@discount-app/modules/dashboard/store/extend';
import { select, Store } from '@ngrx/store';
import { DiscountCodeCreateFormService } from '@discount-app/modules/discount-code/services/discount-code-form.service';
import { Country } from '@discount-app/models/countries.enum';
import { merge, Observable, Subject, takeUntil } from 'rxjs';
import { AuthService } from '@root/modules/user-auth/services/auth.service';
import { ErrorResponse } from '~shared/model';
import moment from 'moment-timezone';
import { Timezones } from '@discount-app/modules/discount-code/constants/country-timezones';
import { DiscountCodeValidators } from '@discount-app/modules/discount-code/validators';
import { DateUtil } from '@discount-app/utils/date.util';

@Component({
  selector: 'app-extend-validity-modal',
  templateUrl: './extend-validity-modal.component.html',
  styleUrls: ['./extend-validity-modal.component.scss'],
})
export class ExtendValidityModalComponent implements OnInit, OnDestroy {
  public discount: DiscountCode;
  public extendForm: UntypedFormGroup;
  public originalEndDateAndTime;
  public updatedEndDateAndTime;
  public showConfirm = false;
  public isSending$: Observable<boolean>;
  private unsubscribe$ = new Subject<void>();
  public error$: Observable<ErrorResponse>;
  private dateFields;
  public TimeOptions = TimeOptions;
  public codeOrPrefix = '';

  constructor(
    @Inject(MAT_DIALOG_DATA)
    data: {
      discount: DiscountCode;
      codeOrPrefix: string;
    },
    private fb: UntypedFormBuilder,
    private dialogRef: MatDialogRef<ExtendValidityModalComponent>,
    private store: Store<fromDiscountCodeExtend.ExtendState>,
    private discountCodeService: DiscountCodeCreateFormService,
    private authService: AuthService,
  ) {
    this.discount = data.discount;
    this.extendForm = this.fb.group(
      {
        endDate: ['', [Validators.required]],
        endTime: ['', [Validators.required]],
      },
      {
        validators: [
          DiscountCodeValidators.isNewEndTimeGraterThanOldEndDate(this.discount),
          DiscountCodeValidators.isNewEndTimeGraterThanNow(this.discount),
        ],
      },
    );
    this.codeOrPrefix = data.codeOrPrefix;
  }

  get endTime() {
    return DateUtil.convertUTCTimeStringToReadableString(this.discount.endTime, this.discount.country);
  }

  ngOnInit(): void {
    const country = this.discount.country.toLowerCase();
    moment.tz.setDefault(Timezones[country]);
    const endDateField = this.extendForm.get('endDate');
    const endTimeField = this.extendForm.get('endTime');
    this.isSending$ = this.store.pipe(select(fromDiscountCodeExtend.getIsSending));
    this.error$ = this.store.pipe(select(fromDiscountCodeExtend.getErrors));
    this.store.pipe(select(fromDiscountCodeExtend.getIsSuccess), takeUntil(this.unsubscribe$)).subscribe(success => {
      if (success) {
        this.dialogRef.close({
          success,
          discount: this.discount,
          endDate: endDateField.value,
          endTime: this.updatedEndDateAndTime.time,
        });
      }
    });
    this.dateFields = this.discountCodeService.parseCommonData(this.discount);
    const momentEndDate = DateUtil.getTimeWithTimezone(this.discount.endTime, this.discount.country);
    this.originalEndDateAndTime = {
      date: DateUtil.getFormattedTime(momentEndDate, 'DD.MM.YYYY'),
      time: DateUtil.getFormattedTime(momentEndDate, 'HH:mm'),
    };
    this.extendForm.patchValue({
      endDate: DateUtil.getDateForEdit(this.dateFields.endDate),
      endTime: this.dateFields.endTime,
    });
    merge(endDateField.valueChanges, endTimeField.valueChanges).subscribe(() => {
      const timeData = {
        hours: Number(endTimeField.value.split(':')[0]),
        minutes: Number(endTimeField.value.split(':')[1]),
      };
      const _endTime = moment.tz(new Date(endDateField.value).toDateString(), Timezones[country]);
      _endTime.set({ h: timeData.hours, m: timeData.minutes });
      this.updatedEndDateAndTime = {
        date: DateUtil.getFormattedTime(_endTime, 'DD.MM.YYYY'),
        time: DateUtil.getFormattedTime(_endTime, 'HH:mm'),
      };
      this.showConfirm = endDateField.value || endTimeField.value;
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  public closeDialogue() {
    this.dialogRef.close();
  }

  public extend() {
    this.store.dispatch(
      new fromDiscountCodeExtend.ExtendDiscountCode({
        codeGroup: this.discount.codeGroup,
        request: {
          endTime: this.discountCodeService.getDateString(
            this.extendForm.get('endDate').value,
            this.extendForm.get('endTime').value,
            this.discount.country as Country,
          ),
          updatedBy: this.authService.getUserUuid(),
          updatedByEmail: this.authService.userName(),
        },
      }),
    );
  }
}
