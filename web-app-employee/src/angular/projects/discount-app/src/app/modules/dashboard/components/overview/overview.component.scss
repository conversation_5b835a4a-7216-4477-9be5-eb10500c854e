@import 'node_modules/@metromarkets/components-17/src/theme/settings';

::ng-deep .mm-table .mm-cell {
  vertical-align: middle !important;
  text-align: center;
}

::ng-deep .mm-table .mm-header-cell {
  text-align: center !important;
}

.wrapper {
  max-height: calc(100vh - 215px);
  overflow: scroll;

  &.no-data {
    padding-bottom: 300px;
    overflow: visible;
  }

  .actions-menu {
    &__deactivate {
      color: map-get($baseColors, warning);
    }
  }
}

.info-icon {
  display: inline-block;
  margin-left: 5px;
  cursor: pointer;
  height: 24px;
  width: 24px;
  border-radius: 12px;
  background-color: #e6f0ff;
  padding-top: 2px;
}

.button-container {
  text-align: center;
  margin-top: 30px;
}
