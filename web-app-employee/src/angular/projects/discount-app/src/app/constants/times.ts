function getFormattedTime(time: number) {
  const DIGIT_COUNT = 2;
  return `0${time}`.slice(-1 * DIGIT_COUNT);
}
function getTimes() {
  const INTERVAL_MINS = 30;
  const MINS_IN_HOUR = 60;
  const HOURS_IN_DAY = 24;
  const hours = [];
  for (let i = 0; (i * INTERVAL_MINS) / MINS_IN_HOUR < HOURS_IN_DAY; i++) {
    const hour = Math.floor((i * INTERVAL_MINS) / MINS_IN_HOUR);
    const minutes = (i * INTERVAL_MINS) % MINS_IN_HOUR;
    const time = `${getFormattedTime(hour)}:${getFormattedTime(minutes)}`;
    hours.push(time);
  }
  return hours;
}
export const TimeOptions = getTimes();
