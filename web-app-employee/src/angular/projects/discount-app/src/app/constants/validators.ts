export const MAX_DISCOUNT_AMOUNT_FIXED = 3000;
export const MAX_DISCOUNT_AMOUNT_PERCENTAGE = 99;
export const MAX_LENGT_DISCOUNT_CODE = 20;
export const MIN_LENGT_DISCOUNT_CODE = 9;
export const MAX_LENGTH_PREFIX = 15;
export const MIN_LENGTH_PREFIX = 1;
export const MIN_LENGTH_SERIALIZED_RANDOM_CODE = 5; // randomly generated code after prefix
export const MIN_COUNT_SERIALIZED_DISCOUNT_CODE = 1;
export const MAX_COUNT_SERIALIZED_DISCOUNT_CODE = 100000;
export const DISCOUNT_CODE_PATTERN = '[A-Z0-9]*';
const SPECIFIC_CAMPAIGN_ID = 209;
const OTHERS_ID = 212;
export const DETAIL_REQUIRED_REASONS = [SPECIFIC_CAMPAIGN_ID, OTHERS_ID];
export const MIN_SPENDING_AMOUNT_FOR_PERCENTAGE = 0;
export const MAX_SPENDING_AMOUNT_FOR_PERCENTAGE = 2147483647;
export const MIN_CHARACTER_COUNT_FOR_FILTER = 3;
