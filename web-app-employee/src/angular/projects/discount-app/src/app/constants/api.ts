import { environment } from '~env/environment';

export const DISCOUNT_API = `${environment.svcDiscountBaseUrl}/api/v1`;
export const DISCOUNT_DOWNLOAD_SERIALIZED_API = `${environment.svcDiscountBaseUrl}/api/v1/download-serialized-code`;
export const ORDER_LINE_DOMAIN_EVENTS_API = `${environment.svcOrderManagementBaseUrl}/api/v1/order-management/back-office/orderlines/domain-events`;
export const DISCOUNT_CODE_EXTEND_VALIDITY_API = `${environment.svcDiscountBaseUrl}/api/v1/discount-code/extend`;
export const BUYER_CATEGORIES_API = `${environment.svcBuyerCategoryBaseUrl}/api/categories`;

export function getCategoryUrl(countryCode?: string): string {
  return countryCode
    ? `${environment.svcCategoryBaseUrl}/admin/v1/${countryCode.toUpperCase()}/categories`
    : `${environment.svcCategoryBaseUrl}/admin/v1/categories`;
}
