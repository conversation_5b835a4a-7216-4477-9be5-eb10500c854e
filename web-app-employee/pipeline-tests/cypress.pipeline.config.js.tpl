const { defineConfig } = require("cypress");

module.exports = defineConfig({
  env: {
    buyerUrl: "http://web-app-buyer.${ENV_ID}.infra.metro-markets.cloud/",
    employeeUrl: "http://web-app-employee.${ENV_ID}.infra.metro-markets.cloud/",
    sellerUrl: "http://web-app-seller.${ENV_ID}.infra.metro-markets.cloud/",
    apiUrl:
      "https://app-buyer-account.${ENV_ID}.infra.metro-marketplace.cloud/api/v1/account-info",
    apiCart:
      "https://app-checkout.${ENV_ID}.infra.metro-markets.cloud/api/v1/cart",
    idamUrl: "https://idam-pp.metro.de/authorize/api/oauth2/access_token",
    imsLoginUrl:
      "https://service-ims-v2.${ENV_ID}.infra.metro-markets.cloud/employee/auth/login",
    pimBaseUrl: "https://app-seller-pim.${ENV_ID}.infra.metro-markets.cloud",
    categoryBaseUrl:
      "https://service-category.${ENV_ID}.infra.metro-markets.cloud",
    inventoryBaseUrl:
      "https://app-seller-inventory.${ENV_ID}.infra.metro-markets.cloud",
    disputesBaseUrl:
      "https://service-dispute-management.${ENV_ID}.infra.metro-markets.cloud",
    discountBaseUrl:
      "https://service-discount.${ENV_ID}.infra.metro-markets.cloud",
    clientSecretIdamTokenBuyer: "${CLIENT_SECRET_IDAM_TOKEN_BUYER}",
    clientSecretIdamTokenEmployee: "${CLIENT_SECRET_IDAM_TOKEN_EMPLOYEE}",
    domain: "${ENV_ID}.infra.metro-markets.cloud",
    users: {
      employeeLogin: {
        username: "${EMPLOYEE_USER_NAME}",
        password: "${EMPLOYEE_PASSWORD}",
      },
      buyerLogin: {
        username: "${BUYER_USER_NAME}",
        password: "${BUYER_PASSWORD}",
      },
      sellerLogin: {
        username: "${SELLER_USER_NAME}",
        password: "${SELLER_PASSWORD}",
      },
      sellerLoginPimUpload: {
        username: "${SELLER_PIM_UPLOAD_USER_NAME}",
        password: "${SELLER_PIM_UPLOAD_PASSWORD}",
      },
    },
  },

  viewportHeight: 1080,
  viewportWidth: 1920,
  scrollBehavior: false,
  video: false,
  chromeWebSecurity: false,
  defaultCommandTimeout: 30000,
  downloadsFolder: "./cypress/downloads",

  retries: {
    runMode: 2,
  },

  e2e: {
    setupNodeEvents(on, config) {
      return require('./cypress/plugins/index.ts')(on, config)
    },
  },
});
