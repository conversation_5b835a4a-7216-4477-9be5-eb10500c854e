const { defineConfig } = require('cypress')

module.exports = defineConfig({
  env: {
    imsLoginUrl:
      'https://service-ims-v2.pp-de.metro-marketplace.cloud/employee/auth/login',
    pimBaseUrl: 'https://app-seller-pim.pp-de.metro-marketplace.cloud',
    categoryBaseUrl: 'https://service-category.pp-de.metro-marketplace.cloud',
    inventoryBaseUrl:
      'https://app-seller-inventory.pp-de.metro-marketplace.cloud',
    disputesBaseUrl:
      'https://service-dispute-management.pp-de.metro-marketplace.cloud',
    discountBaseUrl:
      'https://service-discount.pp-de.metro-marketplace.cloud',
    idamUrl: '',
    sellerUrl: '',
    buyerUrl: '',
    employeeUrl: '',
    clientSecretIdamTokenBuyer: '',
    clientSecretIdamTokenEmployee: '',
    users: {
      employeeLogin: {
        username: '',
        password: '',
      },
      buyerLogin: {
        username: '',
        password: '',
      },
      sellerLogin: {
        username: '',
        password: '',
      },
    },
  },
  domain: '',
  viewportHeight: 1080,
  viewportWidth: 1920,
  scrollBehavior: false,
  video: false,
  chromeWebSecurity: false,
  defaultCommandTimeout: 30000,
  downloadsFolder: './cypress/downloads',
  e2e: {
    // We've imported your old cypress plugins here.
    // You may want to clean this up later by importing these.
    setupNodeEvents(on, config) {
      return require('./cypress/plugins/index.ts')(on, config)
    },
  },
})
