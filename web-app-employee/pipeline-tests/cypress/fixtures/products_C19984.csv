PRODUCT CATEGORY;PRODUCT NAME;MA<PERSON>FACTURER;MPN;BRAND;SHORT DESCRIPTION;DESCRIPTION;MSRP;KEY FEATURE;KEY FEATURE;KEY FEATURE;KEY FEATURE;KEY FEATURE;IMAGE;IMAGE;COLOUR
CATEGORYPLACEHOLDER;TestDataEE_ClassNew;Philips;MPNC19984;Philips;In short the Docker containers contain everything you need to run an application including the source code you wrote;Docker creates stand-alone packages called containers that contain everything that is needed for you to run your application. Each container gets its own CPU, memory and network resources and does not depend on a specific operating system or kernel. The first that comes to mind when I describe the above is a Virtual Machine, but <PERSON><PERSON> differs in how it shares or dedicates resources. <PERSON><PERSON> uses a so-called layered file system which enables the containers to share common parts and the end result is that containers are way less of resource-hog on the host system than a virtual machine.;116;Key feature 1;Key feature 2;Key feature 3;Key feature 4;Key feature 5;https://storage.googleapis.com/fe-cdn/app-seller-pim-images-for-tests/1fd89f24-56cb-11e9-8647-d663bd873d93_orig.jpg;https://storage.googleapis.com/fe-cdn/app-seller-pim-images-for-tests/1fd809b0-56cb-11e9-8647-d663bd873d93_orig.jpg;red
