import {loginApi} from "../../support/api/idam-login-api";
import {landingPage} from "../../support/pages/employee/landing-page";
import {contentProviderPage} from "../../support/pages/employee/content-provider-page";
import {ContentProviderFilter} from "../../support/constants/pim-enums";
import {generateRandomChars} from "../../support/helpers";

let contentProvider: any = null;

function filterContentProviderListProcess(filterKey: string, value: string) {
  contentProviderPage.interceptContentProviderListFilterBy( filterKey, value);

  contentProviderPage.filterBy(filterKey, value);

  return cy.wait(`@getCPListFilteredBy${filterKey}`).then(interception => {
    return interception.response.body;
  });
}

function invalidEditContentProviderRankProcess(id: string, value: number) {
  contentProviderPage.interceptContentProviderDetails(id);

  contentProviderPage.goToContentProviderDetails(id);
  return cy.wait('@getContentProvider').then(() => {
    contentProviderPage.editContentProviderRank(value);
  });
}

describe("Content Provider Ranking Phase 1", () => {

  before(() => {
    loginApi.employeeLogin("employeeLogin");
    cy.fixture("../fixtures/content_provider_data.json").then((data) => {
      contentProvider = data.contentProvider;
    });
  });

  beforeEach(() => {
    cy.injectToken();
    landingPage.goToLandingEnPage();
    contentProviderPage.interceptContentProviderList();
    landingPage.goToContentProviderRankingPage();
  });

  it("Get Content provider List", () => {
    cy.wait('@getContentProviderList').then(interception => {
      contentProviderPage.checkContentProviderListStructure(interception.response.body);
    });
  });

  it("Apply Filter By Organization Name for CP list", () => {
    const organizationNameFilter = ContentProviderFilter.ORGANIZATION_NAME;
    const organizationName = contentProvider.organizationName;

    filterContentProviderListProcess(organizationNameFilter, organizationName).then((data) => {
      contentProviderPage.checkContentProviderFilteredList(
        organizationNameFilter,
        organizationName,
        data
      );
    });
  });

  it("Apply Filter By Shop Name for CP list", () => {
    const shopNameFilter = ContentProviderFilter.SHOP_NAME;
    const shopName = contentProvider.shopName;

    filterContentProviderListProcess(shopNameFilter, shopName).then((data) => {
      contentProviderPage.checkContentProviderFilteredList(
        shopNameFilter,
        shopName,
        data
      );
    });
  });

  it.skip("Apply Filter By Market for CP list", () => {
    const market = contentProvider.market;

    contentProviderPage.interceptContentProviderListFilterBy(ContentProviderFilter.MARKET, market);

    contentProviderPage.filterByMarket(market);

    cy.wait(2000);
    return cy.wait(`@getCPListFilteredBy${ContentProviderFilter.MARKET}`).then(interception => {
      let data =  interception.response.body;
      contentProviderPage.checkContentProviderListFilteredByMarket(
        market,
        data
      );
    });
  });

  it("Change content provider rank", () => {
    const newRankValue = 999;

    contentProviderPage.interceptContentProviderDetails(contentProvider.id);

    contentProviderPage.goToContentProviderDetails(contentProvider.id);
    cy.wait('@getContentProvider').then(() => {
      contentProviderPage.interceptContentProviderRank(contentProvider.id);
      contentProviderPage.editContentProviderRank(newRankValue);

      cy.wait('@patchContentProviderRank').then(() => {
        contentProviderPage.checkContentProviderRank(newRankValue);
      });
    });
  });

  it("Change content provider rank with value < 99", () => {
    const invalidMinRankValue = 90;

    invalidEditContentProviderRankProcess(contentProvider.id, invalidMinRankValue).then(() => {
      contentProviderPage.checkContentProviderRankMINErrorMessage();
    });
  });

  it("Change content provider rank with value > 900000", () => {
    const invalidMaxRankValue = 999999;

    invalidEditContentProviderRankProcess(contentProvider.id, invalidMaxRankValue).then(() => {
      contentProviderPage.checkContentProviderRankMAXErrorMessage();
    });
  });

  it("update content provider note", () => {
    const note = `this is cypress note (${generateRandomChars()})`;

    contentProviderPage.interceptContentProviderDetails(contentProvider.id);

    contentProviderPage.goToContentProviderDetails(contentProvider.id);
    cy.wait('@getContentProvider').then(() => {
      contentProviderPage.interceptContentProviderNote(contentProvider.id);
      contentProviderPage.editContentProviderNote(note);

      cy.wait('@patchContentProviderNote').then(() => {
        cy.wait(2000);
        contentProviderPage.checkContentProviderNote(note);
      });
    });
  });

  it("Get Content Provider details and his Historical ranks", () => {
    contentProviderPage.interceptContentProviderDetails(contentProvider.id);
    contentProviderPage.interceptContentProviderHistoryRank(contentProvider.id);

    contentProviderPage.goToContentProviderDetails(contentProvider.id);

    cy.wait('@getContentProvider').then(interception => {
      contentProviderPage.checkContentProviderDetails(contentProvider, interception.response.body);
    });

    cy.wait('@getContentProviderHistoryRank').then(interception => {
      contentProviderPage.checkContentProviderHistoryRankStructure(interception.response.body);
    });
  });

  it("Download the current CP list with there current ranking", () => {
    cy.wait('@getContentProviderList').then(() => {
      contentProviderPage.interceptDownloadContentProviderList();
      contentProviderPage.downloadContentProviderList();

      cy.wait('@downloadContentProviderList').then(data => {
        contentProviderPage.checkDownloadedList(data.response);
      });
    });
  });
});
