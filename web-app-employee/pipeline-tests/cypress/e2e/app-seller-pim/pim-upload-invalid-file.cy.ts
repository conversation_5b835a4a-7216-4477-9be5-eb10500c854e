import { loginApi } from "../../support/api/idam-login-api";
import { productDataUploadsPage } from "../../support/pages/employee/product-data-uploads-page";
import { appSellerPimApi } from "../../support/api/app-seller-pim-api";
import {UploadStatus, UploadStatusFilter} from "../../support/constants/pim-enums";
import {
  CATEGORY_NOT_FOUND,
  DECISION_EMPTY,
  GTIN_INVALID_CHECKSUM,
  IMAGE_IS_MANDATORY,
  PRODUCT_NAME_INVALID_LENGTH
} from "../../support/constants/pim-report-errors";
import {
  generateRandomChars,
  generateGtin,
} from "../../support/helpers";
import {landingPage} from "../../support/pages/employee/landing-page";
import { WAITING_TIME_TO_PROCESS_UPLOAD } from '../../support/constants/pim-constants';

let orgId = null;
let sellerId = null;

function uploadInvalidFileProcess(fileUrl) {
  let filename :string = 'updated_'+ fileUrl.substring(fileUrl.lastIndexOf('/') + 1) ;
  let newFileUrl :string = `../fixtures/${filename}`;

   cy.fixture(fileUrl).then((productData) => {
    let csvContent = productData.replace('GTINPLACEHOLDER', generateGtin());
    csvContent = csvContent.replace(
      'PRODUCTNAMEPLACEHOLDER',
      `TEST PRODUCT (WARNING) DON'T TOUCH IT. ${generateRandomChars()}`
    );

    cy.writeFile(`cypress/fixtures/${filename}`, csvContent, 'utf8');
  });


  return cy.uploadFile(newFileUrl, orgId, UploadStatusFilter.AUTO).then((uploadedFileId)=>{
    cy.wait(WAITING_TIME_TO_PROCESS_UPLOAD);
    cy.getUpload(uploadedFileId, UploadStatusFilter.REVIEWED).then((uploadedFile) => {
      productDataUploadsPage.checkProcessingFileStatus(uploadedFile, UploadStatus.WITH_ERRORS);
      productDataUploadsPage.checkAutoReviewUpload(uploadedFile);

      cy.downloadReport(uploadedFileId).then((errorReport) => {
          return errorReport;
        });
      });
  });
}

describe("app-seller-pim: Upload Invalid File", () => {

  before(() => {
    loginApi.employeeLogin("employeeLogin");
    loginApi.sellerLogin("sellerLoginPimUpload");
    cy.fixture("../fixtures/pim_data.json").then((data) => {
      sellerId = data.organization.sellerId;
      orgId = data.organization.orgId;
    });
  });

  beforeEach(() => {
    cy.injectToken();
    landingPage.goToLandingPage();
  });

  it("Upload a product file with missing mandatory column", () => {
    const fileUrl = '../fixtures/product_file_without_image.csv';

    uploadInvalidFileProcess(fileUrl).then((errorReport) => {
      productDataUploadsPage.checkErrorReport(errorReport, IMAGE_IS_MANDATORY);
    });
  });

  it("Upload a product file without GTIN + MPN&Manufacturer", () => {
    const fileUrl = '../fixtures/product_file_without_gtin_mpn_manufacturer.csv';

    uploadInvalidFileProcess(fileUrl).then((errorReport) => {
      productDataUploadsPage.checkErrorReport(errorReport, DECISION_EMPTY);
    });
  });

  it("Upload a product file with product name > 150 symbols", () => {
    const fileUrl = '../fixtures/product_file_with_invalid_max_name.csv';

    uploadInvalidFileProcess(fileUrl).then((errorReport) => {
      productDataUploadsPage.checkErrorReport(errorReport, PRODUCT_NAME_INVALID_LENGTH);
    });
  });

  it("Upload a product file with GTIN with wrong Checksum", () => {
    const fileUrl = '../fixtures/product_file_with_gtin_invalid_checksum.csv';

    uploadInvalidFileProcess(fileUrl).then((errorReport) => {
      productDataUploadsPage.checkErrorReport(errorReport, GTIN_INVALID_CHECKSUM);
    });
  });

  it("Upload a product file with inActive category", () => {
    const fileUrl = '../fixtures/product_file_with_category_not_found.csv';

    uploadInvalidFileProcess(fileUrl).then((errorReport) => {
      productDataUploadsPage.checkErrorReport(errorReport, CATEGORY_NOT_FOUND);
    });
  });


  after(() => {
    appSellerPimApi.setAutoReview(orgId, false);
  });
});
