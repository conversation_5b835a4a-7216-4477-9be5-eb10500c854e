import { appSellerPim<PERSON><PERSON> } from "../../support/api/app-seller-pim-api";
import { loginApi } from "../../support/api/idam-login-api";
import { productDataUploadsPage } from "../../support/pages/employee/product-data-uploads-page";
import {
  UploadStatus,
  UploadStatusFilter,
} from "../../support/constants/pim-enums";
import { landingPage } from "../../support/pages/employee/landing-page";
import { WAITING_TIME_TO_PROCESS_UPLOAD } from "../../support/constants/pim-constants";
import { getRandomString } from "../../support/helpers";
import { serviceCategoryApi } from "../../support/api/service-category-api";

let orgId = null;
let sellerId = null;
let categoryId = null;

function uploadValidFileProcess(fileUrl) {
  return serviceCategoryApi.category.getItemTypeId().then((categoryId) => {
    const filename =
      `updated_${getRandomString(5)}_` +
      fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
    const newFileUrl = `../fixtures/${filename}`;

    return cy.fixture(fileUrl).then((fileContent) => {
      const updatedContent = fileContent.replace(
        /CATEGORYPLACEHOLDER/g,
        categoryId
      );
      cy.writeFile(`cypress/fixtures/${filename}`, updatedContent, "utf8");

      return cy
        .uploadFile(newFileUrl, orgId, UploadStatusFilter.AUTO)
        .then((uploadedFileId) => {
          cy.wait(WAITING_TIME_TO_PROCESS_UPLOAD);
          return cy
            .getUpload(uploadedFileId, UploadStatusFilter.REVIEWED)
            .then((uploadedFile) => {
              productDataUploadsPage.checkProcessingFileStatus(
                uploadedFile,
                UploadStatus.SUCCESS
              );
              productDataUploadsPage.checkAutoReviewUpload(uploadedFile);

              return cy
                .getProductFromFile(newFileUrl)
                .then((uploadedProduct) => {
                  return cy
                    .downloadReport(uploadedFileId)
                    .then((fileReport) => {
                      return cy
                        .searchProductInDB(fileReport.MID)
                        .then((productInDB) => {
                          return { productInDB, uploadedProduct };
                        });
                    });
                });
            });
        });
    });
  });
}

describe("app-seller-pim: Upload Valid File", () => {
  before(() => {
    loginApi.employeeLogin("employeeLogin");
    loginApi.sellerLogin("sellerLoginPimUpload");
    cy.fixture("../fixtures/pim_data.json").then((data) => {
      orgId = data.organization.orgId;
      sellerId = data.organization.sellerId;
    });
  });

  beforeEach(() => {
    cy.injectToken();
    landingPage.goToLandingPage();
  });

  it("Create a new product with valid 8-digits GTIN and check MID generation C89740", () => {
    const fileUrl = "../fixtures/products_C89740.csv";
    uploadValidFileProcess(fileUrl).then(({ productInDB, uploadedProduct }) => {
      productDataUploadsPage.checkProductGtin(productInDB, uploadedProduct);
    });
  });

  it("Upload a product with name value containing 150 symbols C90425", () => {
    const fileUrl = "../fixtures/products_C90425.csv";

    uploadValidFileProcess(fileUrl).then(({ productInDB, uploadedProduct }) => {
      productDataUploadsPage.checkProductName(productInDB, uploadedProduct);
    });
  });

  it("Upload a product without gtin and with mpn manufacturer C14747", () => {
    const fileUrl = "../fixtures/products_C14747.csv";

    uploadValidFileProcess(fileUrl).then(({ productInDB, uploadedProduct }) => {
      productDataUploadsPage.checkProductEmptyGtin(
        productInDB,
        uploadedProduct
      );
    });
  });

  it("Upload a product with GTIN and MPN manufacturer C19387", () => {
    const fileUrl = "../fixtures/products_C19387.csv";

    uploadValidFileProcess(fileUrl).then(({ productInDB, uploadedProduct }) => {
      productDataUploadsPage.checkProductGtin(productInDB, uploadedProduct);
      productDataUploadsPage.checkProductManufacturer(
        productInDB,
        uploadedProduct
      );
    });
  });

  it("Upload a product by CSV with headers in lower case C19983", () => {
    const fileUrl = "../fixtures/products_C19983.csv";

    uploadValidFileProcess(fileUrl).then(({ productInDB, uploadedProduct }) => {
      cy.log(productInDB, uploadedProduct);
    });
  });

  it("Create a product by CSV with headers in upper case C19984", () => {
    const fileUrl = "../fixtures/products_C19984.csv";

    uploadValidFileProcess(fileUrl).then(({ productInDB, uploadedProduct }) => {
      cy.log(productInDB, uploadedProduct);
    });
  });

  after(() => {
    appSellerPimApi.setAutoReview(orgId, false);
  });
});
