import { TestPrefixesEnum } from '../../support/constants/shared';
import { loginApi } from '../../support/api/idam-login-api';
import { SELLER_INVOICES } from '../../support/constants/endpoints';
import { sellerInvoicesPage } from '../../support/pages/employee/seller-invoices-page';

describe(`${TestPrefixesEnum.FinOps}: Seller Invoices List`, () => {
  before(() => {
    loginApi.employeeLogin('employeeLogin');
  });

  beforeEach(() => cy.injectToken());

  it('Test if seller invoice iframe is loaded', () => {
    cy.visit(`${Cypress.env('employeeUrl')}/${SELLER_INVOICES}`);
    sellerInvoicesPage.checkIfIframeIsLoaded();
  });
});
