import { generateUploadFile, getFileName } from "../../support/helpers";
import { loginApi } from "../../support/api/idam-login-api";
import { UploadStatusFilter } from "../../support/constants/pim-enums";
import { landingPage } from "../../support/pages/employee/landing-page";
import { productDataUploadsPage } from "../../support/pages/employee/product-data-uploads-page";
import { rowReviewPage } from "../../support/pages/employee/row-review-page";
import { stats_values } from "../../support/constants/pim-stats-values";
import { serviceCategoryApi } from "../../support/api/service-category-api";

let orgId = null;
let uploadedFileId = null;
let uploadName = null;

describe("Product file upload review", () => {
  before(() => {
    loginApi.employeeLogin("employeeLogin");
    loginApi.sellerLogin("sellerLogin");

    cy.fixture("../fixtures/row_review_data.json").then((data) => {
      orgId = data.organization.orgId;
    });

    cy.injectToken();
    cy.then(() => {
      const filePath = "cypress/fixtures/product_file_with_de_products.csv";

      return serviceCategoryApi.category.getItemTypeId().then((categoryId) => {
        return cy.readFile(filePath).then((fileContent) => {
          if (!fileContent.includes(categoryId)) {
            const updatedContent = fileContent.replace(
              /CATEGORYPLACEHOLDER/g,
              categoryId
            );
            return cy.writeFile(filePath, updatedContent, "utf8");
          }
        });
      });
    });
  });

  beforeEach(() => {
    cy.injectToken();
    landingPage.goToLandingEnPage();
  });

  it("Upload Stats: Upload and approve file with 10 new products (random GTINs)", () => {
    const fileUrl = "../fixtures/product_file_with_de_products.csv";
    const uploadStatusFilter = UploadStatusFilter.IN_REVIEW;
    const generatedFilePath = generateUploadFile(fileUrl);
    uploadName = getFileName(generatedFilePath);

    cy.uploadFile(generatedFilePath, orgId, uploadStatusFilter).then(
      (uploadId) => {
        uploadedFileId = uploadId;

        landingPage.goToProductUploadsV2();
        productDataUploadsPage.initNeedsReviewGrid(uploadName);
        productDataUploadsPage.filterAndParseFileProcess(uploadedFileId);
      }
    );
  });

  it("Upload Stats: Should validate the stats in the 'Needs Review' tab", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initNeedsReviewGrid(uploadName);
    productDataUploadsPage.openStatsSidebar(uploadedFileId);
    productDataUploadsPage.checkStatsInSidebar(stats_values.in_review);
  });

  it("Upload Stats: Approve one product and reject the rest", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initNeedsReviewGrid(uploadName);

    cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.IN_REVIEW).then(
      (uploadedFile) => {
        productDataUploadsPage.goToRowReviewPage(uploadedFile.fileName);

        rowReviewPage.approveRow();
        rowReviewPage.rejectAllRows();
      }
    );
  });

  it("Upload Stats: Validate Stats in 'Reviewed' Tab after file rejection", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initReviewedGrid(uploadName);
    productDataUploadsPage.openStatsSidebar(uploadedFileId);
    productDataUploadsPage.checkStatsInSidebar(stats_values.reviewed);
  });
});
