import { productDataUploadsPage } from "../../support/pages/employee/product-data-uploads-page";
import { rowReviewPage } from "../../support/pages/employee/row-review-page";
import { loginApi } from "../../support/api/idam-login-api";
import {
  checkStatusCode,
  generateGtin,
  generateRandomChars,
  generateUploadFile,
  getFileName,
  getRandomString,
} from "../../support/helpers";
import {
  FileStatus,
  FilterOptions,
  FilterValues,
  ProcessingStatus,
  RowStatus,
  UploadStatusFilter,
} from "../../support/constants/pim-enums";
import { dateRangesValues } from "../../support/constants/pim-upload-report-constants";
import {
  PRODUCT_UPLOAD_ENDPOINT_TIMEOUT,
  rejectRowDraftReasonMessage,
  rejectRowReasonMessages,
} from "../../support/constants/pim-upload-constants";
import { landingPage } from "../../support/pages/employee/landing-page";
import {
  germanMarketCode,
  nonAssignedLabel,
  spanishMarketCode,
} from "../../support/constants/pim-constants";

let orgId = null;
let uploadedFileId = null;
let uploadName = null;
let userName = null;

describe("Product file upload review", () => {
  before(() => {
    const credentials = Cypress.env("users");
    const employeeLogin = "employeeLogin";
    userName = credentials[employeeLogin].username;

    loginApi.employeeLogin(employeeLogin);
    loginApi.sellerLogin("sellerLogin");

    cy.fixture("../fixtures/row_review_data.json").then((data) => {
      orgId = data.organization.orgId;
    });
  });

  beforeEach(() => {
    cy.injectToken();
    landingPage.goToLandingEnPage();
  });

  function generateUploadWithSameGTIN(fileUrl): string {
    let filename: string =
      `updated_${getRandomString(5)}_` +
      fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
    let newFileUrl: string = `../fixtures/${filename}`;

    cy.fixture(fileUrl).then((productData) => {
      let count = productData.split("GTINPLACEHOLDER").length - 1;
      let csvContent = productData;
      let duplicatedGTIN = generateGtin();

      while (count > 0) {
        csvContent = csvContent.replace("GTINPLACEHOLDER", duplicatedGTIN);
        csvContent = csvContent.replace(
          "PRODUCTNAMEPLACEHOLDER",
          `TEST PRODUCT (WARNING) DON'T TOUCH IT. ${generateRandomChars()}`
        );
        count--;
      }

      cy.writeFile(`cypress/fixtures/${filename}`, csvContent, "utf8");
    });

    return newFileUrl;
  }

  function autoRejectRowProcess(
    generatedFilePath,
    orgId,
    uploadStatusFilter
  ): void {
    const fileName = getFileName(generatedFilePath);
    cy.uploadFile(generatedFilePath, orgId, uploadStatusFilter).then(
      (uploadId) => {
        uploadedFileId = uploadId;
        landingPage.goToProductUploadsV2();

        productDataUploadsPage.initReviewedGrid(fileName);
        cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.REVIEWED).then(
          (uploadedFile) => {
            productDataUploadsPage.checkProcessingStatus(
              uploadedFile.fileName,
              ProcessingStatus.WITH_ERRORS
            );
          }
        );
      }
    );
  }

  it("Product Uploads: Download and Validate reports", () => {
    landingPage.goToProductUploadsV2();

    dateRangesValues.forEach(({ label }) => {
      productDataUploadsPage.interceptReport();
      productDataUploadsPage.selectReportRange(label);
      productDataUploadsPage.checkReport();
    });
  });

  it("Product Uploads Grid: File is parsed automatically", () => {
    const fileUrl = "../fixtures/products_row_review.csv";
    const uploadStatusFilter = UploadStatusFilter.REVIEWED;
    const generatedFilePath = generateUploadFile(fileUrl);
    uploadName = getFileName(generatedFilePath);

    cy.uploadFile(generatedFilePath, orgId, uploadStatusFilter).then(
      (uploadId) => {
        uploadedFileId = uploadId;
        landingPage.goToProductUploadsV2();
        productDataUploadsPage.initNeedsReviewGrid(uploadName);
        productDataUploadsPage.startParseCheck(uploadedFileId);
      }
    );
  });

  it("Row Review: Approve one product", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initNeedsReviewGrid(uploadName);

    cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.IN_REVIEW).then(
      (uploadedFile) => {
        productDataUploadsPage.goToRowReviewPage(uploadedFile.fileName);
        cy.wait("@getUploadReviewRows").then((interception) => {
          rowReviewPage.checkRowsStructure(interception.response);
          rowReviewPage.approveRow();
        });
      }
    );
  });

  it("Row Review: Reject one product", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initNeedsReviewGrid(uploadName);

    cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.IN_REVIEW).then(
      (uploadedFile) => {
        productDataUploadsPage.goToRowReviewPage(uploadedFile.fileName);
        cy.wait("@getUploadReviewRows").then((interception) => {
          rowReviewPage.checkRowsStructure(interception.response);
          rowReviewPage.rejectRow();
        });
      }
    );
  });

  it("Product Uploads Grid: Approve upload file", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initNeedsReviewGrid(uploadName);

    cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.IN_REVIEW).then(
      (uploadedFile) => {
        productDataUploadsPage.approveUploadFile(uploadedFile.fileName);

        cy.wait("@approveFile").then((interception) => {
          checkStatusCode(interception.response);
          productDataUploadsPage.initReviewedGrid(uploadName);

          cy.getFilteredUpload(
            uploadedFileId,
            UploadStatusFilter.REVIEWED
          ).then((approvedFile) => {
            productDataUploadsPage.checkApprovalFileStatus(
              approvedFile,
              FileStatus.APPROVED
            );
          });
        });
      }
    );
  });

  it("Product Uploads Grid: Reject upload file with custom error message", () => {
    const fileUrl = "../fixtures/product-file-rejection.csv";
    const uploadStatusFilter = UploadStatusFilter.IN_REVIEW;
    const generatedFilePath = generateUploadFile(fileUrl);
    const fileName = getFileName(generatedFilePath);

    cy.uploadFile(generatedFilePath, orgId, uploadStatusFilter).then(
      (uploadId) => {
        const uploadedFileId = uploadId;
        landingPage.goToProductUploadsV2();
        productDataUploadsPage.initNeedsReviewGrid(fileName);

        cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.IN_REVIEW).then(
          () => {
            productDataUploadsPage.rejectUploadFile(fileName);
            cy.wait(3000);
            productDataUploadsPage.initReviewedGrid(fileName);
            cy.getFilteredUpload(
              uploadedFileId,
              UploadStatusFilter.REVIEWED
            ).then((rejectedFile) => {
              productDataUploadsPage.checkApprovalFileStatus(
                rejectedFile,
                FileStatus.REJECTED
              );
            });
          }
        );
      }
    );
  });

  it("Product Uploads Grid: Reject uploaded file with dialog error message", () => {
    const fileUrl = "../fixtures/product-file-rejection-with-error-message.csv";
    const uploadStatusFilter = UploadStatusFilter.IN_REVIEW;
    const generatedFilePath = generateUploadFile(fileUrl);
    const fileName = getFileName(generatedFilePath);

    cy.uploadFile(generatedFilePath, orgId, uploadStatusFilter).then(
      (uploadId) => {
        const uploadedFileId = uploadId;
        landingPage.goToProductUploadsV2();
        productDataUploadsPage.initNeedsReviewGrid(fileName);

        cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.IN_REVIEW).then(
          () => {
            productDataUploadsPage.rejectUploadByDialog(fileName);
            cy.wait(3000);

            productDataUploadsPage.initReviewedGrid(fileName);
            cy.getFilteredUpload(
              uploadedFileId,
              UploadStatusFilter.REVIEWED
            ).then((rejectedFile) => {
              productDataUploadsPage.checkApprovalFileStatus(
                rejectedFile,
                FileStatus.REJECTED
              );
            });
          }
        );
      }
    );
  });

  it("Row Review: Bulk approve for selected Market", () => {
    const fileUrl = "../fixtures/bulk_approve_products.csv";
    const uploadStatusFilter = UploadStatusFilter.IN_REVIEW;
    const generatedFilePath = generateUploadFile(fileUrl);
    const fileName = getFileName(generatedFilePath);

    cy.uploadFile(generatedFilePath, orgId, uploadStatusFilter).then(
      (uploadId) => {
        uploadedFileId = uploadId;
        landingPage.goToProductUploadsV2();
        productDataUploadsPage.initNeedsReviewGrid(fileName);
        productDataUploadsPage.startParseCheck(uploadedFileId);

        cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.IN_REVIEW).then(
          (uploadedFile) => {
            productDataUploadsPage.interceptProductUploads();
            cy.wait("@getProductUploads", {
              timeout: PRODUCT_UPLOAD_ENDPOINT_TIMEOUT,
            }).then(() => {
              cy.wait(1000);
              productDataUploadsPage.goToRowReviewPage(uploadedFile.fileName);

              rowReviewPage.approveRows();
            });
          }
        );
      }
    );
  });

  it("Row Review: Save and filter Drafts", () => {
    const fileUrl = "../fixtures/bulk_reject_products.csv";
    const uploadStatusFilter = UploadStatusFilter.IN_REVIEW;
    const generatedFilePath = generateUploadFile(fileUrl);
    uploadName = getFileName(generatedFilePath);

    cy.uploadFile(generatedFilePath, orgId, uploadStatusFilter).then(
      (uploadId) => {
        uploadedFileId = uploadId;
        landingPage.goToProductUploadsV2();
        productDataUploadsPage.initNeedsReviewGrid(uploadName);
        productDataUploadsPage.startParseCheck(uploadedFileId);

        cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.IN_REVIEW).then(
          (uploadedFile) => {
            productDataUploadsPage.interceptProductUploads();
            cy.wait("@getProductUploads", {
              timeout: PRODUCT_UPLOAD_ENDPOINT_TIMEOUT,
            }).then(() => {
              cy.wait(1000);
              productDataUploadsPage.goToRowReviewPage(uploadedFile.fileName);

              rowReviewPage.saveDrafts(rejectRowDraftReasonMessage);
              rowReviewPage.filterByHasDraft();
              rowReviewPage.checkRows(RowStatus.PENDING, [
                rejectRowDraftReasonMessage,
              ]);
            });
          }
        );
      }
    );
  });

  it("Row Review: Bulk Reject for selected  market", () => {
    landingPage.goToProductUploadsV2();

    productDataUploadsPage.initNeedsReviewGrid(uploadName);
    productDataUploadsPage.goToRowReviewPage(uploadName);

    rowReviewPage.rejectAllRows();
    rowReviewPage.navigateToStatusTab(RowStatus.REJECTED);
    rowReviewPage.checkRows(RowStatus.REJECTED, rejectRowReasonMessages);
  });

  it("Product Uploads Grid: Should validate Upload Report", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initReviewedGrid(uploadName);
    productDataUploadsPage.downloadAndCheckUploadReport(uploadedFileId);
  });

  it("Product Uploads Grid: Validate upload with (incorrect/non exist) markets", () => {
    const fileUrl = "../fixtures/product_with_invalid_market.csv";
    const generatedFilePath = generateUploadFile(fileUrl);
    const uploadStatusFilter = UploadStatusFilter.IN_REVIEW;

    autoRejectRowProcess(generatedFilePath, orgId, uploadStatusFilter);
  });

  it("Product Uploads Grid: Validate upload with duplicated GTIN", () => {
    const fileUrl = "../fixtures/products_with_same_gtin.csv";
    const generatedFilePath = generateUploadWithSameGTIN(fileUrl);
    const uploadStatusFilter = UploadStatusFilter.IN_REVIEW;

    autoRejectRowProcess(generatedFilePath, orgId, uploadStatusFilter);
  });

  it("Filter Search: Should add some filters and display results", () => {
    const fileUrl = "../fixtures/multi-market-file.csv";
    const generatedFilePath = generateUploadFile(fileUrl);
    uploadName = getFileName(generatedFilePath);

    cy.uploadFile(generatedFilePath, orgId, UploadStatusFilter.IN_REVIEW).then(
      (uploadId) => {
        uploadedFileId = uploadId;
        landingPage.goToProductUploadsV2();
        productDataUploadsPage.checkAvailableFilters();
        productDataUploadsPage.initNeedsReviewGrid(uploadName);
        productDataUploadsPage.startParseCheck(uploadedFileId);

        cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.IN_REVIEW).then(
          () => {
            productDataUploadsPage.interceptProductUploads();
            cy.wait("@getProductUploads", {
              timeout: PRODUCT_UPLOAD_ENDPOINT_TIMEOUT,
            }).then(() => {
              productDataUploadsPage.addFilter(
                FilterOptions.CP_TYPE_FILTER,
                FilterValues.SELLER_VALUE
              );
              productDataUploadsPage.addFilter(
                FilterOptions.PENDING_MARKETS_FILTER,
                FilterValues.GERMAN_VALUE
              );
              cy.wait("@getProductUploads", {
                timeout: PRODUCT_UPLOAD_ENDPOINT_TIMEOUT,
              }).then((interception) => {
                const uploadedFileFromResponse =
                  interception.response.body.items.find(
                    (file) => file.uploadId === uploadedFileId
                  );
                expect(uploadedFileFromResponse.fileName).to.equal(uploadName);
                expect(uploadedFileFromResponse.pendingMarkets.DE).to.be.true;

                productDataUploadsPage.checkFilterResults(uploadName);
                productDataUploadsPage.clearAllFilters();
              });
            });
          }
        );
      }
    );
  });

  it("Assign Upload Market: Should assign to german market", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initNeedsReviewGrid(uploadName);
    productDataUploadsPage.setMarket(germanMarketCode);
    productDataUploadsPage.setAssignee(uploadName, uploadedFileId);
  });

  it("Assign Upload Market: Should be assigned to german market", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initNeedsReviewGrid(uploadName);
    productDataUploadsPage.setMarket(germanMarketCode);
    productDataUploadsPage.checkAssignee(
      uploadName,
      uploadedFileId,
      germanMarketCode,
      userName
    );
  });

  it("Assign Upload Market: Should assign to spanish market", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initNeedsReviewGrid(uploadName);
    productDataUploadsPage.setMarket(spanishMarketCode);
    productDataUploadsPage.setAssignee(uploadName, uploadedFileId);
  });

  it("Assign Upload Market: Should be assigned to spanish market", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initNeedsReviewGrid(uploadName);
    productDataUploadsPage.setMarket(spanishMarketCode);
    productDataUploadsPage.checkAssignee(
      uploadName,
      uploadedFileId,
      spanishMarketCode,
      userName
    );
  });

  it("Assign Upload Market: Should remove from spanish market", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initNeedsReviewGrid(uploadName);
    productDataUploadsPage.setMarket(spanishMarketCode);
    productDataUploadsPage.removeAssignee(uploadName, uploadedFileId);
  });

  it("Assign Upload Market: Should be removed from spanish market", () => {
    landingPage.goToProductUploadsV2();
    productDataUploadsPage.initNeedsReviewGrid(uploadName);
    productDataUploadsPage.setMarket(spanishMarketCode);
    productDataUploadsPage.checkAssignee(
      uploadName,
      uploadedFileId,
      spanishMarketCode,
      nonAssignedLabel
    );
  });
});
