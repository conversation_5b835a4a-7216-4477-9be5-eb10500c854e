import { TestPrefixesEnum } from '../../support/constants/shared';
import { loginApi } from '../../support/api/idam-login-api';
import { DISCOUNT_CODE_OVERVIEW_URL } from '../../support/constants/endpoints';
import {
  codeFilterQueryParam,
  promotionReason,
  createdBy,
  createdByFilterLabel,
  createdByFilterQueryParam,
  DateFilterType,
  deSalesChannel,
  discountCode,
  discountCodeFilterLabel,
  expiredStatus,
  FilterType,
  reasonFilterLabel,
  reasonFilterQueryParam,
  salesChannel,
  salesChannelFilterLabel,
  salesChannelFilterQueryParam,
  statusFilterLabel,
  statusFilterQueryParam, getDateFilterString
} from '../../support/constants/discount-code';
import { discountApi } from '../../support/api/discount-code.api';
import { dashboardPage } from '../../support/pages/employee/discount-code/dashboard';
import { dashboardLocators } from '../../support/locators/employee/discount-code/dashboard-locators';

describe.skip(`${TestPrefixesEnum.FinOps}: Discount list`, () => {
  before(() => {
    loginApi.employeeLogin('employeeLogin', `${Cypress.env('employeeUrl')}${DISCOUNT_CODE_OVERVIEW_URL}`);
  });

  beforeEach(() => cy.injectToken());

  it('[API] Get discount list', () => {
    discountApi.getDiscountList();
    cy.get('@discountList').then((response: any) => {
      cy.task('setDiscountCodeId', response.body.items[0].id);
      dashboardPage.checkDiscountCodeList(response.body);
    });
  });

  it.skip('Discount list should be loaded and the elements should be present', () => {
    dashboardPage.checkElementsPresent();
  });

  it('The user should be able to filter discount code by Start Time', () => {
    const dateFilterQueryString = getDateFilterString(DateFilterType.start)
    dashboardPage.filterByDateRange(dateFilterQueryString, dashboardLocators.startDate);
  });

  it('The user should be able to filter discount code by End Time', () => {
    const dateFilterQueryString = getDateFilterString(DateFilterType.end)
    dashboardPage.clearFilter();
    dashboardPage.filterByDateRange(dateFilterQueryString, dashboardLocators.endDate);
  });

  it('The user should be able to filter the discount code overview by Created By', () => {
    dashboardPage.clearFilter();
    dashboardPage.filter(dashboardLocators.overviewFilter,
      createdByFilterLabel,
      createdBy,
      createdByFilterQueryParam,
      dashboardLocators.mmCreatedByColumn,
      createdBy.split('@')[0], FilterType.INPUT);
  });

  it('The user should be able to filter the discount code overview by Discount Code', () => {
    dashboardPage.clearFilter();
    dashboardPage.filter(dashboardLocators.overviewFilter,
      discountCodeFilterLabel,
      discountCode,
      codeFilterQueryParam,
      dashboardLocators.mmCodeColumn,
      discountCode, FilterType.INPUT);
  });

  it('The user should be able to filter the discount code overview by Sales Channel', () => {
    dashboardPage.clearFilter();
    dashboardPage.filter(dashboardLocators.overviewFilter,
      salesChannelFilterLabel,
      salesChannel,
      salesChannelFilterQueryParam,
      dashboardLocators.mmSalesChannelColumn,
      deSalesChannel, FilterType.DROPDOWN);
  });

  it('The user should be able to filter the discount code overview by Reason', () => {
    dashboardPage.clearFilter();
    dashboardPage.filter(dashboardLocators.overviewFilter,
      reasonFilterLabel,
      promotionReason,
      reasonFilterQueryParam,
      dashboardLocators.mmAmountColumn,
      promotionReason.toLowerCase(), FilterType.DROPDOWN);
  });

  // skipping for now because of the bug MPGD-25084
  it.skip('The user should be able to filter the discount code overview by Status', () => {
    dashboardPage.clearFilter();
    dashboardPage.filter(dashboardLocators.overviewFilter,
      statusFilterLabel,
      expiredStatus,
      statusFilterQueryParam,
      dashboardLocators.mmStatusColumn,
      expiredStatus, FilterType.DROPDOWN);
  });

  it('The user should be able to deactivate active discount codes', () => {
    dashboardPage.clearFilter();
    dashboardPage.deactivateDiscountCode();
  });

  it('[API] Get discount code details', () => {
    cy.task('getDiscountCodeId').then(codeId => {
      discountApi.getDiscountCodeDetails(codeId);
      cy.get('@discountCodeDetails').then((response: any) => {
        dashboardPage.checkDiscountCodeDetails(response.body);
      });
    });
  });

  it.skip('The user should be able to view the discount code details', () => {
    dashboardPage.viewDetails();
  });

  it('The user should be able to extend the validity of discount code duration', ()=>{
    dashboardPage.extendDiscountCode();
  });

});
