import { TestPrefixesEnum } from '../../support/constants/shared';
import { loginApi } from '../../support/api/idam-login-api';
import { DISCOUNT_CODE_REDEMPTION_URL } from '../../support/constants/endpoints';
import {
  FilterType,
  orderNumberFilterLabel,
  redemptionFilterOrderNumber,
  orderNumberFilterQueryParam,
  discountCodeFilterLabel,
  setStartAndEndDates,
  discountCode
} from '../../support/constants/discount-code';
import { discountApi } from '../../support/api/discount-code.api';
import { redemptionPage } from '../../support/pages/employee/discount-code/redemption';
import { dashboardPage } from '../../support/pages/employee/discount-code/dashboard';
import { dashboardLocators } from '../../support/locators/employee/discount-code/dashboard-locators';

describe.skip(`${TestPrefixesEnum.FinOps}: Discount code redemption list`, () => {
  before(() => {
    loginApi.employeeLogin('employeeLogin', `${Cypress.env('employeeUrl')}${DISCOUNT_CODE_REDEMPTION_URL}`);
  });

  beforeEach(() => cy.injectToken());

  it('[API] Get discount redemption list', () => {
    discountApi.getDiscountRedemptionList();
    cy.get('@discountRedemptionList').then((response: any) => {
      redemptionPage.checkDiscountCodeRedemptionList(response.body);
    });
  });

  it('Redemption list should be loaded and the elements should be present', () => {
    redemptionPage.checkElementsPresent();
  });

  it('The user should be able to filter the redemption list by order number', () => {
    dashboardPage.filter(dashboardLocators.redemptionFilters,
      orderNumberFilterLabel,
      redemptionFilterOrderNumber,
      orderNumberFilterQueryParam,
      dashboardLocators.mmOrderNumberColumn,
      redemptionFilterOrderNumber, FilterType.INPUT);
  });

  it('The user should be able to filter the redemption list by discount code', () => {
    dashboardPage.clearFilter();
    dashboardPage.filter(dashboardLocators.redemptionFilters,
      discountCodeFilterLabel,
      discountCode,
      discountCode,
      dashboardLocators.mmRedemptionCodeColumn,
      discountCode, FilterType.INPUT);
  });

  it('The user should be able to clear the filter', () => {
    dashboardPage.clearFilter();
  });

  it('The user should be able to filter redemption list by Redemption Date', () => {
    setStartAndEndDates('11.21.2020', '11.23.2020'); //refer service-discount/service/tests/DataFixtures/RedemptionHistoryFixtures.php
    dashboardPage.filterByDateRange('&redeemedAtStartDate=21.11.2020&redeemedAtEndDate=23.11.2020', dashboardLocators.redemptionDate, true);
  });

});
