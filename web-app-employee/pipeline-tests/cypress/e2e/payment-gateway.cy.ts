import { apiRequests } from '../support/api/api';
import { loginApi } from '../support/api/idam-login-api'
import { addressPage } from '../support/pages/buyer/checkout/address';
import { paymentSelectionPage } from '../support/pages/buyer/checkout/payment-selection';
import { API_CHECKOUT_PAYMENT_URL, MARKETPLACE_URL, SELLER_ORDERS_URL, EMPLOYEE_PAYMENTS_URL } from '../support/constants/endpoints';
import { OrderDetails } from '../support/interfaces/user-details';
import { orderDashboardPage } from '../support/pages/seller/orders/order-dashboard';
import { shipmentDashboardPage } from '../support/pages/seller/orders/shipment-dashboard';
import { landingPage } from '../support/pages/employee/landing-page';
import testingConfig from '../support/constants/payment-gateway-config';

describe.skip('[API]_infra Payment', () => {
  // Added it.skip("[API]_infra") to temporary skip because of infra is unavailable
  const orders = testingConfig.orders;

  before(() => {
    loginApi.buyerLogin('buyerLogin');
    //loginApi.sellerLogin('sellerLogin');
  });

  beforeEach(() => cy.injectToken());

it('Tests payment methods according to a given configuration', () => {
  orders.forEach((order: OrderDetails) => {
    const paymentWidgets = order?.paymentAssertions;
    cy.intercept('POST', API_CHECKOUT_PAYMENT_URL).as('order');

      // Navigate to marketplace and clear cart from products
      cy.visit(`${Cypress.env('buyerUrl')}${MARKETPLACE_URL}`);
      apiRequests.clearCart();
      // Add product to cart
      apiRequests.addOfferToCart(order.offerId, 1)

      // Navigate to address page and checkout
      addressPage.navigateToAddressesPage();
      addressPage.proceedWithCheckout();

      // Assert for payment widgets
      paymentSelectionPage.assertForPaymentWidgets(paymentWidgets);

      // Execute payment method based on config
      paymentSelectionPage.executePaymentMethod(order);

      // Gets the orderNumber and sets it
      cy.wait('@order').then(interception => {
        const orderNumber = interception.response.body.orderNumber;

        cy.task('setOrderNumber', orderNumber.toString());
      });

      // Assert order was placed
      cy.url().should('contain', '/thank-you');
    });
  });

it.skip('Tests order is correctly shipped and confirmed', () => {

    // Navigates to seller orders and finds respective latest orderNumber paid
    cy.task('getOrderNumber').then((orderNumber: string) => {
      cy.visit(`${Cypress.env('sellerUrl')}${SELLER_ORDERS_URL}`);

      orderDashboardPage.checkOrder(orderNumber);
      orderDashboardPage.checkIfOrderIsVisible(orderNumber);

      // Confirm order selection
      orderDashboardPage.confirmStatusSelection();

      // Submit order selection
      orderDashboardPage.submitOrderSelection();

      // Assert for selected order
      orderDashboardPage.assertAdditionalSelectedOrderDetails();

      // Confirm order shipment
      shipmentDashboardPage.confirmShippedOrderSelection();
      cy.getByTestId('status-select--shipped--option').click();


      // Check for carrier and proceed
      shipmentDashboardPage.assertForCarrierAndContinue();

      // Enter tracking id and proceed
      shipmentDashboardPage.enterTrackingId();
      shipmentDashboardPage.submitShipmentSelection();

      // Assert for shipped title
      shipmentDashboardPage.assertForShippedTitle();
    });
  });

it('Tests that an order has an Authorized state in backoffice ui', () => {

    // Login and navigate to payments dashboard
    loginApi.employeeLogin('employeeLogin', `${Cypress.env('employeeUrl')}${EMPLOYEE_PAYMENTS_URL}`);

    // Check if orderNumber exists and navigate to order
    landingPage.checkIfOrderNumberExists();

    // Check to see if order was authorized
    landingPage.checkIfOrderWasAuthorized();

  })
});
