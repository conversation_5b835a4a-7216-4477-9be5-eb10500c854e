import { TestPrefixesEnum } from '../../support/constants/shared';
import { loginApi } from '../../support/api/idam-login-api';
import { DISPUTE_MANAGEMENT_URL } from '../../support/constants/endpoints';
import { disputePage } from '../../support/pages/employee/dispute-management/dispute-page';

describe.skip(`${TestPrefixesEnum.FinOps}: Dispute list`, () => {
  before(() => {
    loginApi.employeeLogin('employeeLogin', `${Cypress.env('employeeUrl')}${DISPUTE_MANAGEMENT_URL}`);
  });

  beforeEach(() => cy.injectToken());

  it('Dispute list should be loaded and user should be able to clear the filters', () => {
    disputePage.checkElementsPresent();
    disputePage.checkIfPrefilteredByWaitingForSellerResponseStatus();
    disputePage.clearFilter();
    disputePage.checkIfViewDetailsButtonExists();
  });

  it('User should be able to filter by order number', () => {
    disputePage.filterByOrderNumber();
  });

  it('User should be able to filter by status', () => {
    disputePage.clearFilter();
    disputePage.filterByOrderStatus();
  });

  it('Order Number link should take the user to the order details page in new tab', () => {
    disputePage.clickOrderNumber();
  });

});
