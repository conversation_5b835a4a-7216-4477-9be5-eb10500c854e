import { TestPrefixesEnum } from '../../support/constants/shared';
import { loginApi } from '../../support/api/idam-login-api';
import { DISPUTE_MANAGEMENT_URL_WITH_FILTER } from '../../support/constants/endpoints';
import { disputePage } from '../../support/pages/employee/dispute-management/dispute-page';
import { disputeApi } from '../../support/api/dispute-management';
import { disputeDetailsDisputeId } from '../../support/constants/dispute-management';

describe.skip(`${TestPrefixesEnum.FinOps}: Dispute details`, () => {
  before(() => {
    loginApi.employeeLogin('employeeLogin', `${Cypress.env('employeeUrl')}${DISPUTE_MANAGEMENT_URL_WITH_FILTER}`);
  });

  beforeEach(() => cy.injectToken());

  it('[API] Get dispute details', () => {
    disputeApi.getDisputeDetails(disputeDetailsDisputeId);
    cy.get('@disputeDetails').then((response: any) => {
      disputePage.checkDisputeDetails(response.body);
    });
  });

  // the following are skipped for now. will be reverted once IDAM is back.

  it.skip('[UI] User should be able to view the details page', () => {
    disputePage.clearFilter();
    disputePage.goToDetailsPage();
  });

  it.skip('[UI] Conversation tab should be visible by default', () => {
    disputePage.checkIfConversationTabIsEnabledOnLoad();
    disputePage.checkIfConversationElementsArePresent();
  });

  it.skip('[UI] User should be able to see the case details', () => {
    disputePage.goToCaseDetails();
  });

  it.skip('[UI] User should be able to see the order details', () => {
    disputePage.goToOrderDetails();
  });
});
