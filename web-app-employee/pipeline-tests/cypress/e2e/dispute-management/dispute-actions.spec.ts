import { TestPrefixesEnum } from '../../support/constants/shared';
import { loginApi } from '../../support/api/idam-login-api';
import { apiRequests } from '../../support/api/api';
import {
  API_CHECKOUT_PAYMENT_URL,
  DISPUTE_MANAGEMENT_URL,
  DISPUTE_MANAGEMENT_URL_WITH_FILTER,
  MARKETPLACE_URL
} from '../../support/constants/endpoints';
import { addressPage } from '../../support/pages/buyer/checkout/address';
import { disputePage } from '../../support/pages/employee/dispute-management/dispute-page';
import { Dispute, PaginatedResponse } from '../../support/interfaces/dispute-details';
import { statusResolved, offerId, minimumNumberOfInterception } from '../../support/constants/dispute-management';

describe(`${TestPrefixesEnum.FinOps}: Dispute actions-escalate and respond`, () => {
  before(() => {
    loginApi.employeeLogin('employeeLogin', `${Cypress.env('employeeUrl')}${DISPUTE_MANAGEMENT_URL_WITH_FILTER}`);
  });

  beforeEach(() => cy.injectToken());

  // TODO: this is skipped for now. It needs to be done in such a way to create a new dispute and escalate it. For more details see ticket MPGD-23948
  it.skip('[UI] Buy product', () => {
    const order = {
      offerId
    };
    cy.intercept('POST', API_CHECKOUT_PAYMENT_URL).as('order');
    cy.visit(`${Cypress.env('buyerUrl')}${MARKETPLACE_URL}`);
    apiRequests.clearCart();
    apiRequests.addOfferToCart(order.offerId, 1)
    addressPage.navigateToAddressesPage();
    addressPage.proceedWithCheckout();
    cy.wait('@order');
    cy.url().should('contain', '/thank-you');
  });

  //following is skipped until the unknown disputes issue is resolved.
  it.skip('[UI] Go to the dispute detail page of open dispute and escalate the dispute to claim', () => {
    disputePage.getTheFirstUnresolvedDisputeId().then(disputeId=>{
      if(disputeId){
        cy.intercept('GET', 'api/v1/paypal/disputes/*').as('disputeDetails');
        cy.visit(`${Cypress.env('employeeUrl')}${DISPUTE_MANAGEMENT_URL}/${disputeId}`);
        disputePage.escalateIssueToPayPal();
      }
    });
  });

  it.skip('[UI] Go to the dispute detail page of open dispute and respond to it', ()=>{
    cy.visit(`${Cypress.env('employeeUrl')}${DISPUTE_MANAGEMENT_URL}`)
    disputePage.getTheFirstUnresolvedDisputeId().then(disputeId=>{
      if(disputeId){
        cy.intercept('GET', 'api/v1/paypal/disputes/*').as('disputeDetails');
        cy.visit(`${Cypress.env('employeeUrl')}${DISPUTE_MANAGEMENT_URL}/${disputeId}`);
        disputePage.respondToDispute();
      }
    });
  });

});
