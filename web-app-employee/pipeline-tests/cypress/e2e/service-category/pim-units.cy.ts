import { loginApi } from "../../support/api/idam-login-api";
import { serviceCategoryApi } from "../../support/api/service-category-api";
import { landingPage } from "../../support/pages/employee/landing-page";
import { lovAndUnitsPage } from "../../support/pages/employee/lov-and-units-page";
import { germanMarketCode } from "../../support/constants/pim-constants";

describe("service-category: Get measure units", () => {
  before(() => {
    loginApi.employeeLogin("employeeLogin");
  });

  beforeEach(() => cy.injectToken());

  it("[API] List of units filtered by name, baseUnit, displayUnit and possibleUnits", () => {
    const partialName = "min";
    const queryParams = `filter[search]=${partialName}`;
    serviceCategoryApi.measureUnit.getMeasureUnits(queryParams);
    cy.get("@getMeasureUnitsFiltered").then((response: any) => {
      lovAndUnitsPage.checkMeasureUnits(response.body, partialName);
    });
  });

  it("[UI] List measure units", () => {
    landingPage.goToLandingEnPage();
    landingPage.goToAttributesPage();

    lovAndUnitsPage.changeMarketInPicker(germanMarketCode);
    lovAndUnitsPage.interceptGetMeasureUnit();
    lovAndUnitsPage.goToMeasureUnit();
    cy.wait("@getMeasureUnits").then((interception) => {
      lovAndUnitsPage.checkMeasureUnits(interception.response.body);
    });
  });
});
