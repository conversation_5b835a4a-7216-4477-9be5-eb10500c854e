import { loginApi } from '../../support/api/idam-login-api';
import { serviceCategoryApi} from "../../support/api/service-category-api";
import { landingPage } from '../../support/pages/employee/landing-page';
import { categoryTreePage } from "../../support/pages/employee/category-tree-page";
import { frenchMarketCode } from '../../support/constants/pim-constants';

describe('service-category: Get list of markets', () => {
  before(() => {
    loginApi.employeeLogin('employeeLogin');
  });

  beforeEach(() => cy.injectToken());

  it('[API] List/Read all available markets', () => {
    serviceCategoryApi.market.getAvailableMarkets();
    cy.get('@getAvailableMarkets').then((response: any) => {
      landingPage.checkStructureOfAvailableMarkets(response.body);
    });
  });

  it('[UI] List/Read all available markets', () => {
    landingPage.interceptGetAvailableMarkets();
    landingPage.goToLandingPage();

    cy.wait('@getAvailableMarkets').then(interception => {
      landingPage.checkStructureOfAvailableMarkets(interception.response.body);
    });
  });

  it('[UI] Market Picker List', () => {
    landingPage.interceptGetAvailableMarkets();
    landingPage.goToLandingEnPage();
    landingPage.goToCategoryTreePage();

    cy.wait('@getAvailableMarkets').then(interception => {
      categoryTreePage.checkMarketPickerList(interception.response.body);
    });
  });

  it('[UI] change market from Market Picker List', () => {
    landingPage.goToLandingEnPage();
    landingPage.goToCategoryTreePage();
    categoryTreePage.changeMarketInPicker(frenchMarketCode);
    categoryTreePage.checkMarketInLocalStorage(frenchMarketCode);
  });
});
