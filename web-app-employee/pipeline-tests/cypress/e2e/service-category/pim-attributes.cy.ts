import { loginApi } from "../../support/api/idam-login-api";
import { serviceCategoryApi } from "../../support/api/service-category-api";
import { landingPage } from "../../support/pages/employee/landing-page";
import { attributesPage } from "../../support/pages/employee/attributes-page";
import { attributesLocators } from "../../support/locators/employee/attributes-locators";
import { checkStatusCode, getRandomString } from "../../support/helpers";
import { AttributeType } from "../../support/constants/pim-enums";
import { checkInvalidMarketResponse, checkResponseStatus404, } from "../../support/common";
import { AttributeCondition, AttributeUnit, } from "../../support/interfaces/attribute-model";
import {
  attributeBuilder,
  attributeFixturesByType,
  getAttributeFixture,
  mapAttribute,
} from "../../builders/pim-attributes-builder";
import { acceptLanguages, germanMarketCode, invalidId, invalidMarket, } from "../../support/constants/pim-constants";

describe("service-category: Get Attributes", () => {
  before(() => {
    loginApi.employeeLogin("employeeLogin");
  });

  beforeEach(() => cy.injectToken());

  //#region "GET Attribute List"
  it("[API] Get list of all attributes sorted by default (by name) per market", () => {
    serviceCategoryApi.attribute.getAttributes(germanMarketCode);
    cy.get("@getAttributes").then((response: any) => {
      attributesPage.checkAttributes(response);
    });
  });

  it("[API] Get list of all attributes by invalid market", () => {
    serviceCategoryApi.attribute.getAttributes(invalidMarket);
    cy.get("@getAttributes").then((response: any) => {
      checkInvalidMarketResponse(response);
    });
  });

  it("[API] Get list of all attributes sorted by code sorted DESC per market", () => {
    const queryParams = "?sort[code]=DESC";

    serviceCategoryApi.attribute.getAttributes(germanMarketCode, queryParams);
    cy.get("@getAttributes").then((response: any) => {
      attributesPage.checkAttributeCodes(response);
    });
  });

  it("[API] Get list of all attributes sorted by type per market", () => {
    const queryParams = "?sort[type]=ASC&limit=1000";

    serviceCategoryApi.attribute.getAttributes(germanMarketCode, queryParams);
    cy.get("@getAttributes").then((response: any) => {
      attributesPage.checkAttributesSortedByType(response);
    });
  });

  it("[UI] Search Attribute by name", () => {
    landingPage.goToLandingEnPage();
    landingPage.goToAttributesPage();


    attributesPage.changeMarketInPicker(germanMarketCode);

    const searchName = "energy";
    attributesPage.interceptAttributeSearch(searchName);

    cy.get(attributesLocators.attributesSearch)
      .clear()
      .type(`${searchName}{enter}`);

    cy.wait("@getPimAttributeSearch").then((interception: any) => {
      const attributes = interception.response.body;
      attributesPage.checkAttributePreview(attributes, searchName);
    });
  });
  //#endregion "GET Attribute List"

  //#region "Create New Attribute"
  it("[API] Create Boolean attribute that is not filterable c188259", () => {
    const params = {type: AttributeType.BOOLEAN};
    const newAttribute = attributeBuilder(params);

    serviceCategoryApi.attribute.createAttribute(
      newAttribute,
      germanMarketCode
    );
    cy.get("@createAttribute").then((response: any) => {
      attributesPage.checkAttributeCreated(AttributeType.BOOLEAN, response);
    });
  });

  it("[API] Check code and CSV header duplicate validations C188266", () => {
    const existingAttribute = getAttributeFixture(AttributeType.BOOLEAN);

    serviceCategoryApi.attribute.getAttributeByMarket(
      germanMarketCode,
      existingAttribute.id
    );
    cy.get("@getAttributeByMarket").then((response: any) => {
      const existingAttributeName = response.body.code;
      const params = {
        type: AttributeType.BOOLEAN,
        name: existingAttributeName,
      };
      const newAttribute = attributeBuilder(params);

      serviceCategoryApi.attribute.createAttribute(
        newAttribute,
        germanMarketCode
      );
      cy.get("@createAttribute").then((response: any) => {
        checkStatusCode(response, 400);
      });
    });
  });

  it("[API] Create Text attribute without example 188260", () => {
    const params = {type: AttributeType.TEXT};
    const newAttribute = attributeBuilder(params);

    serviceCategoryApi.attribute.createAttribute(
      newAttribute,
      germanMarketCode
    );
    cy.get("@createAttribute").then((response: any) => {
      attributesPage.checkAttributeCreated(AttributeType.TEXT, response);
    });
  });

  it("[API] Create Integer attribute without unit and description c188261", () => {
    const params = {type: AttributeType.INTEGER};
    const newAttribute = attributeBuilder(params);

    serviceCategoryApi.attribute.createAttribute(
      newAttribute,
      germanMarketCode
    );
    cy.get("@createAttribute").then((response: any) => {
      attributesPage.checkAttributeCreated(AttributeType.INTEGER, response);
    });
  });

  it("[API] Create File attribute", () => {
    const params = {type: AttributeType.FILE};
    const newAttribute = attributeBuilder(params);

    serviceCategoryApi.attribute.createAttribute(
      newAttribute,
      germanMarketCode
    );
    cy.get("@createAttribute").then((response: any) => {
      attributesPage.checkAttributeCreated(AttributeType.FILE, response);
    });
  });

  it("[API] Create LOV attribute", () => {
    serviceCategoryApi.lov.getLovsByMarket(germanMarketCode);
    cy.get("@getLovsByMarketFiltered").then((response: any) => {
      const lovId = response.body.items[0].id;
      const params = {type: AttributeType.LOV, lovId};
      const newAttribute = attributeBuilder(params);

      serviceCategoryApi.attribute.createAttribute(
        newAttribute,
        germanMarketCode
      );
      cy.get("@createAttribute").then((response: any) => {
        attributesPage.checkAttributeCreated(AttributeType.LOV, response);
      });
    });
  });

  it("[API] Create Decimal attribute with units c188263", () => {
    const partialName = "unit";
    const queryParams = `filter[search]=${partialName}`;

    serviceCategoryApi.measureUnit.getMeasureUnits(queryParams);
    cy.get("@getMeasureUnitsFiltered").then((response: any) => {
      const measureUnit = response.body.items.find(
        (item) => item.baseUnit === partialName
      );
      const unit: AttributeUnit = {csvPredefined: false, id: measureUnit.id};
      const params = {
        type: AttributeType.DECIMAL,
        unit: unit,
      };
      const newAttribute = attributeBuilder(params);

      serviceCategoryApi.attribute.createAttribute(
        newAttribute,
        germanMarketCode
      );
      cy.get("@createAttribute").then((response: any) => {
        attributesPage.checkAttributeCreated(AttributeType.DECIMAL, response);
      });
    });
  });

  it("[API] Create Image attribute without multi-value allowed C188264", () => {
    const params = {type: AttributeType.IMAGE};
    const newAttribute = attributeBuilder(params);

    serviceCategoryApi.attribute.createAttribute(
      newAttribute,
      germanMarketCode
    );
    cy.get("@createAttribute").then((response: any) => {
      attributesPage.checkAttributeCreated(AttributeType.IMAGE, response);
    });
  });

  it("[API] Create LongText attribute", () => {
    const params = {type: AttributeType.LONGTEXT};
    const newAttribute = attributeBuilder(params);

    serviceCategoryApi.attribute.createAttribute(
      newAttribute,
      germanMarketCode
    );
    cy.get("@createAttribute").then((response: any) => {
      attributesPage.checkAttributeCreated(AttributeType.LONGTEXT, response);
    });
  });

  it("[API] Create attribute without mandatory fields C188267", () => {
    const newAttribute = attributeBuilder({type: null});
    serviceCategoryApi.attribute.createAttribute(
      newAttribute,
      germanMarketCode
    );
    cy.get("@createAttribute").then((response: any) => {
      checkStatusCode(response, 400);
    });
  });

  it("[API] Check max symbols validation for name, code, CSV header and description fields C188265", () => {
    const randomString = `${getRandomString(125)}_${getRandomString(200)}`;
    const params = {
      type: AttributeType.TEXT,
      name: randomString,
    };
    const newAttribute = attributeBuilder(params);

    serviceCategoryApi.attribute.createAttribute(
      newAttribute,
      germanMarketCode
    );
    cy.get("@createAttribute").then((response: any) => {
      checkStatusCode(response, 400);
    });
  });

  it("[API] Wrong attribute type id and front-end flag values C188268", () => {
    const wrongValue = 10000;
    const params = {
      type: wrongValue,
      frontEndFlag: wrongValue.toString(),
    };
    const newAttribute = attributeBuilder(params);

    serviceCategoryApi.attribute.createAttribute(
      newAttribute,
      germanMarketCode
    );
    cy.get("@createAttribute").then((response: any) => {
      checkStatusCode(response, 400);
    });
  });

  it("[API] No English name translation C188256.", () => {
    const params = {
      type: AttributeType.BOOLEAN,
      translationFirstLang: acceptLanguages[2],
    };
    const newAttribute = attributeBuilder(params);

    serviceCategoryApi.attribute.createAttribute(
      newAttribute,
      germanMarketCode
    );
    cy.get("@createAttribute").then((response: any) => {
      checkStatusCode(response, 400);
    });
  });

  //#endregion "Create New Attribute"

  //#region "Get Attribute Details"
  it("[API] Get attribute details by type", () => {
    attributeFixturesByType.forEach((attribute) => {
      serviceCategoryApi.attribute.getAttribute(attribute.id);
      cy.get("@getAttributeById").then((response: any) => {
        attributesPage.checkAttributeTypeDetails(attribute.type, response.body);
      });
    });
  });

  it("[API] Get attribute details by wrong id", () => {
    serviceCategoryApi.attribute.getAttribute(invalidId);
    cy.get("@getAttributeById").then((response: any) => {
      checkResponseStatus404(response);
    });
  });

  it("[API] Update LOV attribute with LOV Group and multiple flag change C188310", () => {
    const attribute = getAttributeFixture(AttributeType.LOV);

    serviceCategoryApi.attribute.getAttribute(attribute.id);
    cy.get("@getAttributeById").then((attributeResponse: any) => {
      checkStatusCode(attributeResponse);

      serviceCategoryApi.lov.getLovsByMarket(germanMarketCode);
      cy.get("@getLovsByMarketFiltered").then((LOVsResponse: any) => {
        checkStatusCode(LOVsResponse);

        const LOVId = LOVsResponse.body.items[0]?.id;
        const translationName = `testCY${getRandomString(125)}`;
        const condition: AttributeCondition = {
          unique: false,
          filterable: false,
          multiple: !attributeResponse.body.condition.multiple,
        };
        const LOVAttribute = mapAttribute(
          attributeResponse.body,
          translationName,
          condition,
          undefined,
          LOVId
        );

        serviceCategoryApi.attribute.updateAttribute(
          LOVAttribute,
          germanMarketCode,
          attribute.id
        );
        cy.get("@updateAttribute").then((updateAttributeResponse: any) => {
          checkStatusCode(updateAttributeResponse);

          serviceCategoryApi.attribute.getAttribute(
            updateAttributeResponse.body.id
          );
          cy.get("@getAttributeById").then((updatedAttributeResponse: any) => {
            attributesPage.checkAttributeUpdated(
              updatedAttributeResponse,
              LOVAttribute,
              LOVId,
              null
            );
          });
        });
      });
    });
  });

  it("[API] Update Integer attribute: set Unit Group and change filterable flag C188311", () => {
    const attribute = getAttributeFixture(AttributeType.INTEGER);

    serviceCategoryApi.attribute.getAttribute(attribute.id);
    cy.get("@getAttributeById").then((integerResponse: any) => {
      checkStatusCode(integerResponse);

      serviceCategoryApi.measureUnit.getMeasureUnits();
      cy.get("@getMeasureUnitsFiltered").then((unitsResponse: any) => {
        checkStatusCode(unitsResponse);

        const translationName = `testCY${getRandomString(125)}`;
        const unit: AttributeUnit = {
          id: unitsResponse.body.items[0]?.id,
          csvPredefined: true,
        };
        const condition: AttributeCondition = {
          unique: false,
          filterable: !integerResponse.body.condition.filterable,
          multiple: false,
        };
        const integerAttribute = mapAttribute(
          integerResponse.body,
          translationName,
          condition,
          unit
        );

        serviceCategoryApi.attribute.updateAttribute(
          integerAttribute,
          germanMarketCode,
          attribute.id
        );
        cy.get("@updateAttribute").then((updateAttributeResponse: any) => {
          checkStatusCode(updateAttributeResponse);

          serviceCategoryApi.attribute.getAttribute(
            updateAttributeResponse.body.id
          );
          cy.get("@getAttributeById").then((updatedAttributeResponse: any) => {
            attributesPage.checkAttributeUpdated(
              updatedAttributeResponse,
              integerAttribute,
              null,
              unit
            );
          });
        });
      });
    });
  });

  it("[API] Update Decimal attribute: remove Unit Group and change name, description, example and frontend value C188312", () => {
    const attribute = getAttributeFixture(AttributeType.DECIMAL);

    serviceCategoryApi.attribute.getAttribute(attribute.id);
    cy.get("@getAttributeById").then((decimalResponse: any) => {
      checkStatusCode(decimalResponse);

      const translationName = `testCY${getRandomString(125)}`;
      const unit = null;
      const decimalAttribute = mapAttribute(
        decimalResponse.body,
        translationName,
        decimalResponse.body.condition,
        unit
      );

      serviceCategoryApi.attribute.updateAttribute(
        decimalAttribute,
        germanMarketCode,
        attribute.id
      );
      cy.get("@updateAttribute").then((updateAttributeResponse: any) => {
        serviceCategoryApi.attribute.getAttribute(
          updateAttributeResponse.body.id
        );
        cy.get("@getAttributeById").then((updatedAttributeResponse: any) => {
          attributesPage.checkAttributeUpdated(
            updatedAttributeResponse,
            decimalAttribute,
            null,
            unit
          );
        });
      });
    });
  });

  it("[API] Update Boolean attribute: do not send required parameters C188314", () => {
    const attribute = getAttributeFixture(AttributeType.BOOLEAN);

    serviceCategoryApi.attribute.getAttribute(attribute.id);
    cy.get("@getAttributeById").then((booleanResponse: any) => {
      checkStatusCode(booleanResponse);

      const translationName = "";
      const unit = null;
      const booleanAttribute = mapAttribute(
        booleanResponse.body,
        translationName,
        booleanResponse.body.condition,
        unit
      );

      serviceCategoryApi.attribute.updateAttribute(
        booleanAttribute,
        germanMarketCode,
        attribute.id
      );
      cy.get("@updateAttribute").then((updateAttributeResponse: any) => {
        checkStatusCode(updateAttributeResponse, 400);
      });
    });
  });
  //#endregion "Get Attribute Details"

  //#region "Update Existing Attribute"
  it("[API] Update non-existing attribute id C188315", () => {
    const params = {type: AttributeType.LOV};
    const newAttribute = attributeBuilder(params);

    serviceCategoryApi.attribute.updateAttribute(
      newAttribute,
      germanMarketCode,
      invalidId
    );
    cy.get("@updateAttribute").then((updateAttributeResponse: any) => {
      checkStatusCode(updateAttributeResponse, 404);
    });
  });

  it("[API] Update LongText attribute: change description and translations", () => {
    const attribute = getAttributeFixture( AttributeType.LONGTEXT);
    serviceCategoryApi.attribute.getAttribute(attribute.id);
    cy.get("@getAttributeById").then((response: any) => {
      checkStatusCode(response);

      const translationName = `testCY${getRandomString(10)}`;
      const longTextAttribute = mapAttribute(
        response.body,
        translationName,
        response.body.condition,
        null
      );

      serviceCategoryApi.attribute.updateAttribute(
        longTextAttribute,
        germanMarketCode,
        attribute.id
      );

      cy.get("@updateAttribute").then((updateAttributeResponse: any) => {
        checkStatusCode(updateAttributeResponse);
      });
    });
  });
  //#endregion "Update Existing Attribute"
});
