import { loginApi } from "../../support/api/idam-login-api";
import { serviceCategoryApi } from "../../support/api/service-category-api";
import { lovAndUnitsPage } from "../../support/pages/employee/lov-and-units-page";
import { availableMarkets } from "../../support/constants/pim-constants";
import { getRandomNumber, getTranslation } from "../../support/helpers";
import * as LovBuilder from "../../builders/pim-lov-builder";
import {
  FILTER_KEY_NAME,
  Languages,
  LOV_NAME_TEST,
  LOV_SUFFIXES,
} from "../../support/constants/pim-lov-constants";
import { checkInvalidMarketResponse } from "../../support/common";
import { landingPage } from "../../support/pages/employee/landing-page";
import { lovAndUnitsLocators } from "../../support/locators/employee/lov-and-units-locators";

describe("service-category: LOVs", () => {
  let lovPrefix = "";
  let lovName = "";

  before(() => {
    loginApi.employeeLogin("employeeLogin");
    const random: number = getRandomNumber(10000);
    lovPrefix = `${LOV_NAME_TEST}${random}`;
  });

  beforeEach(() => cy.injectToken());

  it("[UI] Create a new LOV", (): void => {
    landingPage.goToLandingEnPage();
    landingPage.goToAttributesPage();
    lovAndUnitsPage.goToLOVs();
    lovAndUnitsPage.goToCreateLov();
    lovAndUnitsPage.interceptCreateLov();
    lovName = lovAndUnitsPage.setValuesIntoLov(lovPrefix);
    cy.wait("@createLov").then((response) => {
      lovAndUnitsPage.checkStatusCode(response);
    });
  });

  it("[UI] Edit an existing LOV", () => {
    const updatedTranslation: string = `${lovPrefix}_${LOV_SUFFIXES.UPDATED}`;
    landingPage.goToLandingEnPage();
    landingPage.goToAttributesPage();
    lovAndUnitsPage.goToLOVs();
    lovAndUnitsPage.interceptLovs();
    lovAndUnitsPage.searchLov(lovName);

    cy.wait("@getLovsByMarket").then((interception) => {
      const lov = interception.response.body.items.find(
        (item) => item.name === lovName
      );
      lovAndUnitsPage.interceptLovDetails(lov.id);
      cy.get(lovAndUnitsLocators.editButton(lov.id)).click();
      cy.wait("@getLovDetails").then((interception) => {
        const lovDetails = interception.response.body;
        lovAndUnitsPage.searchValue(lovDetails.options[0].name);
        lovAndUnitsPage.interceptUpdateLov(lov.id);
        lovAndUnitsPage.editTranslationAndRemoveMapping(
          Languages.DE,
          updatedTranslation
        );
        lovAndUnitsPage.checkLovUpdated(
          lov.id,
          Languages.DE,
          updatedTranslation
        );
      });
    });
  });

  it("[UI] Search LOV and validate data in sidebar", () => {
    landingPage.goToLandingEnPage();
    landingPage.goToAttributesPage();
    lovAndUnitsPage.goToLOVs();
    lovAndUnitsPage.interceptLovs();
    lovAndUnitsPage.searchLov(lovName);

    cy.wait("@getLovsByMarket").then((interception) => {
      const responseBody = interception.response.body;
      const lov = responseBody.items.find((item) => item.name === lovName);
      lovAndUnitsPage.interceptLovDetails(lov.id);
      cy.get(lovAndUnitsLocators.previewButton(lov.id)).click();
      cy.wait("@getLovDetails").then((interception) => {
        const lovDetails = interception.response.body;
        const options = lovDetails.options;

        lovAndUnitsPage.validateSidebarContent(lovDetails);
        lovAndUnitsPage.performSearchInSidebar(lovPrefix);
        lovAndUnitsPage.validateSearchedOptionContent(lovPrefix, options);
      });
    });
  });

  it("[UI] List all LOVs sorted by name", () => {
    landingPage.goToLandingEnPage();
    landingPage.goToAttributesPage();
    lovAndUnitsPage.goToLOVs();
    lovAndUnitsPage.updatePaginator();
    lovAndUnitsPage.interceptLovs();
    cy.wait("@getLovsByMarket").then((interception) => {
      lovAndUnitsPage.checkLovsUI(interception.response.body);
    });
  });

  it("[API] Search LOV by name", () => {
    const queryParams = `filter[${FILTER_KEY_NAME}]=${lovName}`;

    serviceCategoryApi.lov.getLovsByMarket(availableMarkets[0], queryParams);
    cy.get("@getLovsByMarketFiltered").then((response: any) => {
      lovAndUnitsPage.checkLovsApi(response.body, FILTER_KEY_NAME, lovName);
    });
  });

  it("[API] Search LOV by non-existing name", () => {
    const filterValue = "non-existing-LOV";
    const queryParams = `filter[${FILTER_KEY_NAME}]=${filterValue}`;

    serviceCategoryApi.lov.getLovsByMarket(availableMarkets[0], queryParams);
    cy.get("@getLovsByMarketFiltered").then((response: any) => {
      lovAndUnitsPage.checkNonExistingLovsApi(response.body);
    });
  });

  it("[API] List LOVs by invalid market", () => {
    const invalidMarket = "XX";

    serviceCategoryApi.lov.getLovsByMarket(invalidMarket, "");
    cy.get("@getLovsByMarketFiltered").then((response) => {
      checkInvalidMarketResponse(response);
    });
  });

  it("[API] Check structure of LOV response", () => {
    const queryParams = `filter[${FILTER_KEY_NAME}]=${lovName}`;

    serviceCategoryApi.lov.getLovsByMarket(availableMarkets[0], queryParams);
    cy.get("@getLovsByMarketFiltered").then((response: any) => {
      const items = response.body.items;

      if (items && items.length > 0) {
        const lovId = items.find((lov) => {
          return lov.name === lovName;
        }).id;

        serviceCategoryApi.lov.getLovDetails(lovId);
        cy.get("@getLovDetails").then((response: any) => {
          lovAndUnitsPage.checkLovDetailsApi(response.body);
        });
      }
    });
  });
  //#endregion "GET List of values LOV"

  //#region "GET LOV details"

  it("[API] Get LOV details by invalid id", () => {
    const invalidId = "abc";

    serviceCategoryApi.lov.getLovDetails(invalidId);
    cy.get("@getLovDetails").then((response) => {
      lovAndUnitsPage.checkLovDetailsByInvalidId(response);
    });
  });

  //#endregion "GET LOV details"

  //#region "Create LOV"
  it("[API] Create LOV: with unique name, few options and transformations", () => {
    serviceCategoryApi.lov.createLov(LovBuilder.createLovBuilder());
    cy.get("@createLovFiltered").then((response: any) => {
      lovAndUnitsPage.checkCreatedLOVWithUniqueName(response);
      serviceCategoryApi.lov.getLovDetails(response.body.id);
      cy.get("@getLovDetails").then((response: any) => {
        lovAndUnitsPage.checkGetLOV(response);
        lovName = getTranslation(response.body.translations, Languages.EN);
      });
    });
  });

  it("[API] Create LOV: with non-unique name", () => {
    serviceCategoryApi.lov.createLov(LovBuilder.createNonUniqueLovBuilder());
    serviceCategoryApi.lov.createLov(LovBuilder.createNonUniqueLovBuilder());
    cy.get("@createLovFiltered").then((response: any) => {
      lovAndUnitsPage.checkLOVHasInvalidParams(response);
    });
  });

  it("[API] Create LOV: with empty name", () => {
    serviceCategoryApi.lov.createLov(
      LovBuilder.createLovWithEmptyNamesBuilder()
    );
    cy.get("@createLovFiltered").then((response: any) => {
      lovAndUnitsPage.checkLOVHasInvalidParams(response);
    });
  });

  it("[API] Create LOV: without any options", () => {
    serviceCategoryApi.lov.createLov(
      LovBuilder.createLovWithoutOptionsBuilder()
    );
    cy.get("@createLovFiltered").then((response: any) => {
      lovAndUnitsPage.checkLOVHasInvalidParams(response);
    });
  });

  it("[API] Create LOV: with an empty option", () => {
    serviceCategoryApi.lov.createLov(
      LovBuilder.createLovWithEmptyOptionsBuilder()
    );
    cy.get("@createLovFiltered").then((response: any) => {
      lovAndUnitsPage.checkLOVHasInvalidParams(response);
    });
  });

  it("[API] Create LOV: with repeated options", () => {
    serviceCategoryApi.lov.createLov(
      LovBuilder.createLovWithRepeatedOptionsBuilder()
    );
    cy.get("@createLovFiltered").then((response: any) => {
      lovAndUnitsPage.checkLOVHasInvalidParams(response);
    });
  });
  //#endregion "Create LOV"

  //#region "Update Existing LOV"
  it("[API] Edit LOV: Edit Name, Options and Mappings", () => {
    const queryParams = `filter[${FILTER_KEY_NAME}]=${lovName}`;

    serviceCategoryApi.lov.getLovsByMarket(availableMarkets[0], queryParams);
    cy.get("@getLovsByMarketFiltered").then((response: any) => {
      const id = response.body.items[0].id;

      serviceCategoryApi.lov.getLovDetails(id);
      cy.get("@getLovDetails").then((response: any) => {
        serviceCategoryApi.lov.updateLov(
          id,
          LovBuilder.updateLovBuilder(response.body)
        );
        cy.get("@updateLov").then((response: any) => {
          lovAndUnitsPage.checkStatusCode(response);
          serviceCategoryApi.lov.getLovDetails(id);

          cy.get("@getLovDetails").then((response: any) => {
            lovAndUnitsPage.checkGetLOV(response);
          });
        });
      });
    });
  });

  it("[API] Edit LOV: with non-unique name", () => {
    serviceCategoryApi.lov.getLovsByMarket(availableMarkets[0], "");
    cy.get("@getLovsByMarketFiltered").then((response: any) => {
      const firstLOVId = response.body.items[0].id;
      const secondLOVId = response.body.items[1].id;

      serviceCategoryApi.lov.getLovDetails(secondLOVId);
      cy.get("@getLovDetails").then((response: any) => {
        const propNames = {
          en: getTranslation(response.body.translations, "en"),
          de: getTranslation(response.body.translations, "de"),
        };

        serviceCategoryApi.lov.updateLov(
          firstLOVId,
          LovBuilder.updateLovWithNonUniqueNameBuilder(response.body, propNames)
        );
        cy.get("@updateLov").then((response: any) => {
          lovAndUnitsPage.checkStatusCode(response, 400);
        });
      });
    });
  });

  it("[API] Edit LOV: with empty name", () => {
    serviceCategoryApi.lov.getLovsByMarket(availableMarkets[0], "");
    cy.get("@getLovsByMarketFiltered").then((response: any) => {
      const id = response.body.items[0].id;

      serviceCategoryApi.lov.updateLov(
        id,
        LovBuilder.updateLovWithEmptyNameBuilder()
      );
      cy.get("@updateLov").then((response: any) => {
        lovAndUnitsPage.checkLOVHasInvalidParams(response);
      });
    });
  });

  it("[API] Edit LOV: without any options", () => {
    serviceCategoryApi.lov.getLovsByMarket(availableMarkets[0], "");
    cy.get("@getLovsByMarketFiltered").then((response: any) => {
      const id = response.body.items[0].id;

      serviceCategoryApi.lov.updateLov(
        id,
        LovBuilder.updateLovWithoutOptionsBuilder()
      );
      cy.get("@updateLov").then((response: any) => {
        lovAndUnitsPage.checkLOVHasInvalidParams(response);
      });
    });
  });

  it("[API] Edit LOV: with an empty option", () => {
    serviceCategoryApi.lov.getLovsByMarket(availableMarkets[0], "");
    cy.get("@getLovsByMarketFiltered").then((response: any) => {
      const id = response.body.items[0].id;

      serviceCategoryApi.lov.updateLov(
        id,
        LovBuilder.updateLovWithEmptyOptionsBuilder()
      );
      cy.get("@updateLov").then((response: any) => {
        lovAndUnitsPage.checkLOVHasInvalidParams(response);
      });
    });
  });

  it("[API] Edit LOV: with repeated options", () => {
    serviceCategoryApi.lov.getLovsByMarket(availableMarkets[0], "");
    cy.get("@getLovsByMarketFiltered").then((response: any) => {
      const items = response.body.items;

      if (items && items.length > 0) {
        serviceCategoryApi.lov.updateLov(
          items[0].id,
          LovBuilder.updateLovWithRepeatingOptionsBuilder()
        );
        cy.get("@updateLov").then((response: any) => {
          lovAndUnitsPage.checkLOVHasInvalidParams(response);
        });
      }
    });
  });

  it("[API] Edit LOV: Non-existing id", () => {
    const invalidId = "0";
    serviceCategoryApi.lov.updateLov(
      invalidId,
      LovBuilder.updateLovByNonExistingIdBuilder()
    );
    cy.get("@updateLov").then((response: any) => {
      lovAndUnitsPage.checkLovDetailsByInvalidId(response);
    });
  });
});
//#endregion "Update Existing LOV"
