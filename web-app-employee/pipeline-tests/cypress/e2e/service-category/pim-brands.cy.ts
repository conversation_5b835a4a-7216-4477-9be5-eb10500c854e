import { loginApi } from "../../support/api/idam-login-api";
import { serviceCategoryApi } from "../../support/api/service-category-api";
import { landingPage } from "../../support/pages/employee/landing-page";
import { brandsPage } from "../../support/pages/employee/brands-page";
import { count } from "../../support/constants/pim-constants";
import { brandBuilder } from "../../builders/pim-brand-builder";
import { getRandomItem, getRandomNumber, getRandomString, } from "../../support/helpers";
import { BRAND_NAME, FilterHorecaOptions, } from "../../support/constants/pim-brands-constants";
import { brandsLocators } from '../../support/locators/employee/brands-locators';

describe("service-category: Get brands", () => {
  before(() => {
    loginApi.employeeLogin("employeeLogin");
  });

  beforeEach(() => cy.injectToken());

  //#region "Get Brand List"

  it("[API] List of brands filtered by name C187249", () => {
    const queryParams = `filter[name]=asc`;
    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      brandsPage.checkBrandsWithCorrectName(response);
    });
  });

  it("[UI] List of brands filtered by name or id C187249", () => {
    landingPage.goToLandingEnPage();
    brandsPage.interceptBrands();
    landingPage.goToBrandsPage();

    cy.wait("@getPimBrands");

    const searchFilter = BRAND_NAME;

    brandsPage.searchBrand(searchFilter);
    cy.wait("@getFilteredBrands").then((interception) => {
      brandsPage.checkBrandsWithCorrectName(interception.response, searchFilter);
      cy.get(brandsLocators.cleanAllFilters).click();
      brandsPage.horecaFilter(FilterHorecaOptions.Yes);
    });
  });

  it("[API] List of brands searched by brand name C187698", () => {
    const partialName = "ch";
    const queryParams = `filter[search]=${partialName}`;
    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      brandsPage.checkBrandsWithCorrectName(response, partialName);
    });
  });

  it("[API] List of brands searched by brand name - wrong name C187699", () => {
    const partialWrongName = "рр";
    const queryParams = `filter[search]=${partialWrongName}`;
    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      brandsPage.checkBrandsWithWrongName(response);
    });
  });

  it("[API] Brand details by id C187275", () => {
    const partialName = "ch";
    const queryParams = `filter[search]=${partialName}`;
    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const items = response.body.items;
      if (items && items.length > 0) {
        const randomId = getRandomItem(items.slice(0, 10)).id;
        serviceCategoryApi.brand.getBrandById(queryParams, randomId);
        cy.get("@getBrandByIdFiltered").then((response: any) => {
          brandsPage.checkBrandWithValidId(response);
        });
      }
    });
  });
  //#endregion "Get Brand List"

  //#region "Get Brand Details"
  it("[UI] Brand details and logo", () => {
    landingPage.goToLandingPage();
    brandsPage.interceptBrands();
    landingPage.goToBrandsPage();

    cy.wait("@getPimBrands").then((interception) => {
      const items = interception.response.body.items;
      if (items && items.length > 0) {
        const randomBrandId = getRandomItem(items.slice(0, 10)).id;
        brandsPage.interceptBrand(randomBrandId);
        brandsPage.goToBrand(randomBrandId);

        cy.wait("@getPimBrand").then((interception) => {
          brandsPage.checkBrandWithValidId(interception.response);

          brandsPage.interceptPostBrandLogo(randomBrandId);
          brandsPage.postBrandLogo();
          brandsPage.checkIfLogoIsAdded();

          brandsPage.interceptDeleteBrandLogo(randomBrandId);
          brandsPage.deleteBrandLogo();
          brandsPage.checkIfLogoIsDeleted();
        });
      }
    });
  });

  it("[API] Brand details by invalid id C187276", () => {
    const partialName = "ch";
    const queryParams = `filter[search]=${partialName}`;
    serviceCategoryApi.brand.getBrandById(queryParams, "1");
    cy.get("@getBrandByIdFiltered").then((response: any) => {
      brandsPage.checkBrandWithInvalidId(response);
    });
  });
  //#endregion "Get Brand Details"

  //#region "Create New Brand"
  it("[API] Create Brand with non-unique name and without transformations C187284", () => {
    const queryParams = `filter[name]=asc`;

    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const params = {
        name: response.body.items[0].name,
        mappings: [],
      };
      const data = brandBuilder(params);

      serviceCategoryApi.brand.create(data);
      cy.get("@createBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });

  it("[API] Create Brand with unique name and one unique transformation C187253", () => {
    const randomNumber = getRandomNumber(count);
    const uniqueBrandName = `unique_brand_name_${randomNumber}`;
    const uniqueTransformationName = `unique_transformation_name_${randomNumber}`;

    const params = {
      name: uniqueBrandName,
      mappings: [
        {
          name: uniqueTransformationName,
        },
      ],
    };
    const data = brandBuilder(params);

    serviceCategoryApi.brand.create(data);
    cy.get("@createBrand").then((response: any) => {
      brandsPage.checkValidBrand(response);

      const queryParams = `filter[search]=${uniqueBrandName}`;

      serviceCategoryApi.brand.getBrands(queryParams);
      cy.get("@getBrandsFiltered").then((response: any) => {
        brandsPage.checkBrandsWithUniqueName(
          response.body.items[0],
          uniqueBrandName
        );
        brandsPage.checkStatusCode(response);
      });
    });
  });

  it("[API] Create Brand with unique name and one unique transformation with trailing white-spaces", () => {
    const randomNumber = getRandomNumber(count);
    const whiteSpaces = "   ";
    const uniqueBrandName = `unique_brand_name_${randomNumber}`;
    const uniqueTransformationName = `unique_transformation_name_${randomNumber}`;

    const params = {
      name: `${uniqueBrandName}${whiteSpaces}`,
      mappings: [
        {
          name: `${uniqueTransformationName}${whiteSpaces}`,
        },
      ],
    };
    const data = brandBuilder(params);

    serviceCategoryApi.brand.create(data);
    cy.get("@createBrand").then((response: any) => {
      brandsPage.checkValidBrand(response);

      const queryParams = `filter[search]=${uniqueBrandName}`;

      serviceCategoryApi.brand.getBrands(queryParams);
      cy.get("@getBrandsFiltered").then((response: any) => {
        brandsPage.checkBrandsWithUniqueName(
          response.body.items[0],
          uniqueBrandName,
          uniqueTransformationName
        );
        brandsPage.checkStatusCode(response);
      });
    });
  });

  it("[API] Create Brand with unique name and without transformations C187257", () => {
    const randomNumber = getRandomNumber(count);
    const uniqueBrandName = `unique_brand_name_${randomNumber}`;
    const params = {
      name: uniqueBrandName,
      mappings: [],
    };
    const data = brandBuilder(params);

    serviceCategoryApi.brand.create(data);
    cy.get("@createBrand").then(() => {
      const queryParams = `filter[search]=${uniqueBrandName}`;

      serviceCategoryApi.brand.getBrands(queryParams);
      cy.get("@getBrandsFiltered").then((response: any) => {
        brandsPage.checkBrandsWithUniqueName(
          response.body.items[0],
          uniqueBrandName
        );
        brandsPage.checkStatusCode(response);
      });
    });
  });

  it("[UI] Create Brand with unique name and without transformations C187257", () => {
    landingPage.goToLandingPage();
    brandsPage.interceptBrands();
    landingPage.goToBrandsPage();

    cy.wait("@getPimBrands").then(() => {
      brandsPage.interceptCreateBrand();
      brandsPage.goToAddNewBrandForm();
      brandsPage.setBrandName(getRandomNumber(count));
      cy.wait("@createBrand").then((interception) => {
        brandsPage.checkStatusCode(interception);
      });
    });
  });

  it("[API] Create a brand with name same as other Brand name C187254", () => {
    const queryParams = `filter[name]=asc`;

    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const brandName = response.body.items[0].name;
      const params = { name: brandName };
      const data = brandBuilder(params);

      serviceCategoryApi.brand.create(data);
      cy.get("@createBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });

  it("[API] Create a brand with name same as transformation from another Brand C187255", () => {
    const randomNumber = getRandomNumber(count);
    const transformationName = `transformation_from_brand_${randomNumber}`;
    const baseBrandName = `base_brand_${randomNumber}`;

    const baseBrand = brandBuilder({
      name: baseBrandName,
      mappings: [{ name: transformationName }],
    });

    serviceCategoryApi.brand.create(baseBrand);
    cy.get("@createBrand").then((baseBrandResponse: any) => {
      brandsPage.checkValidBrand(baseBrandResponse);

      const conflictingBrand = brandBuilder({
        name: transformationName,
        mappings: [],
      });

      serviceCategoryApi.brand.create(conflictingBrand);
      cy.get("@createBrand").then((conflictResponse: any) => {
        brandsPage.checkInvalidBrand(conflictResponse);
      });
    });
  });

  it("[API] Create a brand with transformation same as name from another Brand C187264", () => {
    const queryParams = `filter[name]=asc`;

    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const randomNumber = getRandomNumber(count);
      const params = {
        name: `unique_brand_name_${randomNumber}`,
        mappings: [
          {
            name: response.body.items[0].name,
          },
        ],
      };
      const data = brandBuilder(params);

      serviceCategoryApi.brand.create(data);
      cy.get("@createBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });

  it("[API] Create a brand with transformation same as transformation from another Brand C187277", () => {
    const queryParams = `filter[name]=asc&limit=1000`;

    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const getItemWithNonEmtyMappings =
        brandsPage.getItemWithNonEmptyMappings(response);
      const randomNumber = getRandomNumber(count);

      const params = {
        name: `unique_brand_name_${randomNumber}`,
        mappings: [
          {
            name: getItemWithNonEmtyMappings.mappings[0].name,
          },
        ],
      };
      const data = brandBuilder(params);

      serviceCategoryApi.brand.create(data);
      cy.get("@createBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });

  it("[API] Create a brand with matching name and transformation C187256", () => {
    const randomNumber = getRandomNumber(count);
    const params = {
      name: `repeated_name_${randomNumber}`,
      mappings: [
        {
          name: `repeated_name_${randomNumber}`,
        },
      ],
    };

    const data = brandBuilder(params);
    serviceCategoryApi.brand.create(data);
    cy.get("@createBrand").then((response: any) => {
      brandsPage.checkInvalidBrand(response);
    });
  });

  it("[API] Create a brand with matching transformations C187273", () => {
    const randomNumber = getRandomNumber(count);
    const params = {
      name: `random_name_${randomNumber}`,
      mappings: [
        {
          name: `repeated_name_${randomNumber}`,
        },
        {
          name: `repeated_name_${randomNumber}`,
        },
      ],
    };
    const data = brandBuilder(params);

    serviceCategoryApi.brand.create(data);
    cy.get("@createBrand").then((response: any) => {
      brandsPage.checkInvalidBrand(response);
    });
  });

  it("[API] Create a brand with empty name C187258", () => {
    const data = brandBuilder();

    serviceCategoryApi.brand.create(data);
    cy.get("@createBrand").then((response: any) => {
      brandsPage.checkInvalidBrand(response);
    });
  });

  it("[API] Create a brand with name > 255 symbols C187259", () => {
    const randomNumber = getRandomNumber(count);
    const generatedLongString = getRandomString(256);
    const params = { name: `${generatedLongString}_${randomNumber}` };
    const data = brandBuilder(params);

    serviceCategoryApi.brand.create(data);
    cy.get("@createBrand").then((response: any) => {
      brandsPage.checkInvalidBrand(response);
    });
  });

  it("[API] Create a brand with only white-spaces name", () => {
    const name = "       ";

    serviceCategoryApi.brand.create({ name });
    cy.get("@createBrand").then((response: any) => {
      brandsPage.checkInvalidBrand(response);
    });
  });
  //#endregion "Create New Brand"

  //#region "Update Existing Brand"
  it("[API] Update a brand with unique name and add unique transformation C187265", () => {
    const queryParams = `filter[name]=asc&limit=1000`;

    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const randomNumber = getRandomNumber(count);
      const getItemWithNonEmptyMappings =
        brandsPage.getItemWithNonEmptyMappings(response);
      const uniqueBrandName = `unique_brand_name_${randomNumber}`;
      const uniqueTransformation = `unique_transformation_${randomNumber}`;

      const params = {
        name: uniqueBrandName,
        mappings: [
          ...getItemWithNonEmptyMappings.mappings,
          { id: null, name: uniqueTransformation },
        ],
      };
      const data = brandBuilder(params);

      serviceCategoryApi.brand.update(getItemWithNonEmptyMappings.id, data);
      cy.get("@updateBrand").then((response: any) => {
        brandsPage.checkStatusCode(response);

        serviceCategoryApi.brand.getBrandById(
          queryParams,
          getItemWithNonEmptyMappings.id
        );
        cy.get("@getBrandByIdFiltered").then((response: any) => {
          brandsPage.checkBrandsWithUniqueName(
            response.body,
            uniqueBrandName,
            uniqueTransformation
          );
        });
      });
    });
  });

  it("[UI] Update a brand with unique name and add unique transformation C187265", () => {
    const random = getRandomNumber(count);
    const name = `brand_ui_${random}`;

    brandsPage.createBrand(name);

    const queryParams = `filter[search]=${name}`;
    serviceCategoryApi.brand.getBrands(queryParams);

    cy.get("@getBrandsFiltered").then((res: any) => {
      const brandId = res.body.items[0].id;

      landingPage.goToLandingPage();
      brandsPage.interceptEditBrand(brandId);
      brandsPage.interceptBrand(brandId);
      brandsPage.interceptBrands();

      landingPage.goToBrandsPage();
      cy.wait("@getPimBrands").then(() => {

        brandsPage.searchBrand(name);
        cy.wait("@getFilteredBrands").then(() => {

          brandsPage.goToBrand(brandId);
          cy.wait("@getPimBrand").then(() => {

            brandsPage.updateBrand(getRandomNumber(count));
            cy.wait("@editBrand").then((res) => {
              brandsPage.checkStatusCode(res);
            });
          });
        });
      });
    });
  });

  it("[API] Update a brand with unique name and remove transformations C187266", () => {
    const random = getRandomNumber(count);
    const name = `brand_ui_${random}`;
    const transformation = `trans_ui_${random}`;

    brandsPage.createBrand(name, transformation);

    const queryParams = `filter[search]=${name}`;
    serviceCategoryApi.brand.getBrands(queryParams);

    cy.get("@getBrandsFiltered").then((response: any) => {
      const createdBrand = response.body.items[0];
      const uniqueBrandName = `unique_brand_name_${getRandomNumber(count)}`;
      const params = { name: uniqueBrandName }; // sin mappings
      const data = brandBuilder(params);

      serviceCategoryApi.brand.update(createdBrand.id, data);
      cy.get("@updateBrand").then((response: any) => {
        brandsPage.checkStatusCode(response);

        serviceCategoryApi.brand.getBrandById(queryParams, createdBrand.id);
        cy.get("@getBrandByIdFiltered").then((response: any) => {
          brandsPage.checkBrandsWithUniqueName(response.body, uniqueBrandName);

          expect(response.body.mappings).to.be.empty;
        });
      });
    });
  });

  it("[API] Update a brand with name same as other Brand name C187267", () => {
    const queryParams = `filter[name]=asc`;

    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const firstBrand = response.body.items[0];
      const secondBrand = response.body.items[1];
      const params = { name: secondBrand.name };
      const data = brandBuilder(params);

      serviceCategoryApi.brand.update(firstBrand.id, data);
      cy.get("@updateBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });

  it("[API] Update a brand with name same as transformation from another Brand C187268", () => {
    const random = getRandomNumber(count);
    const brandWithMappingName = `original_brand_${random}`;
    const transformation = `trans_conflict_${random}`;
    const targetBrandName = `target_brand_${random}`;

    brandsPage.createBrand(brandWithMappingName, transformation);
    brandsPage.createBrand(targetBrandName);

    const queryParams = `filter[search]=${targetBrandName}`;
    serviceCategoryApi.brand.getBrands(queryParams);

    cy.get("@getBrandsFiltered").then((response: any) => {
      const brandToUpdate = response.body.items[0];

      const data = brandBuilder({ name: transformation });
      serviceCategoryApi.brand.update(brandToUpdate.id, data);

      cy.get("@updateBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });

  it("[API] Update a brand with transformation same as name from another Brand C187269", () => {
    const queryParams = `filter[name]=asc`;

    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const firstBrand = response.body.items[0];
      const secondBrand = response.body.items[1];
      const randomNumber = getRandomNumber(count);
      const uniqueBrandName = `unique_brand_name_${randomNumber}`;

      const params = {
        name: uniqueBrandName,
        mappings: [{ id: null, name: secondBrand.name }],
      };
      const data = brandBuilder(params);

      serviceCategoryApi.brand.update(firstBrand.id, data);
      cy.get("@updateBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });

  it("[API] Update a brand with transformation same as transformation from another Brand C187278", () => {
    const random = getRandomNumber(count);

    const brandWithMappingName = `original_brand_${random}`;
    const transformation = `trans_conflict_${random}`;
    const targetBrandName = `target_brand_${random}`;

    brandsPage.createBrand(brandWithMappingName, transformation);
    brandsPage.createBrand(targetBrandName);

    const queryParams = `filter[search]=${targetBrandName}`;
    serviceCategoryApi.brand.getBrands(queryParams);

    cy.get("@getBrandsFiltered").then((response: any) => {
      const brandToUpdate = response.body.items[0];

      const params = {
        name: targetBrandName,
        mappings: [{ id: null, name: transformation }],
      };
      const data = brandBuilder(params);

      serviceCategoryApi.brand.update(brandToUpdate.id, data);

      cy.get("@updateBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });

  it("[API] Update a brand with matching name and transformation C187270", () => {
    const queryParams = `filter[name]=asc`;

    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const firstBrand = response.body.items[0];
      const randomNumber = getRandomNumber(count);
      const nonUniqueBrandName = `repeated_brand_name_${randomNumber}`;

      const params = {
        name: nonUniqueBrandName,
        mappings: [{ id: null, name: nonUniqueBrandName }],
      };
      const data = brandBuilder(params);

      serviceCategoryApi.brand.update(firstBrand.id, data);
      cy.get("@updateBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });

  it("[API] Update a brand with matching transformations C187274", () => {
    const queryParams = `filter[name]=asc`;

    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const firstBrand = response.body.items[0];
      const randomNumber = getRandomNumber(count);

      const params = {
        name: `random_name_${randomNumber}`,
        mappings: [
          {
            name: `repeated_name_${randomNumber}`,
          },
          {
            name: `repeated_name_${randomNumber}`,
          },
        ],
      };
      const data = brandBuilder(params);

      serviceCategoryApi.brand.update(firstBrand.id, data);
      cy.get("@updateBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });

  it("[API] Update a brand with empty name C187271", () => {
    const queryParams = `filter[name]=asc`;

    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const firstBrand = response.body.items[0];
      const data = brandBuilder();

      serviceCategoryApi.brand.update(firstBrand.id, data);
      cy.get("@updateBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });

  it("[API] Update a brand with name > 255 symbols C187272", () => {
    const queryParams = `filter[name]=asc`;

    serviceCategoryApi.brand.getBrands(queryParams);
    cy.get("@getBrandsFiltered").then((response: any) => {
      const randomNumber = getRandomNumber(count);
      const generatedLongString = getRandomString(256);
      const firstBrand = response.body.items[0];
      const name = `${generatedLongString}_${randomNumber}`;
      const params = { name };
      const data = brandBuilder(params);

      serviceCategoryApi.brand.update(firstBrand.id, data);
      cy.get("@updateBrand").then((response: any) => {
        brandsPage.checkInvalidBrand(response);
      });
    });
  });
  //#endregion "Update Existing Brand"
});
