import { loginApi } from '../../support/api/idam-login-api';
import { serviceCategoryApi } from '../../support/api/service-category-api';
import { categoryTreePage } from '../../support/pages/employee/category-tree-page';
import { AttributeType } from '../../support/constants/pim-enums';
import { attributeListBuilder } from '../../builders/pim-attributes-builder';
import {
  checkStatusCode,
  getChildCategory,
  getRandomNumber,
  getRandomString,
  getTranslation,
  statusCodes
} from '../../support/helpers';
import {
  categoryDetailBuilder,
  categoryWithoutTaxesBuilder,
  categoryWithTaxesBuilder,
  itemTypeBuider,
} from '../../builders/pim-category-tree-builder';
import {
  acceptLanguages,
  availableMarkets,
  count,
  germanMarketCode,
  invalidMarket,
  invalidTaxValue,
  relevance,
  sortingDirection,
  spanishMarketCode,
  taxValue,
  transactionFee,
  waitingTimeToProcessStatusUpdate,
} from '../../support/constants/pim-constants';
import { landingPage } from '../../support/pages/employee/landing-page';


let createdCategoryId = null;
let createdChildCategoryId = null;
let createdItemTypeId = null;

function updateCreatedCategoriesState(ids: string[], isActive: boolean, market: string = germanMarketCode): Cypress.Chainable {
  let promise = cy.wrap(null);

  ids.forEach(id => {
    promise = promise.then(() => {
      serviceCategoryApi.category.updateCategoryState({'isActive': isActive}, market, id);
      return cy.get('@updateCategoryState').then((response: any) => {
        categoryTreePage.checkCategoryWithoutContent(response);
        cy.wait(waitingTimeToProcessStatusUpdate);
      });
    });
  });
  return promise;
}

describe('service-category: Get root categories', () => {
  before(() => {
    loginApi.employeeLogin('employeeLogin');
  });

  beforeEach(() => cy.injectToken());

  it('[UI] List root categories', () => {
    landingPage.goToLandingEnPage();
    categoryTreePage.interceptRootCategories();
    landingPage.goToCategoryTreePage();
    cy.wait('@getRootCategoriesByMarket').then(interception => {
      categoryTreePage.checkRootCategories(interception.response.body);
    });
  });

  it('[UI] Create new unique root category C187654', () => {
    landingPage.goToLandingEnPage();
    categoryTreePage.interceptRootCategories();
    landingPage.goToCategoryTreePage();
    cy.wait('@getRootCategoriesByMarket').then(() => {
      const randomNumber = getRandomNumber(count);

      categoryTreePage.interceptCreateRootCategory();
      categoryTreePage.goToAddNewCategoryForm();
      categoryTreePage.setCategory(randomNumber, taxValue);

      cy.wait('@createRootCategoryByMarket').then(interception => {
        categoryTreePage.checkRootCategoryHasId(interception.response);
      });
    })
  });

  it('[API] List root categories', () => {
    serviceCategoryApi.category.getRootCategories();
    cy.get('@getRootCategories').then((response: any) => {
      categoryTreePage.checkRootCategories(response.body);
    })
  });

  it('[API] Create new unique root category C187654', () => {
    const randomNumber = getRandomNumber(count);
    const uniqueNameDe = `unique_name_de_${randomNumber}`;
    const uniqueNameEn = `unique_name_en_${randomNumber}`;

    const data = categoryWithTaxesBuilder({taxValue, uniqueNameDe, uniqueNameEn});

    serviceCategoryApi.category.createRootCategory(data);
    cy.get('@createRootCategory').then((response: any) => {
      categoryTreePage.checkRootCategoryHasId(response);

      createdCategoryId = response.body.id;

      serviceCategoryApi.category.getCategoryDetail(createdCategoryId);
      cy.get('@getCategoryDetail').then((response: any) => {
        const params = {uniqueNameEn, uniqueNameDe, taxValue, transactionFee};
        categoryTreePage.checkRootCategoryParams(response.body, params);
      });
    })
  });

  it('[API] Create new unique child category C187673', () => {
    const randomNumber = getRandomNumber(count);
    const uniqueNameEn = `unique_name_en_${randomNumber}`;
    const uniqueNameDe = `unique_name_de_${randomNumber}`;
    const data = categoryWithoutTaxesBuilder({uniqueNameDe, uniqueNameEn});

    serviceCategoryApi.category.createChildCategory(data, createdCategoryId);
    cy.get('@createChildCategory').then((response: any) => {
      createdChildCategoryId = response.body.id;

      serviceCategoryApi.category.getCategoryDetail(createdChildCategoryId);
      cy.get('@getCategoryDetail').then((response: any) => {
        categoryTreePage.checkChildCategoryParams(response.body, {uniqueNameEn, uniqueNameDe});
      });
    });
  });

  it('[API] Create new unique item type', () => {
    const randomNumber = getRandomNumber(count);
    const uniqueNameEn = `unique_name_en_${randomNumber}`;
    const uniqueNameDe = `unique_name_de_${randomNumber}`;
    const data = categoryWithoutTaxesBuilder({uniqueNameDe, uniqueNameEn});

    serviceCategoryApi.category.createChildCategory(data, createdChildCategoryId);
    cy.get('@createChildCategory').then((response: any) => {
      createdItemTypeId = response.body.id;

      serviceCategoryApi.category.getCategoryDetail(createdItemTypeId);
      cy.get('@getCategoryDetail').then((response: any) => {
        categoryTreePage.checkChildCategoryParams(response.body, {uniqueNameEn, uniqueNameDe});
      });
    });
  });

  it('[API] Create root category with non-unique name C187655', () => {
    let uniqueNameDe = null;
    let uniqueNameEn = null;

    serviceCategoryApi.category.getRootCategories(acceptLanguages[0]);
    cy.get('@getRootCategories').then((response: any) => {
      uniqueNameDe = response.body.items[0].name;

      // accept-language = 'en'
      serviceCategoryApi.category.getRootCategories(acceptLanguages[1]);
      cy.get('@getRootCategories').then((response: any) => {
        uniqueNameEn = response.body.items[0].name;

        let data = categoryWithTaxesBuilder({taxValue, uniqueNameDe, uniqueNameEn});

        serviceCategoryApi.category.createRootCategory(data);
        cy.get('@createRootCategory').then((response: any) => {
          checkStatusCode(response, statusCodes.BAD_REQUEST);
        });
      });
    });
  });

  it('[API] Create child category with non-unique name C187674', () => {
    serviceCategoryApi.category.getRootCategories(acceptLanguages[1]);
    cy.get('@getRootCategories').then((response: any) => {
      const rootCategoryEn = response.body.items[0];

      serviceCategoryApi.category.getRootCategories(acceptLanguages[0]);
      cy.get('@getRootCategories').then((response: any) => {
        const rootCategoryDe = response.body.items[0];
        const data = categoryWithoutTaxesBuilder({
          uniqueNameDe: rootCategoryDe.name,
          uniqueNameEn: rootCategoryEn.name
        });

        serviceCategoryApi.category.createChildCategory(data, rootCategoryEn.id);
        cy.get('@createChildCategory').then((response: any) => {
          checkStatusCode(response, statusCodes.BAD_REQUEST);
        });
      });
    });
  });

  it('[API] Create new unique root category with trailing white-spaces in category names', () => {
    const randomNumber = getRandomNumber(count);
    const whiteSpaces = '   ';
    const uniqueNameEn: string = `unique_name_en_${randomNumber}`;
    const uniqueNameDe: string = `unique_name_de_${randomNumber}`;
    const data = categoryWithTaxesBuilder({
      taxValue,
      uniqueNameDe: `${uniqueNameDe}${whiteSpaces}`,
      uniqueNameEn: `${uniqueNameEn}${whiteSpaces}`
    });

    serviceCategoryApi.category.createRootCategory(data);
    cy.get('@createRootCategory').then((response: any) => {
      categoryTreePage.checkRootCategoryHasId(response);

      const categoryId = response.body.id;

      serviceCategoryApi.category.getCategoryDetail(categoryId);
      cy.get('@getCategoryDetail').then((response: any) => {
        const params = {uniqueNameEn, uniqueNameDe, taxValue, transactionFee};
        categoryTreePage.checkRootCategoryParams(response.body, params);
      });
    })
  });

  it('[API] Try to create root category without tax value C187656', () => {
    const randomNumber = getRandomNumber(count);
    const taxValue = null;
    const uniqueNameDe = `unique_name_de_${randomNumber}`;
    const uniqueNameEn = `unique_name_en_${randomNumber}`;
    const data = categoryWithTaxesBuilder({taxValue, uniqueNameDe, uniqueNameEn});

    serviceCategoryApi.category.createRootCategory(data);
    cy.get('@createRootCategory').then((response: any) => {
      checkStatusCode(response, statusCodes.BAD_REQUEST);
    });
  });

  it('[API] Try to create child category without parent category id C187675', () => {
    const randomNumber = getRandomNumber(count);
    const uniqueNameDe = `unique_name_de_${randomNumber}`;
    const uniqueNameEn = `unique_name_en_${randomNumber}`;
    const data = categoryWithoutTaxesBuilder({uniqueNameDe, uniqueNameEn});

    serviceCategoryApi.category.createChildCategory(data, null);
    cy.get('@createChildCategory').then((response: any) => {
      categoryTreePage.checkCategoryNotFound(response);
    });
  });

  it('[API] Try to create child category with wrong parent category id C187676', () => {
    const randomNumber = getRandomNumber(count);
    const uniqueNameDe = `unique_name_de_${randomNumber}`;
    const uniqueNameEn = `unique_name_en_${randomNumber}`;
    const id = randomNumber.toString();
    const data = categoryWithoutTaxesBuilder({uniqueNameDe, uniqueNameEn});

    serviceCategoryApi.category.createChildCategory(data, id);

    cy.get('@createChildCategory').then((response: any) => {
      categoryTreePage.checkCategoryNotFound(response);
    });
  });

  it('[API] Try to create root category without name C187658', () => {
    const taxValue = null;
    const uniqueNameDe = null;
    const uniqueNameEn = null;
    const data = categoryWithTaxesBuilder({taxValue, uniqueNameDe, uniqueNameEn});

    serviceCategoryApi.category.createRootCategory(data);
    cy.get('@createRootCategory').then((response: any) => {
      checkStatusCode(response, statusCodes.BAD_REQUEST);
    });
  });

  it('[API] Try to create root category with only whitespaces in category name', () => {
    const whitSpaces = '       ';
    const uniqueNameDe = whitSpaces;
    const uniqueNameEn = whitSpaces;
    const data = categoryWithTaxesBuilder({taxValue, uniqueNameDe, uniqueNameEn});

    serviceCategoryApi.category.createRootCategory(data);
    cy.get('@createRootCategory').then((response: any) => {
      checkStatusCode(response, statusCodes.BAD_REQUEST);
    });
  });

  it('[API] Try to create child category without name C187677', () => {
    serviceCategoryApi.category.getRootCategories(acceptLanguages[1]);
    cy.get('@getRootCategories').then((response: any) => {
      const rootCategoryEn = response.body.items[0];
      const uniqueNameDe = null;
      const uniqueNameEn = null;
      const data = categoryWithoutTaxesBuilder({uniqueNameDe, uniqueNameEn});

      serviceCategoryApi.category.createChildCategory(data, rootCategoryEn.id);
      cy.get('@createChildCategory').then((response: any) => {
        checkStatusCode(response, statusCodes.BAD_REQUEST);
      });
    });
  });

  it('[API] Change category state to Active when itemType state is InActive C374324', () => {
    serviceCategoryApi.category.updateCategoryState({'isActive': true}, germanMarketCode, createdCategoryId);
    cy.get('@updateCategoryState').then((response: any) => {
      categoryTreePage.checkCategoryWithoutContent(response);

      // Category
      serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, createdCategoryId);
      cy.get('@getCategoryDetailByMarket').then((response: any) => {
        categoryTreePage.checkCategoryIsActive(response.body);
      });

      // SubCategory
      serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, createdChildCategoryId);
      cy.get('@getCategoryDetailByMarket').then((response: any) => {
        categoryTreePage.checkCategoryIsNotActive(response.body);
      });

      // Item Type
      serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, createdItemTypeId);
      cy.get('@getCategoryDetailByMarket').then((response: any) => {
        categoryTreePage.checkItemTypeIsNotActive(response.body);
      });
    });
  });

  it('[API] Change Category state to inActive when Child and itemType states are active C374326', () => {
    updateCreatedCategoriesState([createdCategoryId, createdChildCategoryId, createdItemTypeId], true, germanMarketCode).then(() => {
      serviceCategoryApi.category.updateCategoryState({'isActive': false}, germanMarketCode, createdCategoryId);
      cy.get('@updateCategoryState').then((response: any) => {
        categoryTreePage.checkCategoryWithoutContent(response);
        // Root Category
        serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, createdCategoryId);
        cy.get('@getCategoryDetailByMarket').then((response: any) => {
          categoryTreePage.checkCategoryIsNotActive(response.body);
        });
      });
    });
  });

  it('[API] Change Child state to Active when Parent and itemType states are inActive C374325', () => {
    updateCreatedCategoriesState([createdCategoryId], false).then(() => {
      serviceCategoryApi.category.updateCategoryState({'isActive': true}, germanMarketCode, createdChildCategoryId);
      cy.get('@updateCategoryState').then((response: any) => {
        categoryTreePage.checkParentCategoryIsNotActive(response);

        // Root Category
        serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, createdCategoryId);
        cy.get('@getCategoryDetailByMarket').then((response: any) => {
          categoryTreePage.checkItemTypeIsNotActiveAndParentActiveHasChild(response.body);
        });

        // SubCategory
        serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, createdChildCategoryId);
        cy.get('@getCategoryDetailByMarket').then((response: any) => {
          categoryTreePage.checkCategoryIsNotActive(response.body);
        });

        // Item Type
        serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, createdItemTypeId);
        cy.get('@getCategoryDetailByMarket').then((response: any) => {
          categoryTreePage.checkItemTypeIsNotActive(response.body);
        });
      });
    });
  });

  it('[API] Change category state for incorrect market C374318', () => {
    serviceCategoryApi.category.updateCategoryState({isActive: false}, invalidMarket, createdItemTypeId);
    cy.get('@updateCategoryState').then((response: any) => {
      categoryTreePage.checkIncorrectMarket(response);
    });
  });

  it('[API] Update VAT, Transaction Fee and Reporting Category for category C90044', () => {
    serviceCategoryApi.category.getCategoryDetail(createdItemTypeId);
    cy.get('@getCategoryDetail').then(() => {
      const randomNumber = getRandomNumber(count);
      const updatedItemType = {
        name: {
          en: `unique_category_name_en_${randomNumber}`,
          de: `unique_category_name_de_${randomNumber}`,
        },
        transactionFee: taxValue,
        taxesAmount: taxValue,
        metroReportingCategory: getRandomString(10)
      };
      const updatedItemTypeData = categoryDetailBuilder(updatedItemType);

      serviceCategoryApi.category.updateCategoryDetail(germanMarketCode, createdItemTypeId, updatedItemTypeData);
      cy.get('@updateCategoryDetail').then((response: any) => {
        checkStatusCode(response);
      });
    });
  });


  it('[API] Update VAT and Transaction Fee with value > max C90046', () => {
    serviceCategoryApi.category.getCategoryDetail(createdItemTypeId);
    cy.get('@getCategoryDetail').then(() => {
      const randomNumber = getRandomNumber(count);
      const updatedItemType = {
        name: {
          en: `unique_category_name_en_${randomNumber}`,
          de: `unique_category_name_de_${randomNumber}`,
        },
        transactionFee: count,
        taxesAmount: count,
        metroReportingCategory: getRandomString(10)
      };
      const updatedItemTypeData = categoryDetailBuilder(updatedItemType);

      serviceCategoryApi.category.updateCategoryDetail(germanMarketCode, createdItemTypeId, updatedItemTypeData);
      cy.get('@updateCategoryDetail').then((response: any) => {
        checkStatusCode(response, statusCodes.BAD_REQUEST);
      });
    });
  });

  it('[API] Change Item Type state to inActive for one market C374319', () => {
    const categories = [createdCategoryId, createdChildCategoryId, createdItemTypeId];
    updateCreatedCategoriesState(categories, true, germanMarketCode).then(() => {
      updateCreatedCategoriesState(categories, true, spanishMarketCode).then(() => {
        serviceCategoryApi.category.updateCategoryState({'isActive': false}, spanishMarketCode, createdItemTypeId);
        cy.get('@updateCategoryState').then((response: any) => {
          categoryTreePage.checkCategoryWithoutContent(response);

          // Get ES category
          serviceCategoryApi.category.getCategoryDetailByMarket(spanishMarketCode, createdItemTypeId);
          cy.get('@getCategoryDetailByMarket').then((response: any) => {
            categoryTreePage.checkItemTypeIsNotActiveAndParentIsActive(response.body);
          });

          // Get DE category
          serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, createdItemTypeId);
          cy.get('@getCategoryDetailByMarket').then((response: any) => {
            categoryTreePage.checkItemTypeIsActiveAndParentIsActive(response.body);
          });
        });
      });
    });
  });

  it('[API] Change Item Type state from inActive to Active for one market C374320', () => {

    serviceCategoryApi.category.updateCategoryState({'isActive': true}, spanishMarketCode, createdItemTypeId);
    cy.get('@updateCategoryState').then((response: any) => {
      categoryTreePage.checkCategoryWithoutContent(response);

      // Get DE category
      serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, createdItemTypeId);
      cy.get('@getCategoryDetailByMarket').then((response: any) => {
        categoryTreePage.checkItemTypeIsActive(response.body);
      });

      // Get ES category
      serviceCategoryApi.category.getCategoryDetailByMarket(spanishMarketCode, createdItemTypeId);
      cy.get('@getCategoryDetailByMarket').then((response: any) => {
        categoryTreePage.checkItemTypeIsActive(response.body);
      });
    });
  });

  it('[API] Change itemType state - Not found C374321', () => {
    const randomitemTypeId = 'xxx';

    serviceCategoryApi.category.updateCategoryState({'isActive': true}, germanMarketCode, randomitemTypeId);
    cy.get('@updateCategoryState').then((response: any) => {
      categoryTreePage.checkCategoryNotFound(response);
    });
  });

  it('[API] Assign several attributes to an Item Type with different relevance C186990', () => {
    serviceCategoryApi.categoryAttributes.getUnassignedCategoryAttributes(createdItemTypeId);
    cy.get('@getUnassignedCategoryAttributes').then((response: any) => {
      if (response.body.items.length > 0) {
        const relevanceArray = [relevance[0], relevance[1], relevance[2], relevance[3]];
        const attributeTypes = [
          AttributeType.LOV,
          AttributeType.BOOLEAN,
          AttributeType.DECIMAL,
          AttributeType.INTEGER
        ];
        const attributes = attributeTypes.map(type =>
          response.body.items.find(item => item.type.id === type)
        );

        const data = attributeListBuilder(attributes, relevanceArray);

        serviceCategoryApi.categoryAttributes.addCategoryAttributes(data, createdItemTypeId);
        cy.get('@addCategoryAttributes').then((response: any) => {
          categoryTreePage.checkAddedCategoryAttributes(response);

          serviceCategoryApi.categoryAttributes.getCategoryAttributes(createdItemTypeId);
          cy.get('@getCategoryAttributes').then((response: any) => {
            categoryTreePage.checkCategoryAttributes(data, response);

            serviceCategoryApi.categoryAttributes.getCategoryAssignedAttributes(germanMarketCode, createdItemTypeId);
            cy.get('@getCategoryAssignedAttributes').then((response: any) => {
              categoryTreePage.checkItemTypeAttributes([AttributeType.LOV, AttributeType.BOOLEAN, AttributeType.DECIMAL, AttributeType.INTEGER], response.body.items);
            });
          });
        });
      }
    });
  });

  it('[API] Unassign several non-mandatory assigned attributes from itemType C187005', () => {
    serviceCategoryApi.categoryAttributes.getUnassignedCategoryAttributes(createdItemTypeId);

    cy.get('@getUnassignedCategoryAttributes').then((response: any) => {
      if (response.body.items.length > 0) {
        const body = [response.body.items[0].id, response.body.items[1].id];

        serviceCategoryApi.categoryAttributes.unassignAttributesFromCategory(body, createdItemTypeId);
        cy.get('@unassignAttributesFromCategory').then((response: any) => {
          categoryTreePage.checkAddedCategoryAttributesUnassigned(response);

          serviceCategoryApi.categoryAttributes.getCategoryAttributes(createdItemTypeId);
          cy.get('@getCategoryAttributes').then((response: any) => {
            categoryTreePage.checkCategoryAttributesNotFound(body, response);
          });
        });
      }
    });
  });


  it('[API] Set base price attribute for category without basePrice C187816', () => {
    serviceCategoryApi.categoryAttributes.getCategoryAttributes(createdItemTypeId);
    cy.get('@getCategoryAttributes').then((response: any) => {
      const attr = response.body.items
        .find(item => item.type.id == AttributeType.INTEGER || item.type.id == AttributeType.DECIMAL);

      if (attr) {
        const typeId = attr.id;
        const body = {'attributeId': typeId};

        serviceCategoryApi.categoryAttributes.addBasePriseToCategory(body, createdItemTypeId);
        cy.get('@addBasePriseToCategory').then((response: any) => {
          checkStatusCode(response, 204);

          serviceCategoryApi.categoryAttributes.getCategoryAttributes(createdItemTypeId);
          cy.get('@getCategoryAttributes').then((response: any) => {
            const basePriceAttribute = response.body.items.find(item => item.id == typeId);
            categoryTreePage.checkCategoryBasePriceIsActive(basePriceAttribute);
          });
        });
      }
    });
  });

  it('[API] Change base price attribute for category with base price C187817', () => {
    serviceCategoryApi.categoryAttributes.getCategoryAttributes(createdItemTypeId);
    cy.get('@getCategoryAttributes').then((response: any) => {
      const typeIds = response.body.items.filter(item => item.type.id == AttributeType.INTEGER || item.type.id == AttributeType.DECIMAL);

      if (typeIds.length > 0) {
        const initialBasePriceAttr = typeIds[0].id;
        const newBasePriceAttr = typeIds[1].id;
        const body = {'attributeId': initialBasePriceAttr};

        serviceCategoryApi.categoryAttributes.addBasePriseToCategory(body, createdItemTypeId);
        cy.get('@addBasePriseToCategory').then(() => {
          const body = {'attributeId': newBasePriceAttr};

          serviceCategoryApi.categoryAttributes.addBasePriseToCategory(body, createdItemTypeId);
          cy.get('@addBasePriseToCategory').then((response: any) => {
            checkStatusCode(response, 204);

            serviceCategoryApi.categoryAttributes.getCategoryAttributes(createdItemTypeId);
            cy.get('@getCategoryAttributes').then((response: any) => {
              const newBasePrice = response.body.items.find(item => item.id == newBasePriceAttr);
              categoryTreePage.checkCategoryBasePriceIsActive(newBasePrice);

              const initialBasePrice = response.body.items.find(item => item.id == initialBasePriceAttr);
              categoryTreePage.checkCategoryBasePriceIsInActive(initialBasePrice);
            });
          });
        });
      }
    });
  });

  it('[API] Remove base price attribute from with base price C187818', () => {
    serviceCategoryApi.categoryAttributes.getCategoryAttributes(createdItemTypeId);
    cy.get('@getCategoryAttributes').then((response: any) => {
      const attr = response.body.items.find(item => item.isBasePrice == true);

      if (attr) {
        const body = {'attributeId': null};

        serviceCategoryApi.categoryAttributes.addBasePriseToCategory(body, createdItemTypeId);
        cy.get('@addBasePriseToCategory').then((response: any) => {
          checkStatusCode(response, 204);

          serviceCategoryApi.categoryAttributes.getCategoryAttributes(createdItemTypeId);
          cy.get('@getCategoryAttributes').then((response: any) => {
            categoryTreePage.checkCategoryAllBasePricesAreInActive(response.body.items);
          });
        });
      }
    });
  });

  it('[API] Get attributes of item type C374148', () => {
    serviceCategoryApi.categoryAttributes.getCategoryAttributes(createdItemTypeId);
    cy.get('@getCategoryAttributes').then((response: any) => {
      categoryTreePage.checkItemTypeStructure(response);
    });
  });

  it('[API] Get attribute list by categoryID C186889', () => {
    serviceCategoryApi.categoryAttributes.getCategoryAttributes(createdItemTypeId);
    cy.get('@getCategoryAttributes').then((response: any) => {
      categoryTreePage.checkCategoryRelevanceAttribute(response);
    });
  });

  it('[API] Get attribute list with base price attribute sorted by type C186892', () => {
    const field = 'type';
    const queryParams = `sort[${field}]=${sortingDirection[0]}`;

    serviceCategoryApi.categoryAttributes.getCategoryAttributes(createdItemTypeId, queryParams);
    cy.get('@getCategoryAttributes').then((response: any) => {
      categoryTreePage.checkItemTypeStructure(response);
      categoryTreePage.checkCategoryRelevanceAttribute(response);
      categoryTreePage.checkAttributesWereSortedAsc(response.body.items);
    });
  });

  it('[API] Create new unique Item Type for provided market C188121', () => {
    const filledCategoryParams = {
      taxValue,
      transactionFee,
    };

    const emptyCategoryParams = {
      transactionFee,
    };

    const data = itemTypeBuider(filledCategoryParams);

    serviceCategoryApi.category.createItemType(germanMarketCode, createdCategoryId, data);
    cy.get('@createItemType').then((response: any) => {
      categoryTreePage.checkRootCategoryHasId(response);

      const categoryId = response.body.id;

      // DE
      serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, categoryId);
      cy.get('@getCategoryDetailByMarket').then((response: any) => {
        categoryTreePage.checkCategoryParamsIsNotEmpty(response.body, filledCategoryParams);
      });

      // ES
      serviceCategoryApi.category.getCategoryDetailByMarket(availableMarkets[1], categoryId);
      cy.get('@getCategoryDetailByMarket').then((response: any) => {
        categoryTreePage.checkCategoryParamsIsEmpty(response.body, emptyCategoryParams);
      });
    })
  });

  it('[API] Create Item Type with empty "taxes" and "transactionFee" C188115', () => {
    const categoryParams = {
      taxValue: null,
      transactionFee: null,
    };

    const data = itemTypeBuider(categoryParams);

    serviceCategoryApi.category.createItemType(availableMarkets[0], createdCategoryId, data);
    cy.get('@createItemType').then((response: any) => {
      checkStatusCode(response, statusCodes.BAD_REQUEST);
    });
  });

  it('[API] Create Item Type with value > 100 for "taxes" and "transactionFee" C188127', () => {
    const categoryParams = {
      taxValue: invalidTaxValue,
      transactionFee: invalidTaxValue,
    };
    const data = itemTypeBuider(categoryParams);

    serviceCategoryApi.category.createItemType(germanMarketCode, createdCategoryId, data);
    cy.get('@createItemType').then((response: any) => {
      checkStatusCode(response, statusCodes.BAD_REQUEST);
    });
  });

  it('[API] Create Item Type with non-unique EN name C188130', () => {
    serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, createdItemTypeId);
    cy.get('@getCategoryDetailByMarket').then((response: any) => {
      const existingName = getTranslation(response.body.translations, 'en');
      const categoryParams = {
        taxValue,
        transactionFee,
        uniqueNameEn: existingName,
      };
      const data = itemTypeBuider(categoryParams);

      serviceCategoryApi.category.createItemType(germanMarketCode, createdCategoryId, data);
      cy.get('@createItemType').then((response: any) => {
        checkStatusCode(response, statusCodes.BAD_REQUEST);
      });
    });
  });

  it('[API] Update name, VAT, Transaction Fee and Reporting Category for Item Type C187943', () => {
    const randomNumber = getRandomNumber(count);
    const mainMarket = germanMarketCode;
    const secondMarket = spanishMarketCode;

    const categoryParams = {
      name: {
        'de': `unique_category_name_de_${randomNumber}`,
        'en': `unique_category_name_en_${randomNumber}`,
        'es': `unique_category_name_es_${randomNumber}`,
      },
      transactionFee: taxValue,
      taxesAmount: taxValue,
      metroReportingCategory: getRandomString(10)
    };

    const data = categoryDetailBuilder(categoryParams);

    serviceCategoryApi.category.updateCategoryDetail(mainMarket, createdItemTypeId, data);
    cy.get('@updateCategoryDetail').then((response: any) => {
      checkStatusCode(response);

      // Get item type by main market
      serviceCategoryApi.category.getCategoryDetailByMarket(mainMarket, createdItemTypeId);
      cy.get('@getCategoryDetailByMarket').then((response: any) => {
        categoryTreePage.checkItemTypeParamsByMarketIsValid(response.body, categoryParams, mainMarket);
      });

      // Get item type by second market
      serviceCategoryApi.category.getCategoryDetailByMarket(secondMarket, createdItemTypeId);
      cy.get('@getCategoryDetailByMarket').then((response: any) => {
        categoryTreePage.checkItemTypeParamsByMarketIsInvalid(response.body, categoryParams);
      });
    });
  });

  it('[API] Update item type with non-unique name for market C187950', () => {
    serviceCategoryApi.category.getRootCategories();
    cy.get('@getRootCategories').then((response: any) => {
      const firstRootCategory = response.body.items[0];
      const itemWithChild = categoryTreePage.getItemWithChild(response.body.items);

      getChildCategory(itemWithChild);

      cy.get('@firstChildCategory').then((child: any) => {

        serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, firstRootCategory.id);
        cy.get('@getCategoryDetailByMarket').then((response: any) => {
          const childId = child.id;

          if (childId) {
            const categoryParams = {
              name: {
                'en': getTranslation(response.body.translations, 'en'),
                'de': getTranslation(response.body.translations),
              },
              transactionFee: taxValue + 1,
              taxesAmount: taxValue + 1,
              metroReportingCategory: getRandomString(10)
            };

            const data = categoryDetailBuilder(categoryParams);

            serviceCategoryApi.category.updateCategoryDetail(germanMarketCode, childId, data);
            cy.get('@updateCategoryDetail').then((response: any) => {
              checkStatusCode(response, statusCodes.BAD_REQUEST);
            });
          }
        });
      });
    });
  });

  it('[API] Update VAT and Transaction Fee with value > max C187945', () => {
    const randomNumber = getRandomNumber(count);
    const categoryParams = {
      name: {
        'de': `unique_category_name_de_${randomNumber}`,
        'en': `unique_category_name_en_${randomNumber}`,
      },
      transactionFee: count,
      taxesAmount: count,
      metroReportingCategory: getRandomString(10)
    };

    const data = categoryDetailBuilder(categoryParams);

    serviceCategoryApi.category.updateCategoryDetail(germanMarketCode, createdItemTypeId, data);
    cy.get('@updateCategoryDetail').then((response: any) => {
      checkStatusCode(response, statusCodes.BAD_REQUEST);

      serviceCategoryApi.category.getCategoryDetailByMarket(germanMarketCode, createdItemTypeId);
      cy.get('@getCategoryDetailByMarket').then((response: any) => {
        categoryTreePage.checkItemTypeParamsByMarketIsInvalid(response.body, categoryParams);
      });
    });
  });
});
