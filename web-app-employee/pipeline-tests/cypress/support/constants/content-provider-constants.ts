import {ContentProviderFilter} from "./pim-enums";
import {contentProviderLocators} from "../locators/employee/content-provider-locators";

export const CONTENT_PROVIDER_MAX_RANK_ERROR                  = 'This value should be less than or equal to 900000.';
export const CONTENT_PROVIDER_MAX_RANK_ERROR_DE               = 'Dieser Wert sollte kleiner oder gleich 900000 sein.';
export const CONTENT_PROVIDER_MIN_RANK_ERROR                  = 'This value should be greater than or equal to 99.';
export const CONTENT_PROVIDER_MIN_RANK_ERROR_DE                = 'Dieser Wert sollte größer oder gleich 99 sein.';

export const cpFilterMapper: { [id: string]: string; } = {};
cpFilterMapper[ContentProviderFilter.ORGANIZATION_NAME]       = 'Org. name';
cpFilterMapper[ContentProviderFilter.ORGANIZATION_ID]         = 'Org. ID';
cpFilterMapper[ContentProviderFilter.SHOP_NAME]               = 'Shop name';
cpFilterMapper[ContentProviderFilter.SOURCE_NAME]             = 'Source name';
cpFilterMapper[ContentProviderFilter.SOURCE_ID]               = 'Source ID';
cpFilterMapper[ContentProviderFilter.MARKET]                  = 'Markets';

export const cpLocatorMapper: { [id: string]: string; } = {};
cpLocatorMapper[ContentProviderFilter.ORGANIZATION_NAME]      = contentProviderLocators.organizationNameLabel;
cpLocatorMapper[ContentProviderFilter.ORGANIZATION_ID]        = contentProviderLocators.organizationIdLabel;
cpLocatorMapper[ContentProviderFilter.SHOP_NAME]              = contentProviderLocators.shopNameLabel;
cpLocatorMapper[ContentProviderFilter.SOURCE_NAME]            = contentProviderLocators.sourceNameLabel;
cpLocatorMapper[ContentProviderFilter.SOURCE_ID]              = contentProviderLocators.sourceIdLabel;
cpLocatorMapper[ContentProviderFilter.MARKET]                 = contentProviderLocators.marketLabel;

