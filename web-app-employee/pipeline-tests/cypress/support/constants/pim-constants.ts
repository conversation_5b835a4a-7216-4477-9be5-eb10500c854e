import { FilterOptions } from "./pim-enums";

export const availableMarkets = ["DE", "ES", "FR", "IT", "NL", "PT"];
export const marketNames = {
  DE: "Germany",
  ES: "Spain",
  FR: "France",
  IT: "Italy",
  NL: "Netherlands",
  PT: "Portugal",
};
export const germanMarketCode = "DE";
export const spanishMarketCode = "ES";
export const frenchMarketCode = "FR";
export const invalidMarket = "XX";
export const invalidId = "ABC";
export const acceptLanguages = ["de", "en", "es"];
export const count = 100000;
export const taxValue = 19;
export const invalidTaxValue = 101;
export const relevance = [0, 1, 2, 3];
export const sortingDirection = ["ASC", "DESC"];
export const limit = 10;
export const transactionFee = 0;
export const WAITING_TIME_TO_PROCESS_UPLOAD = 8000;
export const IN_REVIEW_FILTERS = [
  FilterOptions.FILE_NAME_FILTER,
  FilterOptions.CP_NAME_SELLER_ID_FILTER,
  FilterOptions.CP_TYPE_FILTER,
  FilterOptions.API_SP_FILTER,
  FilterOptions.UPLOAD_DATE_FILTER,
  FilterOptions.PENDING_MARKETS_FILTER,
];
export const REVIEWED_FILTERS = [FilterOptions.REVIEW_TYPE_FILTER];

export const nonAssignedLabel = "Non-Assigned";

export const MAX_AUTOREFRESH_ATTEMPTS = 15;

export const categoryName: string = "Tumblers";

export const waitingTimeToProcessStatusUpdate = 4000;
