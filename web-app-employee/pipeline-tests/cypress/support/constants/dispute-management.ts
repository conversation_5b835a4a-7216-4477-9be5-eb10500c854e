export const waitingForSellerResponseFilter = 'f.status=WAITING_FOR_SELLER_RESPONSE';
export const statusFilter = 'f.status';
export const orderNumberFilter = 'f.orderNumber';
export const ordersPageURL = 'sales-orders/orders';
export const orderNumberFilterLabel = 'Order Number';
export const statusFilterLabel = 'Status';
export const statusCaseClosed = 'Case Closed';
export const statusWaitingForBuyerResponse = `Waiting for buyer's response`;
export const orderNumber = 'O22-539181904279';
export const disputeDetailsDisputeId = 'PP-R-LZY-10038688';
export const viewDetails = 'View Details';
export const DEBOUNCE_TIME_DISPUTE_DETAILS = 3000;
export const statusResolved = 'RESOLVED';
export const offerId = 'd6a454c2-1ede-401b-a33d-0094f2f237ee';
export const escalateMessage = 'Escalating this dispute';
export const minimumNumberOfInterception = 2;
export const respondMessage = 'Respond message from the seller';

export enum DisputeStages {
  INQUIRY = 'INQUIRY',
  CHARGEBACK = 'CHARGEBACK',
  PRE_ARBITRATION = 'PRE_ARBITRATION',
  ARBITRATION = 'ARBITRATION',
}
export enum DisputeStatus {
  OPEN = 'OPEN',
  RESOLVED = 'RESOLVED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  WAITING_FOR_SELLER_RESPONSE = "WAITING_FOR_SELLER_RESPONSE",
  WAITING_FOR_BUYER_RESPONSE = "WAITING_FOR_BUYER_RESPONSE",
  OTHER = 'OTHER',
}

export const disputeDetailsKeys = [
  'buyer',
  'caseDueDate',
  'caseId',
  'disputedAmount',
  'invoiceId',
  'items',
  'messages',
  'orderId',
  'orderNumber',
  'reportedDate',
  'seller',
  'stage',
  'status',
  'transactionAmount',
  'transactionId',
];

export const disputeItemKeys = [
  'description',
  'issue',
  'name',
  'quantity',
];

export const disputeMessageKeys = [
  'content',
  'documents',
  'initials',
  'postedBy',
  'timePosted',
];
