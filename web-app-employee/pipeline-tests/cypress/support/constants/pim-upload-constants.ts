import { UploadStatusFilter } from "./pim-enums";

export const uploadStatusTabMapper: { [id: string]: string } = {};
uploadStatusTabMapper[UploadStatusFilter.IN_REVIEW] = "mat-tab-label-0-0";
uploadStatusTabMapper[UploadStatusFilter.APPROVED] = "mat-tab-label-0-1";
uploadStatusTabMapper[UploadStatusFilter.REJECTED] = "mat-tab-label-0-2";
uploadStatusTabMapper[UploadStatusFilter.AUTO] = "mat-tab-label-0-3";
uploadStatusTabMapper[UploadStatusFilter.REVIEWED] = "mat-tab-label-0-1";

export const UPLOAD_PARSING_STATISTIC_ENDPOINT_TIMEOUT = 20000;
export const PRODUCT_UPLOAD_ENDPOINT_TIMEOUT = 30000;
export const REJECT_FILE_REASON_MESSAGE = "CYPRESS TEST: reject upload file";
export const rejectRowReasonMessage = "CYPRESS TEST: reject row";
export const rejectRowDraftReasonMessage = "CYPRESS TEST: draft for row";

export const rejectRowReasonMessages = [
  rejectRowDraftReasonMessage,
  rejectRowReasonMessage,
];
export const HEADER_LIST = [
  "Product category",
  "GTIN",
  "Product name",
  "Short description",
  "Description",
  "Key feature",
  "Brand",
];
