export enum translatableValues {
  TRANSLATION_REQUIRED = "Translations Required",
  NO_TRANSLATION_REQUIRED = "No Translations Required",
}

export const LOV_SUFFIXES = {
  GROUP: "group",
  VALUE: "value",
  UPDATED: "updated",
  TRANSFORMATION: "transformation",
};

export enum Languages {
  EN = "en",
  DE = "de",
  ES = "es",
  IT = "it",
  PT = "pt",
  NL = "nl",
  FR = "fr",
}

export const SEARCH_RESULT_MESSAGE = (value: string) =>
  `1 result for: ${value}`;

export const LOV_NAME_TEST = "test_lov_";

export const LOV_NUMBER_OF_TRANSFORMATIONS = 3;

export const FILTER_KEY_NAME = "name";
