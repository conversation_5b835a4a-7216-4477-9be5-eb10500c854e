export const createdByFilterLabel = 'Created By';
export const discountCodeFilterLabel = 'Discount Code';
export const salesChannelFilterLabel = 'Sales Channel';
export const reasonFilterLabel = 'Reason';
export const statusFilterLabel = 'Status';
export const orderNumberFilterLabel = 'Order Number';
export const createdBy = '<EMAIL>';
export const createdByFilterQueryParam = 'f.createdBy';
export const statusFilterQueryParam = 'f.status';
export const codeFilterQueryParam = 'f.code';
export const salesChannelFilterQueryParam = 'f.countries';
export const reasonFilterQueryParam = 'f.purpose';
export const orderNumberFilterQueryParam = 'orderNumber';
export const discountCode = 'ABCDEF4';
export const salesChannel = 'metro.de';
export const deSalesChannel = 'DE';
export const compensationReason = 'Compensation';
export const promotionReason = 'Promotion';
export const expiredStatus = 'Expired';
export const DISCOUNT_CODE_PAGE_LOAD_TIME = 5000;
export const redemptionFilterOrderNumber = 'O20-65374699';
export const statusDeactivated = 'Deactivated';
let startDate = '';
let endDate = '';

export enum FilterType {
  INPUT = 'input',
  DROPDOWN = 'dropdown',
  DATE = 'date',
}

export enum DateFilterType {
  start = 'START',
  end = 'END',
}

export const discountCodeListKeys = [
  "id",
  "code",
  "prefix",
  "status",
  "country",
  "type",
  "amount",
  "minSpendingAmount",
  "maxDiscountAmount",
  "sellerId",
  "deliveryType",
  "maxUsage",
  "usagePerCustomer",
  "startTime",
  "endTime",
  "purpose",
  "reason",
  "reasonDetails",
  "createdAt",
  "createdBy",
  "createdByEmail",
  "categories",
  "codeGroup",
  "allowPromo",
  "numberOfCodes",
  "brands",
  "brandAction",
];

export function getDateFilterString(type) {
  const fromDate = new Date();
  fromDate.setDate(fromDate.getDate() - 1);
  const toDate = new Date();
  let fromFilter = 'fromStartTime';
  let toFilter = 'toStartTime';
  if (type === DateFilterType.start) {
    toDate.setDate(fromDate.getDate() + 4);
  } else {
    toDate.setDate(toDate.getDate() + 20);
    fromFilter = 'fromEndTime';
    toFilter = 'toEndTime';
  }
  setStartAndEndDates(`${fromDate.getMonth() + 1}.${fromDate.getDate()}.${fromDate.getFullYear()}`,
    `${toDate.getMonth() + 1}.${toDate.getDate()}.${toDate.getFullYear()}`);
  return `?offset=0&limit=10&f.${fromFilter}=${fromDate.getDate()}.${fromDate.getMonth() + 1}.${fromDate.getFullYear()}&f.${toFilter}=${toDate.getDate()}.${toDate.getMonth() + 1}.${toDate.getFullYear()}`;
}

export function getStartAndEndDates() {
  return {startDate, endDate};
}

export function setStartAndEndDates(start, end){
  startDate = start;
  endDate = end;
}

export const redemptionListKeys = [
  "discountCode",
  "orderId",
  "orderNumber",
  "customerId",
  "redeemedAt",
]

export const discountCodeKeys = [
  "id",
  "code",
]

export const discountCodeDetailsKeys = [
  "id",
  "code",
  "prefix",
  "status",
  "country",
  "type",
  "amount",
  "minSpendingAmount",
  "maxDiscountAmount",
  "sellerId",
  "deliveryType",
  "maxUsage",
  "usagePerCustomer",
  "startTime",
  "endTime",
  "purpose",
  "reason",
  "reasonDetails",
  "createdAt",
  "createdBy",
  "createdByEmail",
  "categories",
  "codeGroup",
  "allowPromo",
  "numberOfCodes",
  "brands",
  "brandAction"
];