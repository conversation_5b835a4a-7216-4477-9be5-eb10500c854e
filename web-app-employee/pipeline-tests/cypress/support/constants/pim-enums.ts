export enum UploadStatus {
  AUTO_PROCESS = "auto_process",
  APPROVED = "approved",
  UPLOADED = "uploaded",
  IN_REVIEW = "in_review",
  REVIEW_REJECTED = "review_rejected",
  PROCESSING = "processing",
  REPORT_GENERATING = "report_generation",
  SUCCESS = "success",
  WITH_ERRORS = "with_errors",
}

export enum UploadRulesStatus {
  RULES_PROCESSING = 'rules_processing',
  READY_FOR_MANUAL_REVIEW = 'ready_for_manual_review',
}

export enum ParseFileStatus {
  PARSING_COMPLETED = "parse_completed",
  PARSING_IN_PROGRESS = "parse_in_process",
  PARSING_ERROR = "parse_error",
  PARSING_READY = "parse_ready",
}

export enum RowStatus {
  PENDING = "pending",
  REJECTED = "rejected",
  APPROVED = "approved",
}

export enum FileStatus {
  IN_REVIEW = "in_review",
  REJECTED = "rejected",
  APPROVED = "approved",
}

export enum UploadStatusFilter {
  APPROVED = "approved",
  IN_REVIEW = "in_review",
  REJECTED = "rejected",
  AUTO = "auto",
  REVIEWED = "reviewed",
}

export enum AttributeType {
  UNKNOWN,
  LOV,
  BOOLEAN,
  TEXT,
  INTEGER,
  FILE,
  DECIMAL,
  IMAGE,
  LONGTEXT,
}

export enum ContentProviderFilter {
  ORGANIZATION_NAME = "organizationName",
  ORGANIZATION_ID = "organizationId",
  SHOP_NAME = "shopName",
  SOURCE_NAME = "sourceName",
  SOURCE_ID = "sourceId",
  MARKET = "market",
}

export enum ProcessingStatus {
  COMPLETED = "Completed",
  WITH_ERRORS = "Completed with errors",
}

export enum FilterOptions {
  FILE_NAME_FILTER = "File Name",
  CP_NAME_SELLER_ID_FILTER = "CP Name / Seller ID",
  CP_TYPE_FILTER = "CP Type",
  API_SP_FILTER = "API / SP",
  UPLOAD_DATE_FILTER = "Upload Date",
  PENDING_MARKETS_FILTER = "Pending Markets",
  CP_MARKETS_FILTER = "CP Markets",
  REVIEW_TYPE_FILTER = "Review Type",
}

export enum FilterValues {
  SELLER_VALUE = "Seller",
  GERMAN_VALUE = "German",
}
