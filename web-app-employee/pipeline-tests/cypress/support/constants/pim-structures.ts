import {AttributeType} from "./pim-enums";

export const attributeTypes = {
  [AttributeType.LOV]: {
    type: {
      id: 1,
      label: "List of value",
    },
    lov: {},
    unit: null,
    translations: [],
  },
  [AttributeType.BOOLEAN]: {
    type: {
      id: 2,
      label: "Boolean",
    },
    lov: null,
    unit: null,
    condition: { unique: false, filterable: false, multiple: false }
  },
  [AttributeType.TEXT]: {
    type: {
      id: 3,
      label: "Text",
    },
    lov: null,
    unit: null,
    example: ''
  },
  [AttributeType.INTEGER]: {
    type: {
      id: 4,
      label: "Integer",
    },
    lov: null,
    unit: null,
  },
  [AttributeType.FILE]: {
    type: {
      id: 5,
      label: "File",
    },
    lov: null,
    unit: null,
  },
  [AttributeType.DECIMAL]: {
    type: {
      id: 6,
      label: "Decimal",
    },
    lov: null,
    unit: {
      baseUnit: "unit",
      csvPredefined: false
    },
  },
  [AttributeType.IMAGE]: {
    type: {
      id: 7,
      label: "Image",
    },
    lov: null,
    unit: null,
    condition: { unique: false, filterable: false, multiple: false },
  },
  [AttributeType.LONGTEXT]: {
    type: {
      id: 8,
      label: "Long text",
    },
    lov: null,
    unit: null,
    example: ''
  },
};
