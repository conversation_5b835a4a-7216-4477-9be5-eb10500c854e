// Buyer UI
export const CHECKOUT_ADDRESS = 'marktplatz/checkout/order/address';
export const API_BUYER_LOGIN_URL = `https://app-buyer-account.${Cypress.env('API_DOMAIN')}/api/v1/account-info`;
export const API_IDAM_TOKEN_BUYER_URL = `https://${Cypress.env('IDAM_DOMAIN')}/authorize/api/oauth2/access_token`;
export const API_CHECKOUT_BASE_URL = `https://app-checkout.${Cypress.env('domain')}`;
export const API_CHECKOUT_PAYMENT_URL = `api/v2/checkout/make-payment`;
export const API_CART_URL = `${API_CHECKOUT_BASE_URL}/api/v1/cart/`;

// Seller UI
export const API_SELLER_ORDERS_URL = `https://app-order-management.${Cypress.env('domain')}/api/v2/order-management/seller-office/orders`;
export const API_SELLER_LOGIN_URL = `https://service-ims-v2.${Cypress.env('domain')}/accounts/auth/login`;
export const SELLER_ORDERS_URL = `workplace/orders`;

// Employee UI
export const EMPLOYEE_BASE_URL = `https://${Cypress.env('employeeUrl')}.${Cypress.env('domain')}`;
export const EMPLOYEE_PAYMENTS_URL = `payments/payments`;
export const API_IDAM_TOKEN_EMPLOYEE_URL = `https://idam-pp.metrosystems.net/authorize/api/oauth2/access_token`;
export const API_SERVICE_PAYMENT_TRANSACTIONS_URL = `https://service-payment.${Cypress.env('domain')}/api/v2/transactions`;
export const DISPUTE_MANAGEMENT_URL = 'disputes-management/disputes';
export const DISPUTE_MANAGEMENT_URL_WITH_FILTER = 'disputes-management/disputes?limit=10&offset=0&f.status=WAITING_FOR_SELLER_RESPONSE';
export const DISCOUNT_CODE_OVERVIEW_URL = 'discount/overview';
export const DISCOUNT_CODE_REDEMPTION_URL = 'discount/redemption';

 // Url constants
export const MARKETPLACE_URL = 'marktplatz';
export const SELLER_INVOICES = 'accounting/invoices';
