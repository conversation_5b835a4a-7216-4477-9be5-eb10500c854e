export const EXPORTED_FILE_NAME = './cypress/exports/';
export const ORDER_NUMBER = 'orderNumber';
export const PAYMENT_METHOD = 'paymentMethod';
export const PAYMENT_DATE = 'paymentDate';
export const BILLING_ADDRESS = 'billingAddress';
export const DELIVERY_ADDRESS = 'deliveryAddress';
export const VALUE_ADDED_TAXES = 'valueAddedTaxes';
export const SHIIPING_AMOUNT = 'shippingAmount';
export const TOTAL_SUM = 'totalSum';
export const PRODUCT_NAME = 'productName';
export const PRODUCT_QUANTITY = 'productQuantity';
export const SPACES_REGEX_REMOVER = /\s/g;
export const OPEN_INVOICE = 'Ratepay Open invoice';
export const DIRECT_DEBIT = 'Ratepay Lastschrift';
export const CREDIT_CARD = 'Kreditkarte';
export const THANK_YOU_ROUTE = '/thank-you';
export const PP_ENV_BUYER = 'pp.de-buyer';
export const PROD_ENV_BUYER = 'prod.de-buyer';
export const LOCAL_ENV = 'local';
export const BUYER_APP_PP_URL = 'https://marketplace-pp.metro.de';
export const BUYER_APP_PROD_URL = 'https://www.metro.de';
export const LOCAL_URL = 'localhost:80';
export const PP_ENV_SELLER = 'pp.de-seller';
export const PROD_ENV_SELLER = 'prod.de-seller';
export const PP_ENV_EMPLOYEE = 'pp.de-employee';
export const PROD_ENV_EMPLOYEE = 'prod.de-employee';
export const SELLER_APP_PP_URL = 'https://web-app-seller.pp-de.metro-marketplace.cloud';
export const SELLER_APP_PROD_URL = 'https://www.metro-selleroffice.com';
export const EMPLOYEE_APP_PP_URL = 'https://web-app-employee.pp-de.metro-marketplace.cloud';
export const EMPLOYEE_APP_PROD_URL = 'https://backoffice.de.metro-marketplace.cloud';
export const BUYER_DOMAIN = 'buyer';
export const SELLER_DOMAIN = 'seller';
export const EMPLOYEE_DOMAIN = 'employee';
export const CONFIRMED_ORDER = 'bestätigt';
export const SHIPPED_ORDER = 'versendet';
export const TOKEN_ID = 'id_token';
export const ACCESS_TOKEN = 'access_token';
export const IMS_DOMAIN = 'ims';
export const PP_ENV_IMS = 'pp.de-ims';
export const IMS_PP_URL = 'https://service-ims-v2.pp-de.metro-marketplace.cloud';
export const POST_HTTP = 'POST';
export const PARTIAL_REFUND = 'partial';
export const AUTOMATICALLY_TRIGERRED = 'automatically';
export const RETURN = 'return';
export const FULL_REFUND = 'full';
export const REQUESTED = 'Requested';
export const PAYPAL = 'PayPal';
