import { paymentWidgetLocators } from "../locators/buyer/checkout/payment-widgets-locators";


const testingConfig = {
  orders: [
    {
      description: 'Mac Book Pro',
      sellerName: 'Converse Shop',
      paymentMethod: 'Ratepay Lastschrift',
      offerId: 'f04c9387-b7f4-4a62-be7f-d0152765b058',
      paymentAssertions: {
        isThere: [paymentWidgetLocators.directDebitWidgetSelector,
        paymentWidgetLocators.ratepayWidgetSelector, paymentWidgetLocators.creditCardWidgetSelector],
        notThere: [paymentWidgetLocators.paypalWidgetSelector],
      },
      trackingNumber: '1234',
      refundInfo: {
        type: 'full',
        refundBy: 'quantity',
        quantity: '1',
        freshdeskTicketNumber: '1234',
        notes: 'Autotest',
      },
      debitCardDetails: {
        iban: '**********************',
      },
    },
    {
      description: 'Mac Book Pro',
      sellerName: 'Converse Shop',
      paymentMethod: 'Ratepay Open invoice',
      offerId: 'f04c9387-b7f4-4a62-be7f-d0152765b058',
      paymentAssertions: {
        isThere: [paymentWidgetLocators.directDebitWidgetSelector,
        paymentWidgetLocators.ratepayWidgetSelector, paymentWidgetLocators.creditCardWidgetSelector],
        notThere: [],
      },
      trackingNumber: '1234',
      refundInfo: {
        type: 'full',
        refundBy: 'quantity',
        quantity: '1',
        freshdeskTicketNumber: '1234',
        notes: 'Autotest',
      },
      debitCardDetails: {
        iban: '',
      },
    },
  ]
};

export default testingConfig;
