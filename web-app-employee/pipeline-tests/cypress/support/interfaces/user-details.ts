export interface UserDetails {
  login: {
    buyer: {
      email: string;
      password: string;
    };
    seller: {
      email: string;
      password: string;
    };
    employee: {
      username: string;
      password: string;
    };
  };
  orders: OrderDetails[];
  loginAssertions: {
    loginUser: string;
  };
  debitCardDetails: {
    iban: string;
  };
}

export interface OrderDetails {
  description: string;
  sellerName: string;
  paymentMethod: string;
  paymentAssertions: PaymentAssertions;
  offerId: string;
  debitCardDetails: DebitCardDetails;
  trackingNumber: string;
  threeDs2CreditCardDetails?: ThreeDsDetails;
  threeDs1CreditCardDetails?: ThreeDsDetails;
  refundInfo: {
    type: string;
    refundBy: string;
    quantity: string;
    freshdeskTicketNumber: string;
    notes: string;
  };
}

interface ThreeDsDetails {
  creditCardNumber: string;
  validUntil: string;
  cvcCode: string;
  usernameAuthentication: string;
  passwordAuthentication: string;
}

export interface PaymentAssertions {
  isThere: string[];
  notThere: string[];
}

export interface DebitCardDetails {
  iban: string
}
