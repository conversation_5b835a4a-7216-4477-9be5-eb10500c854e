
export interface DisputeDetails {
  disputeId: string;
  orderId?: string;
  orderNumber: string;
  disputedAmount: Money;
  caseFilingDate: string;
  caseDueDate: string;
  invoiceId: string;
  transactionId: string;
  transactionAmount: Money;
  items?: DisputeItem[];
  messages?: DisputeMessage[]
  buyerName: string;
  sellerName: string;
  stage?: string;
  status: string;
}

export interface Money {
  amount: string;
  currency: string;
}


export interface DisputeItem {
  name: string;
  description: string;
  quantity: number;
  issue: string;
}

export interface DisputeMessage {
  content: string;
  postedBy: string;
  timePosted: string;
  initials: string;
  documents?: Document[];
}

export interface Document {
  name: string;
  url: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  totalCount: number;
}

export interface Dispute {
  disputeId: string;
  orderId: string;
  orderNumber: string;
  reason: string;
  status: string;
  disputedAmount: Money;
  caseFilingDate: string;
  caseDueDate: string;
}

