import { ITranslation } from './translation-model';

class Attribute {
  code: string;
  condition: AttributeCondition;
  csvHeaderLabel: string;
  frontend: string = "2";
  isLocalizable: boolean;
  lovId: string = null;
  translations: ITranslation[];
  typeId: number;
  unit: AttributeUnit;

  constructor(
    code: string,
    condition: AttributeCondition,
    isLocalizable: boolean,
    translations: ITranslation[],
    typeId: number,
    unit: AttributeUnit,
    frontend: string,
    lovId: string
  ) {
    this.code = code,
    this.condition = condition,
    this.csvHeaderLabel = code,
    this.isLocalizable = isLocalizable,
    this.translations = translations,
    this.typeId = typeId,
    this.unit = unit,
    this.frontend = frontend,
    this.lovId = lovId
  }
}

interface AttributeCondition {
  unique: boolean;
  filterable: boolean;
  multiple: boolean;
}

interface AttributeUnit {
    id: string;
    csvPredefined: boolean;
}

interface AttributeTypeDetails {
    type: number;
    id: string;
}

interface AttributeDetails {
  type: number,
  unit?: AttributeUnit,
  lovId?: string,
  frontEndFlag?: string,
  translationFirstLang?: string,
  name?: string;
}

export {
  Attribute,
  AttributeCondition,
  AttributeUnit,
  AttributeTypeDetails,
  AttributeDetails,
}
