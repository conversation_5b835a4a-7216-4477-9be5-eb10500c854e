//Categories
const categoryListKeys: string[] = [
  "id",
  "name",
  "hasChild",
  "createdAt",
  "updatedAt",
  "isActive",
];

//Unit groups
const unitGroupsKeys: string[] = [
  "baseUnit",
  "displayUnit",
  "id",
  "name",
  "possibleUnits",
];

//Lovs
const lovListKeys: any = {
  lov: ["id", "name", "createdAt", "updatedAt", "options", "translatable"],
  options: ["id", "value", "hasMapping"],
};

const lovDetailsKeys: any = {
  lovDetails: [...lovListKeys.lov, "translations"],
  options: ["id", "name", "mappings", "translations", "isUsed"],
  translations: ["lang", "properties"],
  properties: ["name"],
  mappings: ["id", "name"],
};

//Brands
const brandKeys: string[] = [
  "id",
  "name",
  "mappings",
  "createdAt",
  "horecaRelevant",
  "updatedAt",
];
const attributesKeys: any = {
  attribute: [
    "id",
    "code",
    "name",
    "description",
    "csvHeaderLabel",
    "type",
    "possibleUnits",
    "possibleValues",
  ],
  type: ["id", "label"],
};

const lovKeys: string[] = [
  "id",
  "name",
  "options",
  "createdAt",
  "updatedAt",
  "translatable",
];

export {
  categoryListKeys,
  unitGroupsKeys,
  lovListKeys,
  lovDetailsKeys,
  brandKeys,
  attributesKeys,
  lovKeys,
};
