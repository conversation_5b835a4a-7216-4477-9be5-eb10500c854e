export const apiEndpoints = {
  markets: "/admin/v1/markets",
  categories: "/admin/v1/categories",
  category: (id: string) => `/admin/v1/categories/${id}`,
  categoryRootByMarket: (market: string) =>
    `/admin/v1/${market}/categories/root`,
  categoriesByMarket: (market: string) => `/admin/v1/${market}/categories`,
  childCategory: (id: string) => `/admin/v1/categories/${id}/child`,
  categoryDetails: (market: string, id: string) =>
    `/admin/v1/${market}/categories/item-type/${id}`,
  categorySearchByName: (market: string) =>
    `/admin/v1/${market}/categories/search`,
  createCategoryItemTypeForMarket: (market: string, id: string) =>
    `/admin/v1/${market}/categories/${id}/item-type`,
  categoryStateByMarket: (market: string, id: string) =>
    `/admin/v1/${market}/categories/${id}/state`,
  categoryDetailByMarket: (market: string, id: string) =>
    `/admin/v1/${market}/categories/${id}`,
  unassignedCategoryAttributes: (id: string) =>
    `/admin/v1/categories/${id}/attributes/unassigned`,
  unassignCategoryAttributes: (id: string) =>
    `/admin/v1/categories/${id}/attributes/unassign`,
  categoryAttribute: (categoryId: string, attributeId: string) =>
    `/admin/v1/categories/${categoryId}/attributes/${attributeId}`,
  categoryAssignedAttributes: (market: string, id: string) =>
    `/admin/v1/${market}/categories/${id}/attributes/extract`,
  categoryBasePrise: (id: string) => `/admin/v1/categories/${id}/base-price`,
  categoryAttributes: (categoryId: string) =>
    `/admin/v1/categories/${categoryId}/attributes`,
  measureUnits: "/admin/v1/measure-units",
  lovsByMarket: (market: string) => `/admin/v1/${market}/lov`,
  lovDetails: (id: string) => `/admin/v1/lov/${id}`,
  lov: "/admin/v1/lov",
  brands: "/admin/v1/brands",
  brandDetails: (id: string) => `/admin/v1/brands/${id}`,
  brand: (id: string) => `/admin/v1/brands/${id}`,
  brandLogo: (id: string) => `/admin/v1/brands/${id}/logo`,
  attributes: (market: string) => `/admin/v1/${market}/attributes`,
  attributesById: (id: string) => `/admin/v1/attributes/${id}`,
  attributesByMarket: (market: string, id: string) =>
    `/admin/v1/${market}/attributes/${id}`,
  uploadFile: "/api/v1/uploads/",
  organizations: "/admin/v1/uploads/organizations",
  productUploads: "/admin/v1/uploads/files",
  assignUploadMarket: (market: string, id: string) =>
    `/admin/v1/uploads/${id}/market/${market}`,
  productList: "/admin/v1/products",
  productInfo: "/admin/v1/products/info",
  product: (market: string, id: string) => `/admin/v1/${market}/products/${id}`,
  reviewStatus: (id: string) =>
    `/admin/v1/organizations/${id}/settings/review-status`,
  downloadReport: (id: string) => `/admin/v1/uploads/${id}/download/report`,
  contentProviderList: "/admin/v1/content-providers",
  contentProvider: (id: string) => `/admin/v1/content-providers/${id}`,
  contentProviderRank: (id: string) =>
    `/admin/v1/content-providers/${id}/weight`,
  contentProviderNote: (id: string) => `/admin/v1/content-providers/${id}/note`,
  contentProviderHistoryRank: (id: string) =>
    `/admin/v1/content-providers/${id}/rank`,
  downloadContentProviderList: "/admin/v1/content-providers/export",
  __startUploadParsing: `admin/v1/uploads/*/parse-start`,
  startUploadParsing: `admin/v1/uploads/*/start`,
  __getUploadParsingStatistics: `admin/v1/uploads/*/parse-stats`,
  getUploadParsingStatistics: `admin/v1/uploads/*/statistics`,
  getRawUploadHeaderList: `admin/v1/uploads/*/headers`,
  approveRows: (market: string) => `admin/v1/${market}/uploads/*/rows/approve`,
  rejectRows: (market: string) => `admin/v1/${market}/uploads/*/rows/reject`,
  draftRows: (market: string) =>
    `admin/v1/${market}/uploads/*/rows/reject/draft`,
  getUploadReviewRows: (market: string) =>
    `admin/v1/${market}/uploads/*/rows/all`,
  approveFile: (market: string) => `/approve`,
  rejectFile: (market: string) => `admin/v1/${market}/uploads/*/reject`,
  uploadStats: `/admin/v1/uploads/*/stats*`,
};
