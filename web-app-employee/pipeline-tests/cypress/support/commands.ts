import "cypress-localstorage-commands";
import { appSellerPimApi } from "./api/app-seller-pim-api";
import {
  BUYER_TOKEN_KEY,
  CMS_TOKEN_KEY,
  EMPLOYEE_TOKEN_KEY,
  SELLER_TOKEN_KEY,
} from "./constants/local-storage-constants";
import { landingPage } from "./pages/employee/landing-page";
import { productDataUploadsPage } from "./pages/employee/product-data-uploads-page";
import { manageProductsPage } from "./pages/seller/inventory/manage-products";
import { UploadStatusFilter } from "./constants/pim-enums";
import { productUploadLocators } from "./locators/employee/product-upload-locators";
import { apiEndpoints } from "./endpoints";

let localBuyerCache = null;
let localSellerCache = null;
let localEmployeeCache = null;
let localCMSCache = null;
const XLSX = require("xlsx");

declare global {
  namespace Cypress {
    interface Chainable<Subject> {
      clearToken(): void;
    }

    interface Chainable<Subject> {
      getByTestId(id: string): Chainable;
    }

    interface Chainable<Subject> {
      acceptDataPrivacyPopup(): Chainable;
    }

    interface Chainable<Subject> {
      getIframe(iframe: string): Chainable;
    }

    interface Chainable<Subject> {
      cacheToken(): void;
    }

    interface Chainable<Subject> {
      injectToken(): void;
    }

    interface Chainable<Subject> {
      uploadFile(
        fileUrl: string,
        orgId: string,
        uploadStatusFilter: UploadStatusFilter
      ): Chainable;
    }

    interface Chainable<Subject> {
      getUpload(uploadedFileId: string, uploadStatusFilter: string): Chainable;
    }

    interface Chainable<Subject> {
      getFilteredUpload(
        uploadedFileId: string,
        uploadStatus: UploadStatusFilter,
        timeOut?: number
      ): Chainable;
    }

    interface Chainable<Subject> {
      getProductFromFile(fileUrl: string): Chainable;
    }

    interface Chainable<Subject> {
      downloadReport(uploadedFileId: string): Chainable;
    }

    interface Chainable<Subject> {
      searchProductInDB(productSearchId: string): Chainable;
    }

    interface Chainable {
      readExcel(filePath: string): Chainable<any[][]>;
    }
  }
}

Cypress.Commands.add("clearToken", () => {
  localStorage.removeItem(BUYER_TOKEN_KEY);
});

Cypress.Commands.add("getByTestId", (testId) =>
  cy.get(`[test-target="${testId}"]`)
);

Cypress.Commands.add("acceptDataPrivacyPopup", () => {
  cy.get('[test-target="cookie-modal-accept"]').click();
});

Cypress.Commands.add("getIframe", (iframe) => {
  return cy
    .get(iframe, { timeout: 20000 })
    .its("0.contentDocument.body")
    .should("not.be.empty")
    .then(cy.wrap);
});

Cypress.Commands.add("cacheToken", () => {
  cy.getLocalStorage(BUYER_TOKEN_KEY).then((buyerToken) => {
    localBuyerCache = buyerToken;
  });

  cy.getLocalStorage(SELLER_TOKEN_KEY).then((sellerToken) => {
    localSellerCache = sellerToken;
  });

  cy.getLocalStorage(EMPLOYEE_TOKEN_KEY).then((employeeToken) => {
    localEmployeeCache = employeeToken;
  });

  cy.getLocalStorage(CMS_TOKEN_KEY).then((cmsToken) => {
    localCMSCache = cmsToken;
  });
});

Cypress.Commands.add("clearToken", () => {
  cy.removeLocalStorage(BUYER_TOKEN_KEY);
  cy.removeLocalStorage(SELLER_TOKEN_KEY);
  cy.removeLocalStorage(EMPLOYEE_TOKEN_KEY);
  cy.removeLocalStorage(CMS_TOKEN_KEY);
});

Cypress.Commands.add("injectToken", () => {
  if (localBuyerCache) {
    localStorage.setItem(BUYER_TOKEN_KEY, localBuyerCache);
  }
  if (localSellerCache) {
    localStorage.setItem(SELLER_TOKEN_KEY, localSellerCache);
  }
  if (localEmployeeCache) {
    localStorage.setItem(EMPLOYEE_TOKEN_KEY, localEmployeeCache);
  }
  if (localCMSCache) {
    localStorage.setItem(CMS_TOKEN_KEY, localCMSCache);
  }
});

Cypress.Commands.add("uploadFile", (fileUrl, orgId, uploadStatusFilter) => {
  appSellerPimApi.setAutoReview(
    orgId,
    uploadStatusFilter === UploadStatusFilter.AUTO
  );
  appSellerPimApi.uploadFile(fileUrl);
  cy.get("@uploadFile").then((response: any) => {
    return manageProductsPage.checkFileUploadId(response);
  });
});

Cypress.Commands.add(
  "getFilteredUpload",
  (uploadedFileId, uploadStatus, customTimeout = 5000) => {
    const alias = `@getFilteredUpload${uploadStatus}`;
    cy.wait(alias, { timeout: customTimeout }).then((interception) => {
      const sellerFiles = interception.response.body.items;
      return sellerFiles.find((file) => file.uploadId === uploadedFileId);
    });
  }
);

Cypress.Commands.add("getProductFromFile", (fileUrl) => {
  cy.fixture(fileUrl, "binary").then((csv) => {
    return productDataUploadsPage.convertCSVToJSON(csv)[0];
  });
});

Cypress.Commands.add("downloadReport", (uploadedFileId) => {
  appSellerPimApi.getFileReport(uploadedFileId);
  cy.get("@getFileReport").then((response: any) => {
    const reportData = response.body;
    return productDataUploadsPage.convertCSVToJSON(reportData)[0];
  });
});

Cypress.Commands.add("searchProductInDB", (searchId) => {
  appSellerPimApi.getProductInfo(searchId);

  cy.get("@getProductInfo").then((response: any) => {
    const productId = response.body.id;

    appSellerPimApi.getProduct(productId);
    cy.get("@getProduct").then((response: any) => {
      return response.body;
    });
  });
});

Cypress.Commands.add(
  "getUpload",
  (uploadedFileId: string, uploadStatusFilter: string) => {
    const currentFilter = `${encodeURI(
      "filter[uploadStatus]"
    )}=${uploadStatusFilter}`;
    cy.intercept(
      "GET",
      `**${apiEndpoints.productUploads}*${currentFilter}*`
    ).as("getProductUploads");

    landingPage.goToProductUploadsV2();

    if (uploadStatusFilter === UploadStatusFilter.REVIEWED) {
      cy.get(productUploadLocators.reviewedTab).click({ force: true });
    }

    cy.wait("@getProductUploads").then((interception) => {
      const sellerFiles = interception.response.body.items;
      return sellerFiles.find((file) => file.uploadId === uploadedFileId);
    });
  }
);

Cypress.Commands.add(
  "readExcel",
  (filePath: string): Cypress.Chainable<any[][]> => {
    return cy.readFile(filePath, "binary").then((fileContent) => {
      const workbook = XLSX.read(fileContent, { type: "binary" });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      const jsonData: any[][] = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
      });

      return cy.wrap(jsonData);
    });
  }
);
