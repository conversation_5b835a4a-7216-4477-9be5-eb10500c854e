export const disputeListLocators = {
  disputeTable: '[test-target="disputes-table"]',
  clearAllFilters: '[test-target="clear-all-filters"]',
  actionColumn: '.mat-column-action',
  actionButton: '.mat-column-action button',
  disputeDetails: '[test-target="dispute-details"]',
  orderNumber: '.mat-column-orderNumber a',
  searchInput:'[test-target="search-bar-input"]',
  searchDropDown: '[test-target="search-bar-dropdown"]',
  filterTypeDropDown: '[test-target="search-bar-select"]',
  filterTypeOption: '.mm-select-menu-wrapper .mm-option',
  searchDropdownOption: 'mat-option span',
  matColumnStatus: '[test-target="disputes-table"] td.mat-column-status',
  matColumnOrderNumber: '[test-target="disputes-table"] .mat-column-orderNumber',
};
