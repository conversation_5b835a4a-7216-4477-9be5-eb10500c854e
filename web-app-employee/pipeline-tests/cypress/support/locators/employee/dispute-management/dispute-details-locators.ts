export const disputeDetailsLocators = {
  disputeDetails: '[test-target="dispute-details"]',
  detailsButton: 'td.mat-column-action button',
  conversationTab: '[test-target="dispute-conversation-tab"]',
  caseDetailsTab: '[test-target="case-details-tab"]',
  conversationDialogue: '[test-target="conversation-dialogue"]',
  conversationInfo: '[test-target="conversation-info"]',
  escalateIssue: '[test-target="escalate-issue-to-paypal"]',
  caseDetails: '[test-target="case-details"]',
  orderLink: '[test-target="case-details-order-link"]',
  escalateMessage: '[test-target="escalate-dispute-message"]',
  escalateModalButton: '[test-target="escalate-modal-button"]',
  escalateModal: '[test-target="escalate-modal"]',
  escalateError: '[test-target="escalate-error"]',
  respondForm: '[test-target="respond-form"]',
  respondFormInput: '[test-target="respond-form-input"]',
  respondFormSubmitButton: '[test-target="respond-form-submit"]',
  respondFileAttachInput: '[test-target="attach-files-input"]',
  respondError: '[test-target="respond-error"] span',
  attachedDocuments: '[test-target="attached-documents"]',
  chatContainer: '[test-target="chat-container"]',
  chatBlocks: '[test-target="chat-container"] .chat',
  messageSent: '[test-target="message-from-seller"]'
};
