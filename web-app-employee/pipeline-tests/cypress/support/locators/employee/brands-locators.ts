export const brandsLocators = {
  addButton: '[test-target="add-new-brand"]',
  nameInput: '[test-target="brand-name"]',
  saveButton: '[test-target="save-button"]',
  editButton: (id: string) => `[test-target="edit-button-${id}"]`,
  transformationButton: '[test-target="add-transformation-button"]',
  transformationInput: '[test-target="transformations-input"]',
  logoInput: '[test-target="brand-logo-input"]',
  logoImageWrapper: '[test-target="brand-logo-image-wrapper"]',
  logoImage: '[test-target="brand-logo-image"]',
  logoRemoveIcon: '[test-target="brand-logo-remove-icon"]',
  logoRemoveConfirmButton: '[test-target="button-confirm"]',
  searchInput: '[test-target="search-input"]',
  searchButton: '[test-target="search-button"]',
  FilterSelect: '[test-target="filter-select"]',
  horecaFilterSelect: '[test-target="horeca-filter-select"]',
  cleanAllFilters: '[test-target="clear-all-filters"]',
  horecaFilterOption: '[test-target="horeca-option"]',
  filterBrandOptions: '[test-target="brand-filter-options"]',
};
