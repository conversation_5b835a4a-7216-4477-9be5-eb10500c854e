export const dashboardLocators = {
  overviewTable: '[test-target="discount-overview-table"]',
  createDiscountCodeButton: '[test-target="create-discount-code"]',
  discountTabs: '[test-target="discount-tabs"]',
  overviewTab: '[test-target="discount-tab-overview"]',
  redemptionTab: '[test-target="discount-tab-redemption"]',
  overviewPagination: '[test-target="discount-overview-paginator"]',
  overviewFilter: '[test-target="discount-code-overview-filters"]',
  filterTypeOption: '.mm-select-menu-wrapper .mm-option',
  searchInput:'[test-target="search-bar-input"]',
  mmCreatedByColumn: '[test-target="discount-overview-table"] tbody td.mm-column-createdByEmail',
  clearAllFilters: '[test-target="clear-all-filters"]',
  mmCodeColumn: '[test-target="discount-overview-table"] tbody td.mm-column-code',
  mmSalesChannelColumn: '[test-target="discount-overview-table"] tbody td.mm-column-country',
  mmAmountColumn: '[test-target="discount-overview-table"] tbody td.mm-column-amount',
  mmStatusColumn: '[test-target="discount-overview-table"] tbody td.mm-column-status',
  startDate: '[test-target="discount-code-start-time"]',
  endDate: '[test-target="discount-code-end-time"]',
  redemptionDate: '[test-target="discount-redemption-table"] tbody td.mm-column-redemptionDate',
  searchDropdownOption: '.mat-option-text',
  searchDropDown: '[test-target="search-bar-dropdown"]',
  filterTypeDropDown: '[test-target="search-bar-select"]',
  redemptionTable: '[test-target="discount-redemption-table"]',
  redemptionFilters: '[test-target="discount-code-redemption-filters"]',
  redemptionPaginator: '[test-target="discount-redemption-paginator"]',
  mmOrderNumberColumn: '[test-target="discount-redemption-table"] tbody td.mm-column-orderNumber',
  mmRedemptionCodeColumn: '[test-target="discount-redemption-table"] tbody td.mm-column-code',
  mmRedemptionDateColumn: '[test-target="discount-redemption-table"] tbody td.mm-column-redemptionDate',
  overviewMenuButton: '[test-target="overview-menu-button"]',
  actionsMenu: '[test-target="actions-menu"]',
  deactivateDiscountCodeButton: '.actions-menu button[test-target="deactivate-discount-code"]',
  extendDiscountCodeButton: '.actions-menu button[test-target="extend-discount-code"]',
  extendDiscountCodeSubmitButton: '[test-target="extend-code-modal-button"]',
  discountCodeStatus: '[test-target="status-discount-code"]',
  code: 'td.mm-column-code',
  status: 'td.mm-column-status',
  viewDetailsDiscountCodeButton: '.actions-menu button[test-target="view-details-discount-code"]',
  discountCodeForm: '[test-target="discount-code-form"]',
  satDatePickerToggle: '[test-target="sat-date-picker-toggle"]',
  satDatePickerInput: '[test-target="sat-date-picker-input"]',
  matCalendarNextButton: '[mat-calendar-next-button]',
  currentEndDate: '[test-target="current-end-date-discount-code"]',
  extendCodeMessage: '[test-target="discount-code-extend-message"]',
  extendCodeNwError: '[test-target="discount-code-extend-network-error"]',
  extendCodeFormError: '[test-target="discount-code-extend-form-error"]',
  extendCodeTimeSelector: '[test-target="extend-code-time-select"]',
  extendCodeTimeOption: '.time-field .ng-option',
  extendValidityTimeSelection: '[test-target="extend-validity-time-selection"]',
}

