export const contentProviderLocators = {
  filterSelect: '[test-target="content-providers-filter-select"]',
  searchInput: '[test-target="search-content-provider-input"]',
  searchButton: '[test-target="search-button"]',
  editRankButton: '[test-target="edit-content-provider-rank-button"]',
  editRankInput: '[test-target="edit-content-provider-rank-input"]',
  submitRankButton: '[test-target="submit-content-provider-rank-button"]',
  invalidRankValue: '[test-target="invalid-content-provider-rank-value"]',
  rankValue: '[test-target="content-provider-rank-value"]',
  editNoteButton: '[test-target="edit-content-provider-note-button"]',
  editNoteInput: '[test-target="edit-content-provider-note-input"]',
  submitNoteButton: '[test-target="submit-content-provider-note-button"]',
  noteValue: '[test-target="content-provider-note-value"]',
  organizationNameLabel:
    '[test-target="content-providers-list-organization-name-label--cell"]',
  organizationIdLabel:
    '[test-target="content-providers-list-cp-id-label--cell"]',
  shopNameLabel: '[test-target="content-providers-list-shop-name-label--cell"]',
  sourceNameLabel:
    '[test-target="content-providers-list-source-name-label--cell"]',
  sourceIdLabel: '[test-target="content-providers-list-source-id-label--cell"]',
  downloadListButton: '[test-target="download-content-provider-list"]',
  marketPicker: '[test-target="market-picker-filter"]',
  marketLabel: '[test-target="content-providers-list-markets-label--cell"]',
  editButton: (id: string) => `[test-target="edit-button-${id}"]`,
};
