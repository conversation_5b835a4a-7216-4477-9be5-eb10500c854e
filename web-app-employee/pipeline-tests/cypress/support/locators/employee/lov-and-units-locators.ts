export const lovAndUnitsLocators = {
  unitGroups: '[test-target="unit-groups"]',
  tablePaginationSelect: '[test-target="table-pagination-select"]',
  lovListTitle: '[test-target="lov-list-title"]',
  lov: '[test-target="lov"]',
  createLove: '[test-target="add-new-lov"]',
  lovNameEnInput: '[test-target="lov-name-en"]',
  lovNameDeInput: '[test-target="lov-name-de"]',
  lovOptionEnInput: '[test-target="lov-option-en"]',
  lovOptionDeInput: '[test-target="lov-option-de"]',
  saveLovButton: '[test-target="save-lov-button"]',
  addTransformationButton: '[test-target="add-transformation-button"]',
  inputLov: '[test-target="search-lov-input"]',
  previewGroupDetails: '[test-target="lov-preview-group-details"]',
  previewGroupTitle: '[test-target="lov-preview-group-title"]',
  previewValueGroupName: '[test-target="preview-property-value-group-name"]',
  previewValueTranslation:
    '[test-target="preview-property-value-translations"]',
  previewTotalValues: '[test-target="preview-property-value-total-values"]',
  previewTotalTransformations:
    '[test-target="preview-property-value-total-transformations"]',
  previewOptionValue: '[test-target="preview-option-value"]',
  previewMappingName: '[test-target="preview-mapping-name"]',
  previewTransformationsCount: '[test-target="preview-transformations-count"]',
  previewButton: (id: string) => `[test-target="preview-button${id}"]`,
  editButton: (id: string) => `[test-target="edit-lov-button-${id}"]`,
  searchWrapper: '[test-target="search-wrapper"]',
  searchResults: '[test-target="search-results"]',
  searchLovOption: '[test-target="search-input"]',
  searchLovButton: '[test-target="search-button"]',
  groupNameLovInput: '[test-target="group-name"]',
  addValueButton: '[test-target="add-value-button"]',
  transformationsCountChip: '[test-target="preview-transformations-count"]',
  valueCardLov: '[test-target="lov-value-card"]',
  transformationInput: (index: number) =>
    `[test-target="transformation-input-${index}"]`,
  removeTransformationButton: (index: number) =>
    `[test-target="transformation-remove-button-${index}"]`,
  newValueInput: '[test-target="new-option-value"]',
  valueInputTranslation: (lang: string) => `[test-target="lov-option-${lang}"]`,
  addValueConfirmationButton: '[test-target="add-value-confirmation-button"]',
};
