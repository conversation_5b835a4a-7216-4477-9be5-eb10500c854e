export const productUploadLocators = {
  organizationsList: '[test-id="PRODUCT_DATA_UPLOADS.ORGANIZATION_LIST"]',
  organization: (sellerId) => `[test-target="${sellerId}"]`,
  reviewUploadButton: '[test-target="review-link"]',
  rejectUploadButton: '[test-target="button-reject"]',
  approveUploadButton: '[test-target="button-approve"]',
  downloadUploadReportButton: '[test-target="button-download-report"]',
  assignButton: '[test-target="button-assign"]',
  assigneeTooltip: ".mm-tooltip",
  confirmUnassignButton: '[test-target="button-confirm"]',
  openStatsButton: (uploadId) => `[test-target="button-stats-${uploadId}"]`,
  statsMarketContainer: (marketCode) =>
    `[test-target="market-container-${marketCode}"]`,
  uploadToServerButton: '[test-target="button-upload-to-server"]',
  downloadCsvFileLink: '[test-target="link-download-csv-file"]',
  errorMessageSearch: '[test-target="search-error-message"]',
  reviewedTab: 'div[id="mat-tab-label-0-1"]',
  sortButton: 'button[test-target="sort-header-button"]',
  createdAtHeaderCell: '[test-target="created-at-header-cell"]',
  statusCell: 'span[test-target="status-cell"]',
  autoReviewIcon: 'round-badge[iconname="stroke"]',
  searchInput: '[test-target="search-input"]',
  searchButton: '[test-target="search-button"]',
  filterSelect: '[mmTestTarget="filter-select"]',
  selectFilterOptions: '[mmTestTarget="select-filter"]',
  searchResultsHeader: '[mmTestTarget="search-results-header"]',
  filterContainer: '[test-target="filter-container"]',
  filterOptionValue: ".ng-dropdown-panel .ng-option",
  filterOption: '[test-target="filter-option"]',
  uploadHistoryTable: '[test-target="upload-history-table"]',
  targetMarketsList: '[test-target="pending-markets"]',
  clearAllFiltersButton: '[test-target="clear-all-filters"]',
  pendingMarketsList: '[test-target="pending-markets-list"]',
  newProductsStats: '[test-target="stats-new-products"]',
  approvedProductsStats: '[test-target="stats-approved-products"]',
  rejectedProductsStats: '[test-target="stats-rejected-products"]',
  pendingProductsStats: '[test-target="stats-pending-products"]',
  totalProductsStats: '[test-target="stats-total-products"]',
  uploadReport: '[test-target="report-menu-button"]',
  uploadReportLabel: '[test-target="select-range-option"]',
  uploadReportExportButton: '[test-target="export-report-button"]',
  uploadReportExportRangeOption: '[test-target="select-range-option"]',
  uploadsReportSelector: '[test-target="uploads-report-selector"]',
};
