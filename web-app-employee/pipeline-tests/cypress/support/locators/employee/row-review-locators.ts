export const rowReviewLocators = {
  rowCheckBox: '[test-target="select-row-checkbox"]',
  filterTab: (status) => `[test-target="filter-tab-${status}"]`,
  rejectProductsButton: '[test-target="reject-button"]',
  approveProductsButton: '[test-target="approve-button"]',
  toogleNewProducts: '[test-target="new-products-toggle"]',
  allRowsCheckBox: '[test-target="select-all-rows-checkbox"]',
  rejectionModalTextArea: 'textarea[name="rejectionReason"]',
  rejectionModalRejectButton: '[test-target="dialog-apply-btn"]',
  rejectionModalSaveDraftButton: '[test-target="dialog-save-draft-btn"]',
  draftFilterToggle: '[test-target="hasDraftOnly"]',
};
