export const paymentWidgetLocators = {
  paypalWidgetSelector: '.payment-methods__type__brands__item--paypal',
  directDebitWidgetSelector: '.payment-methods__type__brands__item--directDebit',
  ratepayWidgetSelector: '.payment-methods__type__brands__item--ratepay',
  creditCardWidgetSelector: '.payment-methods__type__brands__item--visa',
  adyenErrorText: '[class="adyen-checkout__error-text"]',
  continueButton: '[test-target="payment-form__submit"]',
  creditCard: '[test-target="payment-method-creditCard"]',
  creditCardNumberInput: 'input[id="encryptedCardNumber"]',
  directEbanking: '[test-target="payment-method-directEbanking"]',
  expiryDateInput: 'input[id="encryptedExpiryDate"]',
  iframeCardNumber: '[data-cse="encryptedCardNumber"] iframe',
  iframeSecurityCode: '[data-cse="encryptedSecurityCode"] iframe',
  iframeExpiryDate: '[data-cse="encryptedExpiryDate"] iframe',
  paypal: '[test-target="payment-method-paypal"]',
  ratePayDirectDebit: '[test-target="payment-method-ratePayDirectDebit"]',
  ratePayDirectDebitIbanInput: '[test-target="direct-debit-iban"]',
  ratePayDirectDebitVatNumberInput: '[test-target="direct-debit-vat_number-input"]',
  ratePayDirectDebitIbanNotValid: '[test-target="direct-debit-iban-not_valid"]',
  ratePayDirectDebitVatNumberNotValid: '[test-target="direct-debit-vat_number-error"]',
  ratePayInvoice: '[test-target="payment-method-ratePayInvoice"]',
  securityCodeInput: 'input[id="encryptedSecurityCode"]',
  submitPayment: 'checkout-form-submit',
  saveAndContinueButton: 'button-content',
  threeDs1Username: 'input[name="username"]',
  threeDs1Password: 'input[name="password"]',
  threeDs1Submit: 'input[value="Submit"]',
  threeDs2Password: 'input[name="answer"]',
  threeDs2Iframe: '[name="threeDSIframe"]',
  submit3Ds2Button: '#buttonSubmit',
  acceptTerms: '.mm-checkbox__checkmark'
}
