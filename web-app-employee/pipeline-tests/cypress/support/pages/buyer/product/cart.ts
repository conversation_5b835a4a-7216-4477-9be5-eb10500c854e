import { productLocators } from "../../../locators/buyer/product/product-locators";

export class Cart {

  searchForOrder = (productName: string): void => {
    cy.getByTestId(productLocators.searchBox).type(`${productName}{enter}`);
  }

  addProductToCart = (): void => {
    cy.getByTestId(productLocators.productCard).first().click();
    cy.getByTestId(productLocators.addToCart).click();

  }

  assertSellerName = (sellerName: string): void => {
    cy.get(productLocators.sellerName).contains(sellerName);
  }

  assertOrdersFilter = (): void => {
    cy.getByTestId(productLocators.filtersContainer).should('be.visible');
  }

}

export const cartPage = new Cart();
