import { CHECKOUT_ADDRESS } from "../../../constants/endpoints";
import { addressLocators } from "../../../locators/buyer/product/address-locators";


export class Address {

  navigateToAddressesPage = (): void => {
    cy.visit(Cypress.env('buyerUrl') + CHECKOUT_ADDRESS);
  }

  proceedWithCheckout = () => {
    cy.getByTestId(addressLocators.submitForm).should('be.visible').click({force: true});
  }
}

export const addressPage = new Address();
