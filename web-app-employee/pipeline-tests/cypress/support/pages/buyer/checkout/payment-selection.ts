import { paymentWidgetLocators } from "../../../locators/buyer/checkout/payment-widgets-locators";
import { CreditCardDetails } from "../../../interfaces/credit-card-details";
import { OrderDetails, PaymentAssertions } from "../../../interfaces/user-details";
import { CREDIT_CARD, DIRECT_DEBIT, OPEN_INVOICE } from "../../../constants/payment-defines";

export class PaymentSelection {


  assertForPaymentWidgets = (paymentWidget: PaymentAssertions): void => {
    paymentWidget.isThere?.forEach(elem => {
      cy.get(elem).should('be.visible');
    });

    paymentWidget.notThere?.forEach(elem => {
      cy.get(elem).should('not.exist');
    });
  }

  executePaymentMethod = (order: OrderDetails) => {
    switch (order?.paymentMethod) {
      case DIRECT_DEBIT:
        this.implementDirectDebitFlow(order);
        break;
      case CREDIT_CARD:
        this.implementThreeDs1Flow(order);
        break;

      case OPEN_INVOICE:
      this.implementOpenInvoiceFlow()
      break;

      default:
        this.implementThreeDs2Flow(order)
    }
  }

  implementDirectDebitFlow = (order: OrderDetails) => {
    cy.get(paymentWidgetLocators.ratePayDirectDebit).click();
    cy.get(paymentWidgetLocators.ratePayDirectDebitIbanInput)
    .type(order.debitCardDetails.iban);
    cy.get(paymentWidgetLocators.acceptTerms).click();
    cy.wait(2000);
    cy.get(paymentWidgetLocators.continueButton).should('be.enabled');
    cy.get(paymentWidgetLocators.continueButton).click({force: true});
    cy.getByTestId(paymentWidgetLocators.submitPayment).click();
  }

  implementThreeDs1Flow = (order: OrderDetails) => {
    this.fillCreditCardDetails(order?.threeDs1CreditCardDetails);
    cy.getByTestId(paymentWidgetLocators.saveAndContinueButton).click();
    cy.getByTestId(paymentWidgetLocators.submitPayment).click();
  }

  fillCreditCardDetails = (creditCard: CreditCardDetails) => {
    cy.get(paymentWidgetLocators.creditCard).click();

    cy.getIframe(paymentWidgetLocators.iframeCardNumber)
      .find(paymentWidgetLocators.creditCardNumberInput)
      .type(creditCard?.creditCardNumber);
    cy.getIframe(paymentWidgetLocators.iframeExpiryDate)
      .find(paymentWidgetLocators.expiryDateInput)
      .type(creditCard?.validUntil);
    cy.getIframe(paymentWidgetLocators.iframeSecurityCode)
      .find(paymentWidgetLocators.securityCodeInput)
      .type(creditCard?.cvcCode);
  }

  fillThreeDs1ChallengeDetails = (order: OrderDetails) => {
    cy.get(paymentWidgetLocators.threeDs1Username).type(order?.threeDs1CreditCardDetails?.usernameAuthentication);
    cy.get(paymentWidgetLocators.threeDs1Password).type(order?.threeDs1CreditCardDetails?.passwordAuthentication);
    cy.get(paymentWidgetLocators.threeDs1Submit).click();
  }

  implementThreeDs2Flow = (order: OrderDetails) => {
    this.fillCreditCardDetails(order?.threeDs2CreditCardDetails);
    cy.getByTestId(paymentWidgetLocators.saveAndContinueButton).click();
    cy.getByTestId(paymentWidgetLocators.submitPayment).click();
    this.fillThreeDs2ChallengeDetails(order);
  }

  fillThreeDs2ChallengeDetails = (order: OrderDetails) => {
    cy.getIframe(paymentWidgetLocators.threeDs2Iframe).should('be.visible');
    cy.getIframe(paymentWidgetLocators.threeDs2Iframe).find(paymentWidgetLocators.threeDs2Password)
      .type(order?.threeDs2CreditCardDetails?.passwordAuthentication);
    cy.getIframe(paymentWidgetLocators.threeDs2Iframe).find(paymentWidgetLocators.submit3Ds2Button)
      .click({ force: true });
  }

  implementOpenInvoiceFlow = () => {
    cy.get(paymentWidgetLocators.ratePayInvoice).click();
    cy.wait(2000);
    cy.get(paymentWidgetLocators.continueButton).click({force: true});
    cy.getByTestId(paymentWidgetLocators.submitPayment).click();
  }
}


export const paymentSelectionPage = new PaymentSelection();
