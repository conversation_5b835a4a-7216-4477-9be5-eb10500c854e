import { reviewOrderLocators } from "../../../locators/buyer/review/review-order";
import { paymentWidgetLocators } from "../../../locators/buyer/checkout/payment-widgets-locators";

export class ReviewOrder {

  assertOrderWasPlaced = (orderNumber: string): void => {
    cy.getByTestId(reviewOrderLocators.orderDetailsLink).should('be.visible').click();
    cy.getByTestId(reviewOrderLocators.orderTitle).should('be.visible').contains(orderNumber);
    cy.getByTestId(reviewOrderLocators.orderWrapper).filter(`:contains(${orderNumber})`).within(() => {
      cy.get(reviewOrderLocators.orderLineStatus).then((orderStatus) => {
        orderStatus[0].attributes['test-target'].nodeValue === reviewOrderLocators.placedOrderLineStatus ?
          cy.getByTestId(reviewOrderLocators.placedOrderLineStatus).should('be.visible') :
          cy.getByTestId(reviewOrderLocators.pendingVerificationOrderLineStatus).should('be.visible');
      });
    });
  }

  setOrdernumber = (orderNumber: string): void => {
    cy.task('setOrderNumber', orderNumber);
  }
}

export const reviewOrderPage = new ReviewOrder();
