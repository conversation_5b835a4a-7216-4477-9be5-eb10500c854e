import { transactionDetailsLocators } from "../../locators/employee/transaction-details-locators";

export class TransactionDetails {


  executePartialRefundFlow(): void {
    cy.get(transactionDetailsLocators.checkbox).click();
    cy.getByTestId(transactionDetailsLocators.partialRefundButton).click();
    cy.getByTestId(transactionDetailsLocators.radioGroupItem).first().check({ force: true });
    cy.getByTestId(transactionDetailsLocators.refundBy).click();
    cy.get(transactionDetailsLocators.option).contains('Quantity').click();
    cy.getByTestId(transactionDetailsLocators.amount).type('1');
    cy.getByTestId(transactionDetailsLocators.freshDeskTicket).type('123');
    cy.getByTestId(transactionDetailsLocators.grade).click();
    cy.get(transactionDetailsLocators.option).contains('B').click();
    cy.getByTestId(transactionDetailsLocators.refundReason).click();
    cy.get(transactionDetailsLocators.option).contains('It does not work').click();
    cy.get(transactionDetailsLocators.confirmPartialRefundButton).click();
  }

  assertIfRefundableBalanceIszero(): void {
    cy.reload();
    cy.getByTestId('refundable-balance').should('include.text', '0,00');
  }
}

export const transactionDetailsPage = new TransactionDetails();
