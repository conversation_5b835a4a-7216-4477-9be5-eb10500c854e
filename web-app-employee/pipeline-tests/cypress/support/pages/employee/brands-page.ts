import { apiEndpoints } from "../../endpoints";
import { brandKeys } from "../../interfaces/response-keys";
import { brandsLocators } from "../../locators/employee/brands-locators";
import { checkIfDataHasMainProperties } from "../../common";
import { PimPage } from "./common/pim-page";
import "cypress-file-upload";
import {
  BrandFilterOptionsValue,
  FilterBrandOptions,
  FilterHorecaOptions,
  HorecaFilterValues,
} from "../../constants/pim-brands-constants";
import { serviceCategoryApi } from "../../api/service-category-api";
import { brandBuilder } from "../../../builders/pim-brand-builder";

export class BrandsPage extends PimPage {
  public interceptPostBrandLogo(brandId: string): void {
    cy.intercept("POST", `**/${apiEndpoints.brandLogo(brandId)}*`).as(
      "postBrandLogo"
    );
  }

  public postBrandLogo(): void {
    const fileName = "brand-logo.webp";
    const mimeType = "image/webp";
    cy.fixture(`brands/${fileName}`).then((fileContent) => {
      cy.get(brandsLocators.logoInput).attachFile({
        fileContent: fileContent.toString(),
        fileName,
        mimeType,
      });
    });
  }

  public checkIfLogoIsAdded(): void {
    cy.wait("@postBrandLogo").then((interception) => {
      this.checkStatusCode(interception);
      cy.get(brandsLocators.logoImage).should("be.visible");
    });
  }

  public interceptDeleteBrandLogo(brandId: string): void {
    cy.intercept("DELETE", `**/${apiEndpoints.brandLogo(brandId)}*`).as(
      "deleteBrandLogo"
    );
  }

  public deleteBrandLogo(): void {
    cy.get(brandsLocators.logoImageWrapper)
      .should("be.visible")
      .trigger("mouseenter");

    cy.get(brandsLocators.logoRemoveIcon).should("be.visible").click();

    cy.get(brandsLocators.logoRemoveConfirmButton).should("be.visible").click();
  }

  public checkIfLogoIsDeleted(): void {
    cy.wait("@deleteBrandLogo").then((interception) => {
      this.checkStatusCode(interception);
      cy.get(brandsLocators.logoImage).should("not.exist");
      cy.get(brandsLocators.logoInput).should("exist");
    });
  }

  public checkBrandWithValidId(data: any): void {
    brandKeys.forEach((property) => {
      expect(data.body).to.have.property(property);
    });
    this.checkStatusCode(data);
  }

  public checkBrandWithInvalidId(data: any): void {
    const title = data.body.title.toLowerCase();
    expect(title).to.eq("not found");
    this.checkStatusCode(data, 404);
  }

  public checkInvalidBrand(data: any): void {
    this.checkStatusCode(data, 400);
  }

  public checkValidBrand(data: any): void {
    expect(data).property("body").to.equal("");
    this.checkStatusCode(data, 201);
  }

  public checkStatusCode(response: any, code: number = 200): void {
    if (response.status) {
      expect(response).property("status").to.equal(code);
    }

    if (response.statusCode) {
      expect(response).property("statusCode").to.equal(code);
    }
  }

  public searchBrand(searchFilter: string): void {
    this.interceptFilteredBrands(
      BrandFilterOptionsValue.SearchFilter,
      searchFilter
    );

    cy.get(brandsLocators.searchInput).clear().type(searchFilter);
    cy.get(brandsLocators.searchButton).click();
  }

  public horecaFilter(horecaOption: FilterHorecaOptions): void {
    this.interceptFilteredBrands(
      BrandFilterOptionsValue.HorecaFilter,
      HorecaFilterValues[horecaOption]
    );

    cy.get(brandsLocators.FilterSelect).click();
    cy.get(brandsLocators.filterBrandOptions)
      .contains(FilterBrandOptions.HorecaFilter)
      .click();

    cy.get(brandsLocators.horecaFilterSelect).click();
    cy.get(brandsLocators.horecaFilterOption).contains(horecaOption).click();

    cy.wait("@getFilteredBrands").then((interception) => {
      this.checkHorecaBrands(interception.response);
    });
  }

  public checkHorecaBrands(data: any): void {
    checkIfDataHasMainProperties(data.body);

    data.body.items.forEach((item) => {
      brandKeys.forEach((property) => {
        expect(item).to.have.property(property);
      });
      expect(item.horecaRelevant).to.eq(true);
    });

    this.checkStatusCode(data);
  }

  public checkBrandsWithCorrectName(data: any, searchFilter?: string): void {
    checkIfDataHasMainProperties(data.body);

    data.body.items.slice(0, 10).forEach((item) => {
      cy.log(`Brand name: ${item.name}.`);
      brandKeys.forEach((property) => {
        expect(item).to.have.property(property);
      });

      if (searchFilter) {
        const hasSearchFilter = brandKeys.some((property) =>
          this.itemHasValue(item[property], searchFilter)
        );
        return expect(hasSearchFilter).to.eq(true);
      }
    });
    this.checkStatusCode(data);
  }

  public checkBrandsWithUniqueName(
    data: any,
    uniqueBrandName: string = null,
    transformation: string = null
  ): void {
    expect(data.name).to.be.eq(uniqueBrandName);

    const transformationName =
      data.mappings.length > 0 ? transformation || data.mappings[0].name : null;

    if (transformationName) {
      this.checkUniqueTransformation(data, transformationName);
    } else {
      this.checkEmptyTransformation(data);
    }
  }

  private checkUniqueTransformation(data: any, transformation: string): any {
    const hasTransformation = data.mappings.some(
      (item) => item.name === transformation
    );
    return expect(hasTransformation).to.eq(true);
  }

  private checkEmptyTransformation(data: any): void {
    expect(data.mappings).is.to.be.empty;
  }

  public checkBrandsWithWrongName(data: any): void {
    checkIfDataHasMainProperties(data.body);

    expect(data.body.totalCount).to.be.eq(0);
    assert.isArray(data.body.items);
    expect(data.body.items).is.to.be.empty;

    this.checkStatusCode(data);
  }

  public interceptBrands(): void {
    cy.intercept("GET", `**/${apiEndpoints.brands}*`).as("getPimBrands");
  }

  public interceptBrand(id: string): void {
    cy.intercept("GET", `**/${apiEndpoints.brandDetails(id)}`).as(
      "getPimBrand"
    );
  }

  public interceptEditBrand(id: string): any {
    cy.intercept("PUT", `**/${apiEndpoints.brandDetails(id)}*`).as("editBrand");
    cy.log(`${Cypress.env("categoryBaseUrl")}${apiEndpoints.brandDetails(id)}`);
  }

  public interceptCreateBrand(): any {
    cy.intercept("POST", `**/${apiEndpoints.brands}*`).as("createBrand");
    cy.log(`${Cypress.env("categoryBaseUrl")}${apiEndpoints.brands}`);
  }

  public interceptFilteredBrands(
    filterName: string,
    valueFilter: string
  ): void {
    cy.intercept(
      "GET",
      `**/${apiEndpoints.brands}?*filter%5B${filterName}%5D=${valueFilter}*`
    ).as("getFilteredBrands");
  }

  public goToBrand(id: string): void {
    cy.get(brandsLocators.editButton(id)).click();
  }

  public goToAddNewBrandForm(): void {
    cy.get(brandsLocators.addButton).click();
  }

  public getItemWithNonEmptyMappings(data: any) {
    return data.body.items.find((item: any) => item.mappings.length > 0);
  }

  public setBrandName(randomNumber: number): void {
    cy.get(brandsLocators.nameInput)
      .invoke("val", "")
      .trigger("change")
      .type(`unique_name_${randomNumber}`);
    this.saveBrand();
  }

  public saveBrand(): any {
    cy.get(brandsLocators.saveButton).click();
  }

  public createBrand(name: string, transformation: string = null): void {
    const params = {
      name,
      mappings: transformation ? [{ name: transformation }] : [],
    };
    const data = brandBuilder(params);

    serviceCategoryApi.brand.create(data);
    cy.get("@createBrand").then((response) => {
      this.checkValidBrand(response);
    });
  }

  public updateBrand(randomNumber: number): void {
    cy.get(brandsLocators.nameInput)
      .invoke("val", "")
      .trigger("change")
      .type(`unique_name_${randomNumber}`);
    cy.get(brandsLocators.transformationButton).click();
    cy.get(brandsLocators.transformationInput)
      .first()
      .invoke("val", "")
      .trigger("change")
      .type(`unique_transformation_name_${randomNumber}`);
    this.saveBrand();
  }

  private itemHasValue(property, searchFilter): boolean {
    const regExp = new RegExp(searchFilter, "gi");
    return Array.isArray(property)
      ? property.some((value: string) => regExp.test(value))
      : regExp.test(property);
  }
}

export const brandsPage = new BrandsPage();
