import { apiEndpoints } from "../../endpoints";
import { MARKET_KEY } from "../../constants/local-storage-constants";
import { attributesKeys } from "../../interfaces/response-keys";
import { AttributeType } from "../../constants/pim-enums";
import { attributeTypes } from "../../constants/pim-structures";
import { checkIfDataHasMainProperties } from "../../common";
import { checkStatusCode } from "../../helpers";
import { Attribute, AttributeUnit } from "../../interfaces/attribute-model";
import { attributeFixturesByType } from "../../../builders/pim-attributes-builder";
import { PimPage } from "./common/pim-page";

export class AttributesPage extends PimPage {
  public checkAttributes(response: any): void {
    expect(response.status)?.to.eq(200);

    const attributes = response.body;
    checkIfDataHasMainProperties(attributes);
    expect(attributes.totalCount).to.be.gt(0);

    attributes.items.forEach((item) => {
      expect(item).to.have.all.keys(attributesKeys.attribute);
      expect(item.type).to.have.all.keys(attributesKeys.type);
    });
  }

  public checkAttributeCodes(response: any): void {
    expect(response.status).to.eq(200);

    const attributes = response.body;
    assert.isArray(attributes.items);
    expect(attributes.totalCount).to.be.gt(0);

    const firstCode: string = attributes.items[0].code.toLowerCase();
    const secondCode: string = attributes.items[1].code.toLowerCase();
    const isSortedDesc = firstCode.localeCompare(secondCode);

    expect(isSortedDesc).to.be.eq(1);
  }

  public checkAttributesSortedByType(response: any): void {
    expect(response.status).to.eq(200);

    assert.isArray(response.body.items);
    const attributesList: Array<any> = response.body.items;
    const ids = attributesList.map((attribute) => {
      return attribute.type.id;
    });
    expect(ids).to.deep.equal(ids.sort());
  }

  public checkAttributeTypeDetails(type: number, response: any): void {
    Object.keys(attributeTypes[type]).forEach((key) => {
      const property = attributeTypes[type][key];
      if (property && typeof property === "object") {
        return Object.keys(property).forEach((nestedKey) => {
          expect(response[key]).to.have.property(nestedKey);
          expect(response[key][nestedKey]).to.eq(property[nestedKey]);
        });
      }
      expect(response).to.have.property(key);
      expect(response[key]).to.eq(property);
    });
  }

  public checkAttributePreview(data: any, searchName: string): void {
    data.items.forEach((item) => {
      cy.log(JSON.stringify(item));
      const test =
        item.name.toLowerCase().includes(searchName) ||
        item.csvHeaderLabel.toLowerCase().includes(searchName);
      expect(test).to.eq(true);
    });
    const attributeId = data.items[0].id;
    cy.get(`[test-target="preview-attribute-button-${attributeId}"]`).click();
    cy.get(`[test-target="preview-attribute-title"`).then((element) => {
      const title = element.text();
      expect(title.trim()).to.eq(data.items[0].name.trim());
    });
  }

  public interceptAttributeSearch(searchName: string): void {
    const queryParams = `?limit=1000&offset=0&sort%5Bname%5D=asc&filter%5Bsearch%5D=${searchName}`;
    cy.getLocalStorage(MARKET_KEY).then((value) => {
      const market = JSON.parse(value);
      cy.intercept(
        "GET",
        `**/${apiEndpoints.attributes(market.id)}${queryParams}`
      ).as("getPimAttributeSearch");
    });
  }

  public checkAttributeCreated(attributeType: number, response: any): void {
    expect(response.status).to.eq(201);
    expect(response.body).to.have.property("id");
    expect(response.body.id).to.not.be.empty;

    attributeFixturesByType.push({
      type: attributeType,
      id: response.body.id,
    });
  }

  public checkAttributeUpdated(
    updatedAttributeResponse: any,
    attribute: Attribute,
    LOVId?: string,
    unit?: AttributeUnit
  ) {
    switch (attribute.typeId) {
      case AttributeType.LOV:
        checkStatusCode(updatedAttributeResponse);

        const updatedAttribute = updatedAttributeResponse.body;

        expect(updatedAttribute.lov?.id).equals(LOVId);
        expect(updatedAttribute.condition.multiple).equals(
          attribute.condition.multiple
        );
        break;
      case AttributeType.INTEGER:
        checkStatusCode(updatedAttributeResponse);

        expect(updatedAttributeResponse.body.unit.id).equals(unit.id);
        expect(updatedAttributeResponse.body.condition.filterable).equals(
          attribute.condition.filterable
        );
        break;
      case AttributeType.DECIMAL:
        checkStatusCode(updatedAttributeResponse);

        expect(updatedAttributeResponse.body.unit).equals(unit);
        expect(updatedAttributeResponse.body.translations[0]).deep.equals(
          attribute.translations[0]
        );
        expect(updatedAttributeResponse.body.translations[1]).deep.equals(
          attribute.translations[1]
        );
        break;
    }
  }
}

export const attributesPage = new AttributesPage();
