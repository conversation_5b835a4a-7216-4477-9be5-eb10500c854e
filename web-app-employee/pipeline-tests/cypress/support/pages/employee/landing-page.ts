import { landingPageLocators } from '../../locators/employee/landing-page-locators';
import { paymentDetailsLocators } from '../../locators/employee/payment-details-locators';
import { EMPLOYEE_PAYMENTS_URL, API_SERVICE_PAYMENT_TRANSACTIONS_URL } from '../../constants/endpoints';
import { acceptLanguages } from "../../constants/pim-constants";
import { apiEndpoints } from '../../endpoints';

export class LandingPage {

  public goToLandingPage(): void {
    cy.visit(Cypress.env('employeeUrl'));
  }

  public goToLandingEnPage(): void {
    cy.visit(`${Cypress.env('employeeUrl')}/${acceptLanguages[1]}`);
  }

  public interceptGetAvailableMarkets(): void {
    cy.intercept('GET', `**/${apiEndpoints.markets}*`).as('getAvailableMarkets');
    cy.log(`${Cypress.env('categoryBaseUrl')}${apiEndpoints.markets}`);
  }

  public checkStructureOfAvailableMarkets(data: any): void {
    expect(data).to.have.property('totalCount');
    expect(data).to.have.property('items');

    assert.isArray(data.items);
    data.items.forEach(market => {
      expect(market).to.have.property('id');
      expect(market).to.have.property('name');
      expect(market).to.have.property('lang');
      expect(market.lang).to.have.property('id');
    });
  }
  public goToCategoryTreePage(): void {
    this.navigateToPim();
    cy.get(landingPageLocators.categoryTree).click();
  }

  public goToAttributesPage(): void{
    this.navigateToPim();
    cy.get(landingPageLocators.attributes).click();
  }

  public goToLOVandUnitsPage(): void {
    this.navigateToPim();
    cy.get(landingPageLocators.lovAndUnits).click();
  }

  public goToBrandsPage(): void {
    this.navigateToPim();
    cy.get(landingPageLocators.brands).click();
  }

  public goToContentProviderRankingPage(): void {
    this.navigateToPim();
    cy.get(landingPageLocators.contentProviderRanking).click();
  }

  private navigateToPim(): void {
    cy.get(landingPageLocators.sellerSupport).click();
    cy.get(landingPageLocators.pim).click();
  }

  checkIfOrderNumberExists(): void {
    cy.task('getOrderNumber').then((orderNumber: string) => {
      cy.contains(orderNumber)
        .should('be.visible')
        .siblings()
        .within(() => {
          cy.get(paymentDetailsLocators.viewDetailsButton).click();
          cy.wait(4000);
        });
    });
  }

  checkIfOrderWasCaptured(): void {
    cy.url().then(url => {
      const split = JSON.stringify(url).split(EMPLOYEE_PAYMENTS_URL);
      const paymentId = split[1].replace('"', '');

      this.checkPaymentStatus(paymentId, 'Captured');

      cy.reload();
      cy.contains('Captured').should('be.visible');
    });
  }

  checkIfOrderWasAuthorized(): void {
    cy.url().then(url => {
      cy.contains('Authorized').should('be.visible');
    });
  }

  checkPaymentStatus(orderId: string, statusToCheck: string,  attempt = 0) {
    if (attempt > 25) {
      throw 'Error';
    }
    cy.request(`${API_SERVICE_PAYMENT_TRANSACTIONS_URL}${orderId}`).then((response) => {
      if (response.body.items[0].status_value !== statusToCheck) {
        cy.wait(3000);
        this.checkPaymentStatus(orderId, statusToCheck, attempt + 1);
      }
    });
  }

  goToProductUploadsV2(): void {
    cy.get(landingPageLocators.sellerSupport).click();
    cy.get(landingPageLocators.products).click();
    cy.get(landingPageLocators.productDataUploadsV2).click();
  }
}


export const landingPage = new LandingPage();
