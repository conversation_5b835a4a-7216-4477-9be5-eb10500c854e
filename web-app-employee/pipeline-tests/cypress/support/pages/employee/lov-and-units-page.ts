import { apiEndpoints } from "../../endpoints";
import {
  lovDetailsKeys,
  lovListKeys,
  unitGroupsKeys,
} from "../../interfaces/response-keys";
import { lovAndUnitsLocators } from "../../locators/employee/lov-and-units-locators";
import { MARKET_KEY } from "../../constants/local-storage-constants";
import {
  checkIfArrayNotEmpty,
  checkIfDataHasMainProperties,
  checkResponseStatus404,
} from "../../common";
import { PimPage } from "./common/pim-page";
import "cypress-file-upload";
import {
  Languages,
  LOV_NUMBER_OF_TRANSFORMATIONS,
  LOV_SUFFIXES,
  SEARCH_RESULT_MESSAGE,
  translatableValues,
} from "../../constants/pim-lov-constants";
import { productUploadLocators } from "../../locators/employee/product-upload-locators";

export class LovAndUnitsPage extends PimPage {
  public interceptGetMeasureUnit(): void {
    cy.intercept("GET", `**/${apiEndpoints.measureUnits}*`).as(
      "getMeasureUnits"
    );
  }

  public goToMeasureUnit(): void {
    cy.get(lovAndUnitsLocators.unitGroups).click();
  }

  public goToLOVs(): void {
    cy.get(lovAndUnitsLocators.lov).click();
  }

  public interceptLovDetails(id: string): void {
    cy.intercept("GET", `**/${apiEndpoints.lovDetails(id)}*`).as(
      "getLovDetails"
    );
    cy.log(`${Cypress.env("categoryBaseUrl")}${apiEndpoints.lovDetails(id)}`);
  }

  public interceptLovs(): void {
    cy.getLocalStorage(MARKET_KEY).then((value) => {
      const market = JSON.parse(value);
      cy.intercept("GET", `**/${apiEndpoints.lovsByMarket(market.id)}*`).as(
        "getLovsByMarket"
      );
      cy.log(
        `${Cypress.env("categoryBaseUrl")}${apiEndpoints.lovsByMarket(
          market.id
        )}`
      );
    });
  }

  public checkMeasureUnits(data: any, searchFilter?: string): void {
    checkIfDataHasMainProperties(data);
    expect(data.totalCount).to.be.gt(0);
    checkIfArrayNotEmpty(data.items);
    data.items.forEach((item) => {
      cy.log(`Unit name: ${item.name}.`);
      if (searchFilter) {
        const hasSearchFilter = unitGroupsKeys.some((property) =>
          this.itemHasValue(item[property], searchFilter)
        );
        return expect(hasSearchFilter).to.eq(true);
      }
      unitGroupsKeys.forEach((property) => {
        expect(item).to.have.property(property);
      });
    });
  }

  public checkLovsApi(data: any, filterKey: string, filterValue: string): void {
    checkIfDataHasMainProperties(data);
    expect(data.totalCount).to.be.gt(0);
    checkIfArrayNotEmpty(data.items);

    data.items.forEach((item) => {
      expect(this.itemHasValue(item[filterKey], filterValue)).to.eq(true);
    });

    this.checkStatusCode(data);
  }

  public checkNonExistingLovsApi(data: any): void {
    checkIfDataHasMainProperties(data);

    expect(data.totalCount).to.be.eq(0);
    assert.isArray(data.items);
    expect(data.items).to.be.empty;
  }

  public checkLovsUI(data: any): void {
    checkIfDataHasMainProperties(data);
    expect(data.totalCount).to.be.gt(0);
    data.items.forEach((item, index) => {
      expect(item).to.have.all.keys(lovListKeys.lov);

      cy.get(lovAndUnitsLocators.lovListTitle)
        .eq(index)
        .should("contain", item.name);
    });

    //check if LOVs are sorted by name
    const names = data.items.map((item) => item.name);
    const sortedNames = names.sort();
    expect(names).to.be.eq(sortedNames);
  }

  public checkLovDetailsApi(data: any): void {
    expect(data).to.have.all.keys(lovDetailsKeys.lovDetails);

    checkIfArrayNotEmpty(data.translations);
    this.checkLovTranslations(data.translations);

    checkIfArrayNotEmpty(data.options);
    data.options.forEach((option) => {
      expect(option).to.have.all.keys(lovDetailsKeys.options);

      checkIfArrayNotEmpty(option.translations);
      this.checkLovTranslations(option.translations);

      checkIfArrayNotEmpty(option.mappings);
      option.mappings.forEach((mapping) => {
        expect(mapping).to.have.all.keys(lovDetailsKeys.mappings);
      });
    });
  }

  public checkLovDetailsByInvalidId(response: any): void {
    checkResponseStatus404(response);
  }

  public checkStatusCode(response: any, code: number = 200): void {
    if (response.status) {
      expect(response).property("status").to.equal(code);
    }

    if (response.statusCode) {
      expect(response).property("statusCode").to.equal(code);
    }
  }

  public interceptCreateLov(): any {
    cy.intercept("POST", `**/${apiEndpoints.lov}`).as("createLov");
  }

  public interceptUpdateLov(id: string): void {
    cy.intercept("PUT", `**/${apiEndpoints.lovDetails(id)}*`).as("updateLov");
    cy.log(`${Cypress.env("categoryBaseUrl")}${apiEndpoints.lovDetails(id)}`);
  }

  public goToCreateLov(): void {
    cy.get(lovAndUnitsLocators.createLove).click();
  }

  public setValuesIntoLov(baseValue: string): string {
    const lovName = `${baseValue}_${LOV_SUFFIXES.GROUP}`;
    cy.get(lovAndUnitsLocators.groupNameLovInput)
      .invoke("val", "")
      .trigger("change")
      .type(lovName);

    this.addValueCard(baseValue);
    this.fillTranslationInputs(baseValue);
    this.fillTransformationInputs(baseValue);

    this.saveLov();
    return lovName;
  }

  public searchValue(value: string): void {
    cy.get(lovAndUnitsLocators.searchLovOption).clear().type(value);
    cy.get(lovAndUnitsLocators.searchLovButton).click();

    cy.get(lovAndUnitsLocators.searchResults).should(
      "contain",
      SEARCH_RESULT_MESSAGE(value)
    );
  }

  public editTranslationAndRemoveMapping(lang: string, newValue: string): void {
    cy.get(lovAndUnitsLocators.valueCardLov).click();
    cy.get(lovAndUnitsLocators.valueInputTranslation(lang))
      .clear()
      .type(newValue);
    cy.get(lovAndUnitsLocators.removeTransformationButton(0)).click();
    this.saveLov();
  }

  public checkLovUpdated(
    lovId: string,
    lang: string,
    expectedTranslation: string
  ): void {
    cy.wait("@updateLov").then((updateIntercept) => {
      expect(updateIntercept.response.statusCode).to.eq(200);
    });

    this.interceptLovDetails(lovId);

    cy.wait("@getLovDetails").then((getIntercept) => {
      const updatedLov = getIntercept.response.body;

      const translation = updatedLov.options[0].translations.find(
        (t) => t.lang === lang
      );
      expect(translation?.properties?.name).to.eq(expectedTranslation);
      const expectedTransformations = LOV_NUMBER_OF_TRANSFORMATIONS - 1;
      expect(updatedLov.options[0].mappings.length).to.eq(
        expectedTransformations
      );
    });
  }

  public saveLov(): any {
    cy.get(lovAndUnitsLocators.saveLovButton).click();
  }

  public checkCreatedLOVWithUniqueName(data: any): void {
    expect(data.body).to.have.property("id");
    this.checkStatusCode(data, 201);
  }

  public checkLOVHasInvalidParams(data: any): void {
    expect(data.body.invalidParams.length).to.be.gt(0);
    this.checkStatusCode(data, 400);
  }

  public checkGetLOV(data: any): void {
    const languagesCount = Object.keys(Languages).length;

    expect(data.body).to.have.property("id");

    for (const option of data.body.options) {
      expect(option.translations).to.have.lengthOf(languagesCount);
      for (const translation of option.translations) {
        expect(translation.properties.name).to.be.not.empty;
      }

      expect(option.mappings).to.have.lengthOf(LOV_NUMBER_OF_TRANSFORMATIONS);
      for (const mapping of option.mappings) {
        expect(mapping.name).to.be.not.empty;
      }
    }

    this.checkStatusCode(data);
  }

  public searchLov(lovName: string): void {
    cy.get(lovAndUnitsLocators.inputLov)
      .type(lovName)
      .should("have.value", lovName);
    cy.get(productUploadLocators.searchButton).should("be.visible").click();
  }

  public performSearchInSidebar(searchKeyword: string): void {
    cy.get(lovAndUnitsLocators.searchWrapper).within(() => {
      cy.get(lovAndUnitsLocators.searchLovOption)
        .type(searchKeyword)
        .should("have.value", searchKeyword);

      cy.get(productUploadLocators.searchButton).should("be.visible").click();
    });
  }

  public validateSidebarContent(detailsResponse: any): void {
    cy.get(lovAndUnitsLocators.previewGroupDetails).within(() => {
      cy.get(lovAndUnitsLocators.previewValueGroupName)
        .contains(detailsResponse.translations[0].properties.name)
        .should("exist");

      const marketTranslation = detailsResponse.translatable
        ? translatableValues.TRANSLATION_REQUIRED
        : translatableValues.NO_TRANSLATION_REQUIRED;
      cy.get(lovAndUnitsLocators.previewValueTranslation)
        .contains(marketTranslation)
        .should("exist");

      cy.get(lovAndUnitsLocators.previewTotalValues)
        .contains(detailsResponse.options.length.toString())
        .should("exist");

      const totalTransformations = detailsResponse.options.reduce(
        (count, option) => count + option.mappings.length,
        0
      );
      cy.get(lovAndUnitsLocators.previewTotalTransformations)
        .contains(totalTransformations.toString())
        .should("exist");
    });
  }

  public validateSearchedOptionContent(
    searchText: string,
    options: any[]
  ): void {
    const matchingOptions = options.filter((option) =>
      option.name.toLowerCase().includes(searchText.toLowerCase())
    );

    matchingOptions.forEach((matchingOption) => {
      const filteredMappings = matchingOption.mappings.filter((mapping) =>
        mapping.name.toLowerCase().includes(searchText.toLowerCase())
      );
      const expectedMappingCount = filteredMappings.length;

      cy.get(lovAndUnitsLocators.previewOptionValue)
        .should("have.class", "mm-highlight")
        .and("contain.text", searchText);

      cy.get(lovAndUnitsLocators.previewTransformationsCount).should(
        "contain.text",
        `${expectedMappingCount}`
      );

      cy.get(lovAndUnitsLocators.previewOptionValue).click();
      cy.get(lovAndUnitsLocators.previewMappingName).should(
        "have.length",
        expectedMappingCount
      );

      filteredMappings.forEach((mapping: any, index: number) => {
        cy.get(lovAndUnitsLocators.previewMappingName)
          .eq(index)
          .should("contain.text", mapping.name);
      });
    });
  }

  public updatePaginator(optionIndex: number = 0) {
    cy.get(lovAndUnitsLocators.tablePaginationSelect)
      .should("be.visible")
      .select(optionIndex);
  }

  private addValueCard(baseValue: string): void {
    cy.get(lovAndUnitsLocators.addValueButton).click();
    cy.get(lovAndUnitsLocators.newValueInput)
      .invoke("val", "")
      .trigger("change")
      .type(`${baseValue}_${LOV_SUFFIXES.VALUE}`);
    cy.get(lovAndUnitsLocators.addValueConfirmationButton).click();
  }

  private fillTranslationInputs(baseValue: string): void {
    Object.values(Languages)
      .filter((lang) => lang !== Languages.EN)
      .forEach((lang) => {
        cy.get(lovAndUnitsLocators.valueInputTranslation(lang)).type(
          `${baseValue}_${lang}`
        );
      });
  }

  private itemHasValue(property, searchFilter): boolean {
    const regExp = new RegExp(searchFilter, "gi");
    return Array.isArray(property)
      ? property.some((value: string) => regExp.test(value))
      : regExp.test(property);
  }

  private checkLovTranslations(translations): void {
    translations.forEach((translation) => {
      expect(translation).to.have.all.keys(lovDetailsKeys.translations);
      expect(translation.properties).to.have.all.keys(
        lovDetailsKeys.properties
      );
    });
  }

  private fillTransformationInputs(baseValue: string) {
    for (let i = 0; i < LOV_NUMBER_OF_TRANSFORMATIONS; i++) {
      cy.get(lovAndUnitsLocators.addTransformationButton).click();
      cy.get(lovAndUnitsLocators.transformationInput(i))
        .click()
        .type(`${baseValue}_${LOV_SUFFIXES.TRANSFORMATION}_${i}`);
    }
  }
}

export const lovAndUnitsPage = new LovAndUnitsPage();
