import { disputeListLocators } from '../../../locators/employee/dispute-management/dispute-list-locators';
import {
  orderNumber,
  orderNumberFilter,
  orderNumberFilterLabel,
  ordersPageURL,
  statusFilter,
  statusFilterLabel,
  statusWaitingForBuyerResponse,
  waitingForSellerResponseFilter,
  DisputeStages,
  DisputeStatus,
  disputeDetailsKeys,
  disputeItemKeys,
  disputeMessageKeys,
  DEBOUNCE_TIME_DISPUTE_DETAILS,
  escalateMessage,
  statusResolved,
  respondMessage,
} from '../../../constants/dispute-management';
import { WAITING_TIME_TO_PAGE_LOAD } from '../../../constants/page-constants';
import { disputeDetailsLocators } from '../../../locators/employee/dispute-management/dispute-details-locators';
import { DisputeDetails } from '../../../interfaces/dispute-details';
import { DEBOUNCE_TIME, INTERNAL_SERVER_ERROR_STATUS_CODE } from '../../../constants/shared';
import { disputeApi } from '../../../api/dispute-management';

export class DisputePage {

  caseDetails: DisputeDetails = null;

  checkElementsPresent() {
    cy.get(disputeListLocators.disputeTable).should('be.visible');
  }

  checkIfPrefilteredByWaitingForSellerResponseStatus() {
    return cy.url().should('include', waitingForSellerResponseFilter);
  }

  clearFilter() {
    cy.get(disputeListLocators.clearAllFilters).should('be.visible').click();
    cy.url().should('not.contain', statusFilter);
    cy.url().should('not.contain', orderNumberFilter);
    cy.get(disputeListLocators.clearAllFilters).should('not.exist');
    cy.wait(DEBOUNCE_TIME);
  }

  checkIfViewDetailsButtonExists() {
    cy.get(disputeListLocators.actionColumn).should('exist');
    cy.get(disputeListLocators.actionButton).should('exist');
  }

  clickViewDetailsButton() {
    cy.get(disputeListLocators.actionButton).first().should('be.visible').click();
    cy.get(disputeListLocators.disputeDetails, {timeout: WAITING_TIME_TO_PAGE_LOAD}).should('be.visible');
    cy.go('back');
  }

  clickOrderNumber() {
    cy.get(disputeListLocators.orderNumber).first().should('be.visible')
      .then((anchor) => {
        expect(anchor).to.have.attr('target', '_blank');
        anchor.attr('target', '_self');
      }).click();
    cy.url().should('include', ordersPageURL);
    cy.go('back');
  }

  filterByOrderNumber() {
    this.openDropDownMenu();
    cy.get(disputeListLocators.filterTypeOption).contains(orderNumberFilterLabel).first().click();
    cy.get(disputeListLocators.searchInput).type(orderNumber);
    cy.wait(DEBOUNCE_TIME);
    cy.url().should('include', orderNumberFilter);
    cy.get(disputeListLocators.matColumnOrderNumber)
      .contains(orderNumber)
      .should('have.length.at.least', 1);
  }

  filterByOrderStatus() {
    this.openDropDownMenu();
    cy.get(disputeListLocators.filterTypeOption).contains(statusFilterLabel).first().click();
    cy.get(disputeListLocators.searchDropDown).click();
    cy.get(disputeListLocators.searchDropdownOption).contains(statusWaitingForBuyerResponse).first().click();
    cy.wait(DEBOUNCE_TIME);
    cy.get(disputeListLocators.matColumnStatus).should('be.visible')
      .each((column) => cy.wrap(column)
        .should('contain', statusWaitingForBuyerResponse));
    cy.url().should('include', statusFilter);
  }

  openDropDownMenu() {
    cy.get(disputeListLocators.filterTypeDropDown)
      .click();
  }

  goToDetailsPage() {
    const API_DISPUTE_DETAILS = '**/paypal/disputes/*';
    cy.intercept(API_DISPUTE_DETAILS).as('caseDetails');
    this.getClickableDispute().click();
    cy.wait('@caseDetails').then(interception => {
      this.caseDetails = interception.response.body;
      expect(interception.response.statusCode).to.be.equal(200);
      cy.get(disputeDetailsLocators.disputeDetails).should('be.visible');
    })
  }

  getClickableDispute() {
    return cy.get(disputeDetailsLocators.detailsButton).eq(0)
  }

  checkIfConversationTabIsEnabledOnLoad() {
    cy.url().should('contain', 'conversation');
    cy.get(disputeDetailsLocators.conversationTab).invoke('attr', 'class').should('contain', 'active')
    cy.get(disputeDetailsLocators.caseDetailsTab).invoke('attr', 'class').should('not.contain', 'active')
  }

  checkIfConversationElementsArePresent() {
    if (this.caseDetails.stage === DisputeStages.INQUIRY && this.caseDetails.status !== DisputeStatus.RESOLVED) {
      cy.get(disputeDetailsLocators.conversationInfo).should('be.visible');
      cy.get(disputeDetailsLocators.escalateIssue).should('be.visible');
    } else {
      cy.get(disputeDetailsLocators.escalateIssue).should('not.exist');
      cy.get(disputeDetailsLocators.conversationInfo).should('not.exist');
    }
    cy.get(disputeDetailsLocators.conversationDialogue).should('be.visible');
  }

  goToCaseDetails() {
    cy.get(disputeDetailsLocators.caseDetailsTab).click();
    cy.get(disputeDetailsLocators.caseDetails).should('be.visible');
  }

  goToOrderDetails() {
    cy.get(disputeDetailsLocators.orderLink).should('be.visible')
      .then((anchor) => {
        expect(anchor).to.have.attr('target', '_blank');
        anchor.attr('target', '_self');
      }).click();
    cy.url().should('include', ordersPageURL);
    cy.go('back');
  }

  public checkDisputeDetails(data: any): void {
    assert.isArray(data.items);
    assert.isArray(data.messages);
    disputeDetailsKeys.forEach(detailKey => {
      expect(data).to.have.property(detailKey);
    });
    data.items.forEach((item) => {
      disputeItemKeys.forEach(itemKey => {
        expect(item).to.have.property(itemKey);
      });
    });
    data.items.forEach((item) => {
      disputeItemKeys.forEach(itemKey => {
        expect(item).to.have.property(itemKey);
      });
    });
    data.messages.forEach((message, index) => {
      disputeMessageKeys.forEach(itemKey => {
        expect(message).to.have.property(itemKey);
      });
      assert.isArray(message.documents);
    });
  }

  escalateIssueToPayPal(): void {
    cy.wait(DEBOUNCE_TIME_DISPUTE_DETAILS);
    cy.wait('@disputeDetails').then(({response}) => {
      const details = response.body;
      if (details.status !== DisputeStatus.RESOLVED && details.stage === DisputeStages.INQUIRY) {
        this.clickOnEscalateAndSubmitTheForm()
      }
    });
  }

  private clickOnEscalateAndSubmitTheForm(): void {
    cy.intercept('POST', '**/escalate').as('escalate');
    cy.get(disputeDetailsLocators.escalateIssue).click();
    cy.get(disputeDetailsLocators.escalateMessage).clear().type(escalateMessage);
    cy.get(disputeDetailsLocators.escalateModalButton).click();
    cy.wait('@escalate').then(({response}) => {
      if (response.body.status === INTERNAL_SERVER_ERROR_STATUS_CODE) {
        cy.get(disputeDetailsLocators.escalateError).should('be.visible');
      } else {
        cy.get(disputeDetailsLocators.escalateModal).should('not.exist');
        cy.get(disputeDetailsLocators.escalateIssue).should('not.exist');
      }
    })
  }

  getTheFirstUnresolvedDisputeId() {
    return new Cypress.Promise((resolve, reject) => {
      disputeApi.getDisputeList({offset: 0, limit: 50});
      cy.get('@disputeList').then((response: any) => {
        const disputeId = response.body.items.find(dispute => dispute['status'] !== statusResolved)?.disputeId;
        if (disputeId) {
          resolve(disputeId);
        } else reject(null);
      });
    });
  }

  respondToDispute() {
    cy.wait(DEBOUNCE_TIME_DISPUTE_DETAILS);
    cy.wait('@disputeDetails').then(({response}) => {
      const details = response.body;
      cy.log(details.caseId);
      if (details.stage === DisputeStages.INQUIRY) {
        this.checkResponding();
      } else {
        this.checkIfRespondFormIsDisabled();
      }
    });
  }

  private checkResponding() {
    this.checkIfElementsAreEnabled();
    cy.intercept('**/api/v1/paypal/disputes/*/respond').as('respondToDispute');
    cy.get(disputeDetailsLocators.respondFormSubmitButton).click();
    cy.wait('@respondToDispute').then(({response}) => {
      if (response.body.status === INTERNAL_SERVER_ERROR_STATUS_CODE) {
        cy.get(disputeDetailsLocators.respondError).should('be.visible');
      } else {
        this.checkResponseAndDomAfterResponding();
      }
    });
  }

  private checkResponseAndDomAfterResponding() {
    cy.wait(DEBOUNCE_TIME_DISPUTE_DETAILS)
    cy.get(disputeDetailsLocators.respondError).should('not.exist');
    cy.get(disputeDetailsLocators.chatBlocks).last();
    cy.get(disputeDetailsLocators.chatBlocks).last()
      .within(() => {
        cy.get(disputeDetailsLocators.messageSent).should('contain.text', respondMessage);
      })
  }

  private checkIfElementsAreEnabled(){
    cy.get(disputeDetailsLocators.respondFormInput).should('not.be.disabled');
    cy.get(disputeDetailsLocators.respondFormInput).clear().type(respondMessage);
    cy.get(disputeDetailsLocators.respondFormInput).should('not.be.disabled');
    cy.get(disputeDetailsLocators.respondFileAttachInput).should('not.have.attr', 'class', 'disabled');
    cy.get(disputeDetailsLocators.respondForm).should('not.have.attr', 'class', 'disabled');
  }

  private checkIfRespondFormIsDisabled() {
    cy.get(disputeDetailsLocators.respondFormInput)
      .should('be.visible').should('be.disabled');
    cy.get(disputeDetailsLocators.respondFileAttachInput).should('be.visible').should('have.class', 'disabled');
    cy.get(disputeDetailsLocators.respondForm).should('be.visible').should('have.class', 'disabled');
  }

}

export const disputePage = new DisputePage();
