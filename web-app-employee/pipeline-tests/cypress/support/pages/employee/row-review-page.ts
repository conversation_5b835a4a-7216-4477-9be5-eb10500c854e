import { MARKET_KEY } from "../../constants/local-storage-constants";
import { apiEndpoints } from "../../endpoints";
import { rowReviewLocators } from "../../locators/employee/row-review-locators";
import { RowStatus } from "../../constants/pim-enums";
import {
  HEADER_LIST,
  rejectRowReasonMessage,
} from "../../constants/pim-upload-constants";
import { checkStatusCode } from "../../helpers";

export class RowReviewPage {
  public checkRows(
    expectedStatus: RowStatus,
    expectedRejectReasons: string[] | null = null
  ): void {
    this.rowsLoaded().then((interception) => {
      const rows = interception.response.body.items;
      rows.forEach((row: any) => {
        expect(row).to.have.property("status");
        expect(row).to.have.property("rejectReason");
        expect(row.status).to.eq(expectedStatus);
        if (expectedRejectReasons !== null) {
          expectedRejectReasons.forEach((expectedRejectReason) => {
            expect(row.rejectReason).to.contain(expectedRejectReason);
          });
        }
      });
    });
  }

  public approveRow(): void {
    cy.get(rowReviewLocators.rowCheckBox).eq(0).click();

    this.interceptApproveRows();
    this.interceptGetUploadReviewRows();
    cy.get(rowReviewLocators.approveProductsButton).click();
    this.checkSuccessfulResponse("@approveRows");
  }

  public approveRows(): void {
    cy.wait(5000);
    cy.get(rowReviewLocators.rowCheckBox).each(($checkbox) => {
      cy.wrap($checkbox).click();
    });

    this.interceptApproveRows();
    this.interceptGetUploadReviewRows();
    cy.get(rowReviewLocators.approveProductsButton).click();
  }

  public rejectRow(): void {
    cy.wait(5000);
    cy.get(rowReviewLocators.rowCheckBox).eq(0).click();
    cy.get(rowReviewLocators.rejectProductsButton).click();

    cy.get(rowReviewLocators.rejectionModalTextArea).type(
      rejectRowReasonMessage
    );

    this.interceptRejectRows();
    this.interceptGetUploadReviewRows();
    cy.get(rowReviewLocators.rejectionModalRejectButton).click();
    this.checkSuccessfulResponse("@rejectRows");
  }

  public rejectAllRows(): void {
    this.rowsLoaded().then(() => {
      this.interceptRejectRows();
      cy.get(rowReviewLocators.allRowsCheckBox).click();
      cy.get(rowReviewLocators.rejectProductsButton).click();
      cy.get(rowReviewLocators.rejectionModalTextArea).type(
        rejectRowReasonMessage
      );
      cy.get(rowReviewLocators.rejectionModalRejectButton).click();

      this.checkSuccessfulResponse("@rejectRows");
    });
  }

  public interceptGetUploadReviewRows(): void {
    cy.getLocalStorage(MARKET_KEY).then((value) => {
      const market = JSON.parse(value);
      cy.intercept(
        "GET",
        `**${apiEndpoints.getUploadReviewRows(market.id)}*`
      ).as("getUploadReviewRows");
    });
  }

  public checkRowsStructure(response: any): void {
    checkStatusCode(response);

    const data = response.body;

    expect(data).to.have.property("totalCount");
    expect(data).to.have.property("items");
    expect(data).to.have.property("headers");

    assert.isArray(data.items);
    assert.isArray(data.headers);

    this.checkHeadersStructure(data.headers);

    data.items.forEach((row) => {
      expect(row).to.have.property("columns");
      expect(row).to.have.property("rowId");
      expect(row).to.have.property("status");
      expect(row).to.have.property("isNewProduct");
    });
  }

  public saveDrafts(message: string): void {
    this.rowsLoaded().then(() => {
      this.interceptDraft();

      cy.get(rowReviewLocators.rowCheckBox)
        .should("be.visible")
        .each(($checkbox) => {
          cy.wrap($checkbox).click();
        });
      cy.get(rowReviewLocators.rejectProductsButton).click();
      cy.get(rowReviewLocators.rejectionModalTextArea).type(message);
      cy.get(rowReviewLocators.rejectionModalSaveDraftButton).click();

      this.checkSuccessfulResponse("@draft");
    });
  }

  public filterByHasDraft(): void {
    this.rowsLoaded().then(() => {
      cy.get(rowReviewLocators.draftFilterToggle).should("be.visible").click();
    });
  }

  public navigateToStatusTab(status: RowStatus): void {
    this.rowsLoaded().then((interception) => {
      checkStatusCode(interception.response);
      cy.get(rowReviewLocators.filterTab(status)).should("be.visible").click();
    });
  }

  public rowsLoaded(): Cypress.Chainable {
    return cy.wait("@getUploadReviewRows").then((interception) => {
      checkStatusCode(interception.response);
      return interception;
    });
  }

  private interceptRejectRows(): void {
    cy.getLocalStorage(MARKET_KEY).then((value) => {
      const market = JSON.parse(value);
      cy.intercept("POST", `**${apiEndpoints.rejectRows(market.id)}`).as(
        "rejectRows"
      );
    });
  }

  private interceptApproveRows(): void {
    cy.getLocalStorage(MARKET_KEY).then((value) => {
      const market = JSON.parse(value);
      cy.intercept("POST", `**${apiEndpoints.approveRows(market.id)}`).as(
        "approveRows"
      );
    });
  }

  private checkHeadersStructure(headers): void {
    let headerNames = [];
    headers.forEach((header) => {
      expect(header).to.have.property("column");
      expect(header).to.have.property("value");
      headerNames.push(header.value);
    });

    HEADER_LIST.forEach((value) => {
      expect(value).to.be.oneOf(headerNames);
    });
  }

  private interceptDraft(): void {
    cy.getLocalStorage(MARKET_KEY).then((value) => {
      const market = JSON.parse(value);
      cy.intercept("PATCH", `**${apiEndpoints.draftRows(market.id)}`).as(
        "draft"
      );
    });
  }

  private checkSuccessfulResponse(alias: string): void {
    cy.wait(alias).should((interception) => {
      checkStatusCode(interception.response, 200);
    });
  }
}

export const rowReviewPage = new RowReviewPage();
