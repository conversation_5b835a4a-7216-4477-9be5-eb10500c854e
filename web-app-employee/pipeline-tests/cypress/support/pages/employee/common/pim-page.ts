import { MARKET_KEY } from "../../../constants/local-storage-constants";
import { pimPageLocators } from "../../../locators/employee/pim-page.locators";
import { marketNames } from "../../../constants/pim-constants";

export class PimPage {
  public checkMarketInLocalStorage(marketCode: string): void {
    cy.getLocalStorage(MARKET_KEY).then((value) => {
      const market = JSON.parse(value);
      expect(market["name"]).to.eq(marketNames[marketCode]);
    });
  }

  public changeMarketInPicker(marketCode: string): void {
    cy.get(pimPageLocators.marketDropdown).should("be.visible").click();

    cy.get(".ng-dropdown-panel-items")
      .children(".scrollable-content")
      .children(".ng-option")
      .contains(marketNames[marketCode])
      .click();
  }

  public checkMarketPickerList(data: any): void {
    cy.get(pimPageLocators.marketDropdown).should("be.visible").click();

    data.items.forEach((market) => {
      const countryFlagId = `country-flag-${market.id.toLowerCase()}`;

      cy.wait(100);
      cy.get(".ng-option.ng-star-inserted")
        .find(`img[src*="${countryFlagId}"]`)
        .scrollIntoView()
        .should("be.visible")
        .should("exist");
    });

    cy.get(pimPageLocators.marketDropdown).click();
  }
}
