import { DISCOUNT_CODE_PAGE_LOAD_TIME, FilterType, redemptionListKeys } from '../../../constants/discount-code';
import { dashboardLocators } from '../../../locators/employee/discount-code/dashboard-locators';

export class Redemption{

  public checkDiscountCodeRedemptionList(data: any): void {
    assert.isArray(data.items);
    data.items.forEach((item) => {
      redemptionListKeys.forEach(itemKey => {
        expect(item).to.have.property(itemKey);
      });
    });
  }

  checkElementsPresent() {
    cy.wait(DISCOUNT_CODE_PAGE_LOAD_TIME);
    cy.get(dashboardLocators.redemptionTable).should('be.visible');
    cy.get(dashboardLocators.redemptionFilters).should('be.visible');
    cy.get(dashboardLocators.redemptionPaginator).scrollIntoView().should('be.visible');
  }
}
export const redemptionPage =  new Redemption();
