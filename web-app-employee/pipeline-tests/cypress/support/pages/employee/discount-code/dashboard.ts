import {
  DISCOUNT_CODE_PAGE_LOAD_TIME,
  discountCodeListKeys,
  getStartAndEndDates,
  FilterType,
  statusDeactivated,
  discountCodeDetailsKeys,
} from '../../../constants/discount-code';
import { DEBOUNCE_TIME } from '../../../constants/shared';
import { dashboardLocators } from '../../../locators/employee/discount-code/dashboard-locators';

export class Dashboard {
  activeCode;
  newEndDate;

  public checkDiscountCodeList(data: any): void {
    assert.isArray(data.items);
    data.items.forEach((item) => {
      discountCodeListKeys.forEach(itemKey => {
        expect(item).to.have.property(itemKey);
      });
    });
  }

  checkElementsPresent() {
    cy.wait(DISCOUNT_CODE_PAGE_LOAD_TIME);
    cy.get(dashboardLocators.overviewTable).should('be.visible');
    cy.get(dashboardLocators.overviewFilter).should('be.visible');
    cy.get(dashboardLocators.discountTabs).should('be.visible');
    cy.get(dashboardLocators.redemptionTab).should('be.visible');
    cy.get(dashboardLocators.overviewTab).should('be.visible');
    cy.get(dashboardLocators.createDiscountCodeButton).should('be.visible');
    cy.get(dashboardLocators.overviewPagination).scrollIntoView().should('be.visible');
  }

  filter(filterLocator, label, input, urlQueryParam, column, containsText, type) {
    cy.get(filterLocator).scrollIntoView();
    this.openDropDownMenu();
    cy.get(dashboardLocators.filterTypeOption).contains(label).first().click();
    if (type === FilterType.INPUT) {
      cy.get(dashboardLocators.searchInput).type(input);
    } else if (type === FilterType.DROPDOWN) {
      cy.get(dashboardLocators.searchDropDown).click();
      cy.get(dashboardLocators.searchDropdownOption).contains(input).first().click();
      cy.get('body').click(0, 0);
    } else {
      return;
    }
    cy.wait(DISCOUNT_CODE_PAGE_LOAD_TIME);
    cy.url().should('include', urlQueryParam);
    cy.get(column)
      .first()
      .contains(containsText)
      .should('have.length.at.least', 1);
  }

  filterByDateRange(queryString, element, isRedemption?) {
    cy.url().then(url => {
      cy.visit(`${url}${queryString}`);
    });
    cy.wait(DISCOUNT_CODE_PAGE_LOAD_TIME);
    const {startDate, endDate} = getStartAndEndDates();
    cy.get(element).first().should(($column) => {
      const text = $column.text();
      const start = Date.parse(startDate);
      const end = Date.parse(endDate);
      const columnDate = this.getColumnDate(text, isRedemption);
      const dateComparison = columnDate.valueOf() >= start.valueOf() && columnDate.valueOf() <= end.valueOf();
      expect(dateComparison).to.be.true;
    })
  }

  getColumnDate(text, isRedemption) {
    if (isRedemption) {
      const dateString = String(text).trim().split(' ')[0].split('.')
      return Date.parse(`${dateString[1]}.${dateString[0]}.${dateString[2]}`);
    }
    return Date.parse(text);
  }

  clearFilter() {
    cy.get(dashboardLocators.clearAllFilters).scrollIntoView().should('be.visible').click();
    cy.get(dashboardLocators.clearAllFilters).should('not.exist');
    cy.wait(DEBOUNCE_TIME);
  }

  private openDropDownMenu() {
    cy.get(dashboardLocators.filterTypeDropDown)
      .click();
  }

  deactivateDiscountCode() {
    cy.wait(DISCOUNT_CODE_PAGE_LOAD_TIME);
    cy.get(dashboardLocators.mmStatusColumn)
      .invoke('text')
      .then((text) => {
        if (text.includes('Active')) {
          cy.get(dashboardLocators.mmStatusColumn).contains('Active').first().scrollIntoView().parents('tr')
            .within(() => {
              cy.get(dashboardLocators.overviewMenuButton).scrollIntoView().should('be.visible').click({force: true});
              cy.get(dashboardLocators.actionsMenu);
              cy.get(dashboardLocators.code).invoke('text').then(text => {
                this.activeCode = text;
              });
            }).then(() => {
            cy.get(dashboardLocators.deactivateDiscountCodeButton).should('exist').click();
            cy.wait(DISCOUNT_CODE_PAGE_LOAD_TIME);
            cy.get(dashboardLocators.mmCodeColumn).contains(this.activeCode).first().scrollIntoView().parents('tr')
              .within(() => {
                cy.get(dashboardLocators.status).should('include.text', statusDeactivated);
              })
          });
        }
      });
  }

  viewDetails() {
    const API_DISCOUNT_DETAILS = '**/api/v1/discount-code/*';
    cy.get(dashboardLocators.overviewMenuButton).first().scrollIntoView().click({force: true});
    cy.intercept(API_DISCOUNT_DETAILS).as('discountDetails');
    cy.get(dashboardLocators.viewDetailsDiscountCodeButton).scrollIntoView().click();
    cy.wait('@discountDetails').then(interception => {
      expect(interception.response.statusCode).to.be.equal(200);
      cy.get(dashboardLocators.discountCodeForm).should('be.visible');
    })
  }

  checkDiscountCodeDetails(data) {
    if(data.status=== 200){
      discountCodeDetailsKeys.forEach(itemKey => {
        expect(data).to.have.property(itemKey);
      });
    }
  }

  extendDiscountCode() {
    const API_DISCOUNT_EXTEND = '**/' +
      'api/v1/discount-code/extend/*'
    cy.wait(DISCOUNT_CODE_PAGE_LOAD_TIME);
    cy.get(dashboardLocators.mmStatusColumn)
      .invoke('text')
      .then((text) => {
        if (text.includes('Active')) {
          cy.get(dashboardLocators.mmStatusColumn).contains('Active').first().scrollIntoView().parents('tr')
            .within(() => {
              cy.get(dashboardLocators.overviewMenuButton).scrollIntoView().should('be.visible').click({force: true});
              cy.get(dashboardLocators.actionsMenu);
              cy.get(dashboardLocators.code).invoke('text').then(text => {
                this.activeCode = text;
              });
            }).then(() => {
            cy.get(dashboardLocators.extendDiscountCodeButton).should('exist').click();
            cy.wait(300);
            cy.get(dashboardLocators.currentEndDate).invoke('text').then(text => {
              const date =  new Date(text);
              date.setDate(date.getDate()+1);
              this.newEndDate = date;
              const inputDate = `${date.getMonth()+1}.${date.getDate()}.${date.getFullYear()}`
              this.newEndDate.setHours(0,0,0,0);
              cy.get(dashboardLocators.satDatePickerInput).clear({force:true});
              cy.get(dashboardLocators.satDatePickerInput).type(inputDate, {force:true});
              cy.get(dashboardLocators.extendCodeTimeSelector).click();
              cy.get(dashboardLocators.extendValidityTimeSelection).first().click();
              cy.get(dashboardLocators.extendCodeMessage).should('be.visible');
              cy.get(dashboardLocators.extendCodeFormError).should('not.exist');
              cy.intercept(API_DISCOUNT_EXTEND).as('discountExtend');
              cy.get(dashboardLocators.extendDiscountCodeSubmitButton).click();
              cy.wait('@discountExtend').then(interception => {
                if(interception.response.statusCode === 204 ) {
                  cy.get(dashboardLocators.extendCodeNwError).should('not.exist');
                  cy.wait(DISCOUNT_CODE_PAGE_LOAD_TIME);
                  cy.get(dashboardLocators.mmCodeColumn).contains(this.activeCode).first().scrollIntoView().parents('tr')
                    .within(() => {
                      cy.get(dashboardLocators.endDate).invoke('text')
                        .then((text) => {
                          const date = new Date(text).setHours(0, 0, 0, 0);
                          expect(date).eq(this.newEndDate);
                        })
                    });
                }
                else if(interception.response.statusCode === 400 ){
                  cy.get(dashboardLocators.extendCodeNwError).should('be.visible');
                }
              })
            });
          });
        }
      });
  }
}

export const dashboardPage = new Dashboard();
