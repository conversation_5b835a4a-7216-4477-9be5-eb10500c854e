import {
  FileStatus,
  ParseFileStatus,
  ProcessingStatus,
  UploadRulesStatus,
  UploadStatus,
  UploadStatusFilter,
} from "../../../support/constants/pim-enums";
import { apiEndpoints } from "../../../support/endpoints";
import { productUploadLocators } from "../../locators/employee/product-upload-locators";
import { MARKET_KEY } from "../../constants/local-storage-constants";
import {
  PRODUCT_UPLOAD_ENDPOINT_TIMEOUT,
  REJECT_FILE_REASON_MESSAGE,
  rejectRowReasonMessages,
  UPLOAD_PARSING_STATISTIC_ENDPOINT_TIMEOUT,
} from "../../constants/pim-upload-constants";
import { checkStatusCode } from "../../helpers";
import { rowReviewPage } from "./row-review-page";
import {
  PRODUCT_INVALID_IMAGE_FORMAT,
  PRODUCT_INVALID_IMAGE_FORMAT_ID,
  PRODUCT_OUT_OF_SCOPE,
  PRODUCT_OUT_OF_SCOPE_ID,
} from "../../constants/pim-report-errors";

import {
  germanMarketCode,
  IN_REVIEW_FILTERS,
  MAX_AUTOREFRESH_ATTEMPTS,
  nonAssignedLabel,
  REVIEWED_FILTERS,
} from "../../constants/pim-constants";
import { PimPage } from "./common/pim-page";
import { StatsValues } from "../../interfaces/pim-product-upload";
import {
  DATE_PROCESSED_INDEX,
  reportHeaders,
} from "../../constants/pim-upload-report-constants";
import Chainable = Cypress.Chainable;

export class ProductDataUploadsPage extends PimPage {
  public interceptParsing(fileName: string): void {
    this.getFileRow(fileName).within(() => {
      this.interceptStartUploadParsing();
      this.interceptGetUploadParsingStatistic();
      this.interceptProductUploads();
    });
  }

  public goToRowReviewPage(fileName: string): void {
    this.getFileRow(fileName).within(() => {
      rowReviewPage.interceptGetUploadReviewRows();
      cy.get(productUploadLocators.reviewUploadButton)
        .should("be.visible")
        .invoke("removeAttr", "target")
        .click({ force: true });
    });
  }

  public approveUploadFile(fileName: string): void {
    this.getFileRow(fileName).within(() => {
      this.interceptApproveFile();
      cy.get(productUploadLocators.approveUploadButton)
        .should("be.visible")
        .click({ force: true });
    });
  }

  public rejectUploadFile(fileName: string): void {
    this.getFileRow(fileName).within(() => {
      cy.get(productUploadLocators.rejectUploadButton)
        .should("be.visible")
        .click({ force: true });
    });

    cy.get('textarea[name="rejectionReason"]').type(REJECT_FILE_REASON_MESSAGE);
    cy.get('[test-target="dialog-apply-btn"]').click();
  }

  public rejectUploadByDialog(fileName: string): void {
    this.getFileRow(fileName).within(() => {
      cy.get(productUploadLocators.rejectUploadButton)
        .should("be.visible")
        .click({ force: true });
    });

    this.searchRejectError(PRODUCT_OUT_OF_SCOPE, PRODUCT_OUT_OF_SCOPE_ID);
    this.searchRejectError(
      PRODUCT_INVALID_IMAGE_FORMAT,
      PRODUCT_INVALID_IMAGE_FORMAT_ID
    );

    cy.get('[test-target="dialog-apply-btn"]').click();
  }

  public interceptProductUploads(): void {
    cy.intercept("GET", `**${apiEndpoints.productUploads}*`).as(
      "getProductUploads"
    );
  }

  public checkProcessingFileStatus(file, status: UploadStatus): void {
    expect(file.status.processing.id).to.eq(status);
  }

  public checkAutoReviewUpload(upload): void {
    expect(upload.isAutoProcessed).to.eq(true);
    this.getFileRow(upload.fileName).within(() => {
      cy.get(productUploadLocators.autoReviewIcon).should("be.visible");
    });
  }

  public checkParsingFileStatus(file, status: ParseFileStatus): void {
    expect(file.status.parse.id).to.eq(status);
  }

  public checkApprovalFileStatus(file, status: FileStatus): void {
    expect(file.status.approval.id).to.eq(status);
  }

  public convertCSVToJSON(csv): any {
    const lines = csv.split("\n");
    const properties = lines[0].split(";");
    let products = [];

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      let product = {};
      let currentProduct = lines[i].split(";");

      for (let j = 0; j < properties.length; j++) {
        product[properties[j]] = currentProduct[j];
      }
      products.push(product);
    }
    return products;
  }

  public checkProductGtin(productInDB, uploadedProduct): void {
    expect(productInDB).to.have.property("mid");
    expect(productInDB["gtin"]).to.equal(uploadedProduct["GTIN"]);
  }

  public checkProductName(productInDB, uploadedProduct): void {
    expect(productInDB["name"]).to.equal(uploadedProduct["Product name"]);
  }

  public checkProductManufacturer(productInDB, uploadedProduct) {
    expect(productInDB["manufacturer"]).to.equal(
      uploadedProduct["Manufacturer"]
    );
  }

  public checkProductEmptyGtin(productInDB, uploadedProduct): void {
    expect(productInDB["gtin"]).to.eq(null);
    expect(productInDB).to.have.property("mid");
    this.checkProductManufacturer(productInDB, uploadedProduct);
  }

  public checkErrorReport(errorReport, expectedErrorMessage): void {
    expect(errorReport["Status"]).to.eq("rejected");
    expect(errorReport['"Error Report"']).to.include(expectedErrorMessage);
  }

  public checkUploadParsingStatistic(uploadId: string, response: any): void {
    checkStatusCode(response);

    const data = response.body;
    expect(data).to.have.property("uploadId");
    expect(data).to.have.property("fileSize");
    expect(data).to.have.property("parsedSize");
    expect(data).to.have.property("status");

    expect(data.uploadId).to.eq(uploadId);
    expect(data.status).to.oneOf([
      ParseFileStatus.PARSING_ERROR,
      ParseFileStatus.PARSING_IN_PROGRESS,
      ParseFileStatus.PARSING_COMPLETED,
    ]);
    expect(data.fileSize).to.be.gt(0);
  }

  public checkParsedFile(file: any): void {
    this.checkProcessingFileStatus(file, UploadStatus.IN_REVIEW);
    this.checkParsingFileStatus(file, ParseFileStatus.PARSING_COMPLETED);
    this.checkApprovalFileStatus(file, FileStatus.IN_REVIEW);
  }

  public getFileRow(fileName: string): Chainable<JQuery<HTMLElement>> {
    return cy
      .get(productUploadLocators.downloadCsvFileLink)
      .contains(fileName)
      .scrollIntoView()
      .should("be.visible")
      .closest("tr");
  }

  public navigateToReviewedTab() {
    cy.get(productUploadLocators.reviewedTab).click({ force: true });
  }

  public checkProcessingStatus(
    fileName: string,
    expectedProcessingStatus: ProcessingStatus
  ): void {
    this.getFileRow(fileName).within(() => {
      cy.get(productUploadLocators.statusCell).contains(
        expectedProcessingStatus
      );
    });
  }

  public initNeedsReviewGrid(fileName: string) {
    this.setDescSorting();
    this.setInterceptor(UploadStatusFilter.IN_REVIEW);
    this.searchUpload(fileName);
  }

  public initReviewedGrid(fileName: string) {
    this.navigateToReviewedTab();
    this.setInterceptor(UploadStatusFilter.REVIEWED);
    this.searchUpload(fileName);
  }

  public checkAvailableFilters() {
    cy.get(productUploadLocators.filterSelect).should("be.visible");
    cy.get(productUploadLocators.filterSelect).click();
    IN_REVIEW_FILTERS.forEach((filter) => {
      cy.get(productUploadLocators.filterOptionValue)
        .contains(filter)
        .then((option) => {
          cy.wrap(option).scrollIntoView().should("be.visible");
        });
    });
    REVIEWED_FILTERS.forEach((filter) => {
      cy.get(productUploadLocators.filterOptionValue)
        .contains(filter)
        .should("not.exist");
    });
  }

  public addFilter(filterName: string, filterValue: string) {
    cy.get(productUploadLocators.filterSelect).should("be.visible");
    cy.get(productUploadLocators.filterSelect).click();
    cy.get(productUploadLocators.filterOption)
      .contains(filterName)
      .scrollIntoView()
      .click();
    cy.get(productUploadLocators.selectFilterOptions).click();
    cy.get(productUploadLocators.filterOptionValue)
      .contains(filterValue)
      .scrollIntoView()
      .click();
  }

  public checkFilterResults(fileName: string) {
    cy.get(productUploadLocators.searchResultsHeader).should("be.visible");
    cy.get(productUploadLocators.uploadHistoryTable).within(() => {
      cy.contains("td.filename", fileName).should("be.visible");
      cy.contains("td", fileName)
        .parent("tr")
        .within(() => {
          cy.get(productUploadLocators.pendingMarketsList)
            .should("contain.text", germanMarketCode)
            .should("be.visible");
        });
    });
  }

  public clearAllFilters() {
    cy.get(productUploadLocators.clearAllFiltersButton).click();
  }

  public setAssignee(uploadFileName: string, uploadId: string): void {
    this.interceptUpdateAssignee(uploadId);
    this.clickAssignButton(uploadFileName);
    this.checkUpdateAssignee();
  }

  public removeAssignee(uploadFileName: string, uploadId: string): void {
    this.interceptUpdateAssignee(uploadId);
    this.clickAssignButton(uploadFileName);
    cy.get(productUploadLocators.confirmUnassignButton)
      .should("be.visible")
      .click({ force: true });
    this.checkUpdateAssignee();
  }

  public openStatsSidebar(uploadFileId: string) {
    const statsButton = productUploadLocators.openStatsButton(uploadFileId);
    this.interceptUploadStats();
    cy.get(statsButton)
      .should("be.visible")
      .should("be.enabled")
      .click({ force: true });
  }

  public checkStatsInSidebar({
    totalProducts,
    newProducts,
    approvedProducts,
    rejectedProducts,
    pendingProducts = undefined,
  }: StatsValues): void {
    this.interceptUploadStats();
    cy.get(productUploadLocators.totalProductsStats).should(
      "contain.text",
      totalProducts
    );
    cy.get(productUploadLocators.newProductsStats).should(
      "contain.text",
      newProducts
    );
    cy.get(productUploadLocators.approvedProductsStats).should(
      "contain.text",
      approvedProducts
    );
    cy.get(productUploadLocators.rejectedProductsStats).should(
      "contain.text",
      rejectedProducts
    );
    if (pendingProducts !== undefined) {
      cy.get(productUploadLocators.pendingProductsStats).should(
        "contain.text",
        pendingProducts
      );
    }
  }

  public setMarket(marketCode: string) {
    this.changeMarketInPicker(marketCode);
    this.checkMarketInLocalStorage(marketCode);
  }

  public checkAssignee(
    uploadName: string,
    uploadedFileId: string,
    marketCode: string,
    userName: string
  ) {
    this.checkIfAssignedInGrid(
      uploadedFileId,
      uploadName,
      marketCode,
      userName
    );
    this.openStatsSidebar(uploadedFileId);
    this.checkIfAssignedInStatsSidebar(marketCode, userName);
  }

  public filterAndParseFileProcess(uploadedFileId: string) {
    cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.IN_REVIEW).then(
      (uploadedFile) => {
        this.checkProcessingFileStatus(uploadedFile, UploadStatus.IN_REVIEW);
        productDataUploadsPage.interceptParsing(uploadedFile.fileName);
      }
    );
  }

  public interceptReport(): void {
    cy.intercept(
      "GET",
      "**/admin/v1/uploads/report?startDate=**&endDate=**"
    ).as("fileDownload");
  }

  public checkReport(): void {
    cy.wait("@fileDownload").then((interception) => {
      const filePath = this.getDownloadedFilePath(interception.request.url);
      this.validateExcelData(filePath);
      cy.task("deleteFile", filePath);
    });
  }

  public getDownloadedFilePath(url: string): string {
    const urlParams = new URLSearchParams(url.split("?")[1]);
    const startDate = urlParams.get("startDate");
    const endDate = urlParams.get("endDate");
    return `cypress/downloads/uploads_report_${startDate}_to_${endDate}.xlsx`;
  }

  public selectReportRange(range: string): void {
    cy.get(productUploadLocators.uploadReport).click();
    cy.get(productUploadLocators.uploadsReportSelector).click();
    cy.get(productUploadLocators.uploadReportExportRangeOption)
      .contains(range)
      .click();
    cy.get(productUploadLocators.uploadReportExportButton).click();
  }

  public isDateWithinRange(dateString: string, weeksAgo: number): boolean {
    if (!dateString) return false;

    const parseDate = (dateStr: string): Date => {
      const [day, month, year] = dateStr.split(".").map(Number);
      return new Date(year, month - 1, day);
    };

    const setMidnight = (date: Date) => date.setHours(0, 0, 0, 0);

    const actualDate = parseDate(dateString);
    setMidnight(actualDate);
    const today = new Date();
    setMidnight(today);
    const lastSunday = new Date(today);
    lastSunday.setDate(today.getDate() - today.getDay());
    setMidnight(lastSunday);
    const lastMonday = new Date(lastSunday);
    lastMonday.setDate(lastSunday.getDate() - weeksAgo * 7 + 1);
    setMidnight(lastMonday);

    return actualDate >= lastMonday && actualDate <= lastSunday;
  }

  public validateExcelData(filePath: string): void {
    cy.readExcel(filePath).then((sheet) => {
      expect(sheet[0]).to.deep.equal(reportHeaders);
    });
  }

  public downloadAndCheckUploadReport(uploadedFileId: string): void {
    cy.getFilteredUpload(uploadedFileId, UploadStatusFilter.REVIEWED).then(
      (reviewedFile) => {
        this.interceptUploadReport(reviewedFile.uploadId);
        this.getFileRow(reviewedFile.fileName).within(() => {
          cy.get(productUploadLocators.downloadUploadReportButton)
            .should("be.visible")
            .should("be.enabled")
            .click({ force: true });
        });
        this.checkUploadReport();
      }
    );
  }

  private searchRejectError(errorMessage, errorId): void {
    cy.get(productUploadLocators.errorMessageSearch)
      .find("input")
      .type(errorMessage);
    cy.wait(500);
    cy.get(`[test-target="${errorMessage}"]`).click();
    cy.get(productUploadLocators.errorMessageSearch).find("input").clear();

    cy.get('textarea[name="rejectionReason"]')
      .invoke("val")
      .then((value) => {
        expect(value).to.include(errorId);
      });
  }

  private interceptStartUploadParsing(): void {
    cy.log(`Adding **${apiEndpoints.startUploadParsing} intercept`);
    cy.intercept("POST", `**${apiEndpoints.startUploadParsing}`).as(
      "startUploadParsing"
    );
  }

  private interceptGetUploadParsingStatistic(): void {
    cy.intercept("GET", `**${apiEndpoints.getUploadParsingStatistics}`).as(
      "getUploadParsingStatistic"
    );
  }

  private interceptUploadStats(): void {
    cy.intercept("GET", `**${apiEndpoints.uploadStats}`).as("getUploadStats");
  }

  private interceptUpdateAssignee(uploadId: string): void {
    cy.getLocalStorage(MARKET_KEY).then((value) => {
      const market = JSON.parse(value);
      const assignMarket = apiEndpoints.assignUploadMarket(market.id, uploadId);
      cy.intercept("PATCH", `**${assignMarket}`).as("updateAssignee");
    });
  }

  private interceptApproveFile(): void {
    cy.getLocalStorage(MARKET_KEY).then((value) => {
      const market = JSON.parse(value);
      cy.log(`Adding **${apiEndpoints.approveFile(market.id)} intercept`);

      cy.intercept("POST", `**${apiEndpoints.approveFile(market)}`).as(
        "approveFile"
      );
    });
  }

  private setDescSorting(): void {
    cy.get(productUploadLocators.createdAtHeaderCell).within(() => {
      cy.get(productUploadLocators.sortButton)
        .click()
        .should("have.class", "active-desc");
    });
  }

  private setInterceptor(fileStatus: UploadStatusFilter): void {
    const fileNameFilter = `${encodeURI("filter[fileName]")}`;
    const fileStatusFilter = `${encodeURI(
      "filter[uploadStatus]"
    )}=${fileStatus}`;
    const alias = `getFilteredUpload${fileStatus}`;
    cy.intercept(
      "GET",
      `**${apiEndpoints.productUploads}*${fileStatusFilter}*${fileNameFilter}*`
    ).as(alias);
  }

  private searchUpload(fileName: string): void {
    cy.get(productUploadLocators.searchInput).type(fileName);
    cy.get(productUploadLocators.searchButton).click();
  }

  private clickAssignButton(uploadFileName: string): void {
    this.getFileRow(uploadFileName).within(() => {
      cy.get(productUploadLocators.assignButton)
        .should("be.visible")
        .should("be.enabled")
        .click({ force: true });
    });
  }

  private checkUpdateAssignee(): void {
    cy.wait("@updateAssignee").should((interception) => {
      checkStatusCode(interception.response, 200);
    });
  }

  private checkIfAssignedInGrid(
    uploadedFileId: string,
    uploadFileName: string,
    market: string,
    userName: string
  ): void {
    const alias = `@getFilteredUpload${UploadStatus.IN_REVIEW}`;
    cy.wait(alias, {
      timeout: PRODUCT_UPLOAD_ENDPOINT_TIMEOUT,
    }).should((interception) => {
      checkStatusCode(interception.response, 200);

      const uploads = interception.response.body.items;
      const currentUpload = uploads.find(
        (upload) => upload.uploadId === uploadedFileId
      );

      this.checkAssigneeResponse(
        currentUpload.marketsStats,
        market,
        userName !== nonAssignedLabel ? userName : null
      );
      this.getFileRow(uploadFileName)
        .within(() => {
          cy.get(productUploadLocators.assignButton)
            .should("be.visible")
            .should("be.enabled")
            .trigger("mouseenter")
            .as("assignButton");
        })
        .then(() => {
          cy.get(productUploadLocators.assigneeTooltip)
            .and("contain", userName.split("@")[0])
            .and("contain", market);
          cy.get("@assignButton").trigger("mouseleave");
        });
    });
  }

  private checkIfAssignedInStatsSidebar(
    market: string,
    userName: string
  ): void {
    cy.wait("@getUploadStats").should((interception) => {
      checkStatusCode(interception.response, 200);

      this.checkAssigneeResponse(
        interception.response.body.assignees,
        market,
        userName !== nonAssignedLabel ? userName : null
      );

      const statsMarketContainer =
        productUploadLocators.statsMarketContainer(market);
      cy.get(statsMarketContainer).within(() => {
        cy.contains(userName.split("@")[0]).should("be.visible");
        cy.contains(market).should("be.visible");
      });
    });
  }

  private checkAssigneeResponse(
    marketAssignees: any,
    market: string,
    userName: string
  ): void {
    const marketAssignee = marketAssignees.find(
      (marketAssignee) => marketAssignee.market === market
    );
    expect(marketAssignee.assignee).to.eq(userName);
  }

  public startParseCheck(uploadedFileId: string, attempts: number = 0): void {
    cy.getFilteredUpload(
      uploadedFileId,
      UploadStatusFilter.IN_REVIEW,
      PRODUCT_UPLOAD_ENDPOINT_TIMEOUT
    ).then((uploadedFile) => {
      let isReadyForReview =
        uploadedFile.status.parse.id === ParseFileStatus.PARSING_COMPLETED;

      if (uploadedFile.status.rulesProcessing !== null) {
        isReadyForReview =
          uploadedFile.status.rulesProcessing.id ===
          UploadRulesStatus.READY_FOR_MANUAL_REVIEW;
      }

      if (isReadyForReview) {
        const statsButton =
          productUploadLocators.openStatsButton(uploadedFileId);
        cy.get(statsButton)
          .should("be.visible")
          .should("be.enabled")
          .then(() => {
            this.checkParsedFile(uploadedFile);
          });
      } else {
        this.retryParseCheck(uploadedFileId, attempts);
      }
    });
  }

  private retryParseCheck(uploadedFileId: string, attempts: number): void {
    if (attempts < MAX_AUTOREFRESH_ATTEMPTS) {
      attempts++;
      this.startParseCheck(uploadedFileId, attempts);
    } else {
      throw new Error(
        `Error: Parsing not Completed after ${MAX_AUTOREFRESH_ATTEMPTS} attempts`
      );
    }
  }

  private interceptUploadReport(uploadId: string): void {
    cy.intercept("GET", `**${apiEndpoints.downloadReport(uploadId)}*`).as(
      "uploadReport"
    );
  }

  private checkUploadReport() {
    cy.wait("@uploadReport").then((interception) => {
      checkStatusCode(interception.response, 200);
      const reportData = interception.response.body;
      const reportRows = productDataUploadsPage.convertCSVToJSON(reportData);
      reportRows.forEach((reportRow) => {
        rejectRowReasonMessages.forEach((expectedRejectReason) => {
          this.checkErrorReport(reportRow, expectedRejectReason);
        });
      });
    });
  }
}

export const productDataUploadsPage = new ProductDataUploadsPage();
