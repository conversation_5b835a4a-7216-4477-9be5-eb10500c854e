import { apiEndpoints } from "../../endpoints";
import { categoryList<PERSON>eys } from "../../interfaces/response-keys";
import { MARKET_KEY } from "../../constants/local-storage-constants";
import { checkStatusCode } from "../../helpers";
import { categoryTreeLocators } from "../../locators/employee/category-tree-locators";
import { germanMarketCode, relevance } from "../../constants/pim-constants";
import { attributeTypes } from "../../constants/pim-structures";
import { PimPage } from "./common/pim-page";

export class CategoryTreePage extends PimPage {
  public interceptRootCategories(): void {
    cy.getLocalStorage(MARKET_KEY).then((value) => {
      const market = JSON.parse(value);
      cy.intercept(
        "GET",
        `**/${apiEndpoints.categoriesByMarket(market.id)}*`
      ).as("getRootCategoriesByMarket");
      cy.log(
        `${Cypress.env("categoryBaseUrl")}${apiEndpoints.categoriesByMarket(
          market.id
        )}`
      );
    });
  }

  public interceptChildCategories(id: string): any {
    cy.intercept("GET", `**/${apiEndpoints.childCategory(id)}*`).as(
      "getChildCategories"
    );
    cy.log(
      `${Cypress.env("categoryBaseUrl")}${apiEndpoints.childCategory(id)}`
    );
  }

  public interceptCreateRootCategory(): any {
    cy.intercept(
      "POST",
      `**/${apiEndpoints.categoryRootByMarket(germanMarketCode)}*`
    ).as("createRootCategoryByMarket");
    cy.log(
      `${Cypress.env("categoryBaseUrl")}${apiEndpoints.categoryRootByMarket(
        germanMarketCode
      )}`
    );
  }

  public interceptCategoryDetail(id): any {
    cy.intercept("GET", `**/${apiEndpoints.category(id)}*`).as(
      "getDetailCategory"
    );
    cy.log(`${Cypress.env("categoryBaseUrl")}${apiEndpoints.category(id)}`);
  }

  public goToCategory(id: string): void {
    cy.getByTestId(`category-details-button-${id}`).click();
  }

  public expandCategory(id: string): void {
    cy.getByTestId(`tree-expandable-area-${id}`).click();
  }

  public checkRootCategories(data: any): void {
    expect(data).to.have.property("totalCount");
    expect(data).to.have.property("items");

    expect(data.totalCount).to.be.gt(0);
    assert.isArray(data.items);
    expect(data.items).not.to.be.empty;
    data.items.slice(0,10).forEach((item) => {
      cy.log(`Category name: ${item.name}.`);
      categoryListKeys.forEach((property) => {
        expect(item).to.have.property(property);
      });
    });
  }

  public checkRootCategoryHasId(data: any): void {
    expect(data.body).to.have.property("id");

    checkStatusCode(data, 201);
  }

  public checkRootCategoryParams(data: any, params: any): void {
    expect(data.taxes[0].amount).equals(params.taxValue);
    expect(data.name).equals(params.uniqueNameDe) ||
      expect(data.name).equals(params.uniqueNameEn);

    this.checkCategoryParams(data, params);
  }

  public checkCategoryParams(data: any, params: any): void {
    expect(data.transactionFee).equals(params.transactionFee);
    expect(data.isActive).equals(false);
    expect(data.hasChild).equals(false);

    checkStatusCode(data);
  }

  public checkChildCategoryParams(data: any, params: any): void {
    expect(data.name).equals(params.uniqueNameDe) ||
      expect(data.name).equals(params.uniqueNameEn);
    expect(data.transactionFee).equals(0);

    checkStatusCode(data);
  }

  public checkCategoryNotFound(data: any): void {
    expect(data.statusText).equals("Not Found");

    checkStatusCode(data, 404);
  }

  public goToAddNewCategoryForm(): void {
    cy.get(categoryTreeLocators.addNewCategoryButton).click();
  }

  public saveCategory(): any {
    cy.get(categoryTreeLocators.saveCategoryButton).click();
  }

  public setCategory(randomNumber: number, vat: number): any {
    cy.get(categoryTreeLocators.nameInput).each(
      (element: any, index: number) => {
        cy.get(element)
          .invoke("val", "")
          .trigger("change")
          .type(`unique_name_${index}_${randomNumber}`);
      }
    );

    cy.get(categoryTreeLocators.vatInput)
      .invoke("val", null)
      .trigger("change")
      .type(vat.toString());

    this.saveCategory();
  }

  public checkValidUpdatedCategory(currentData: any, updatedData: any): void {
    expect(currentData.transactionFee).to.equal(updatedData.transactionFee);
    expect(currentData.taxesAmount).to.equal(updatedData.taxes[0].amount);
    expect(currentData.metroReportingCategory).to.equal(
      updatedData.metroReportingCategory
    );
  }

  public getItemWithChild(items: any) {
    return items.find((item: any) => item.hasChild);
  }

  public checkParentCategoryIsNotActive(data: any): void {
    expect(data.body.title).equals("Parent category is not active");
    checkStatusCode(data, 400);
  }

  public checkCategoryWithoutContent(data: any): void {
    expect(data.statusText).equals("No Content");
    checkStatusCode(data, 204);
  }

  public checkCategoryIsActive(data: any): void {
    expect(data.isActive).equals(true);
    expect(data.hasChild).equals(true);
    expect(data.isParentActive).equals(true);

    checkStatusCode(data);
  }

  public checkCategoryIsNotActive(data: any): void {
    expect(data.isActive).equals(false);
    expect(data.hasChild).equals(true);

    checkStatusCode(data);
  }

  public checkItemTypeIsNotActive(data: any): void {
    expect(data.isActive).equals(false);
    expect(data.hasChild).equals(false);

    checkStatusCode(data);
  }

  public checkItemTypeIsActiveAndParentIsActive(data: any): void {
    expect(data.isActive).equals(true);
    expect(data.isParentActive).equals(true);

    checkStatusCode(data);
  }

  public checkItemTypeIsActive(data: any): void {
    expect(data.isActive).equals(true);

    checkStatusCode(data);
  }

  public checkItemTypeIsNotActiveAndParentIsActive(data: any): void {
    expect(data.isActive).equals(false);
    expect(data.isParentActive).equals(true);

    checkStatusCode(data);
  }

  public checkItemTypeIsNotActiveAndParentActiveHasChild(data: any): void {
    expect(data.isActive).equals(false);
    expect(data.hasChild).equals(true);
    expect(data.isParentActive).equals(true);

    checkStatusCode(data);
  }

  public checkIncorrectMarket(data: any): void {
    expect(data.body.title).equals("Not found");
    expect(data.body.detail).equals("Market not found");

    checkStatusCode(data, 404);
  }

  public checkAddedCategoryAttributes(data: any): void {
    expect(data.body[0]).equals("OK");
    checkStatusCode(data, 201);
  }

  public checkCategoryAttributes(
    existingAttributes: any,
    updatedAttributes: any
  ): void {
    const relevanceProp = "relevance";
    updatedAttributes.body.items.forEach((item) => {
      existingAttributes.forEach((attribute, index) => {
        if (item.id === attribute.attributeId) {
          expect(item)
            .to.have.property(relevanceProp)
            .equals(relevance[index]);
        }
      });
    });
  }

  public checkCategoryAttributesNotFound(
    existingAttributes: any,
    updatedAttributes: any
  ): void {
    const attributesFound = updatedAttributes.body.items.filter(
      (item) =>
        item.id === existingAttributes[0].attributeId &&
        item.id === existingAttributes[1].attributeId
    );
    expect(attributesFound.length).equals(0);
  }

  public checkAddedCategoryAttributesUnassigned(data: any): void {
    expect(data.body[0]).equals("OK");
    checkStatusCode(data);
  }

  public checkCategoryContainsAttributes(data: any, body: any): void {
    data.body.items.forEach((item) => {
      expect(item.id).to.be.oneOf([body[0].attributeId, body[1].attributeId]);
    });

    checkStatusCode(data);
  }

  public checkItemTypeAttributes(types: any, items: any): void {
    expect(items.length).to.be.greaterThan(0);
    items.forEach((item) => {
      expect(types).to.include(item.type.id);
    });
  }

  public checkCategoryBasePriceIsActive(basePriceAttribute: any): void {
    expect(basePriceAttribute).to.have.property("isBasePrice").to.equal(true);
  }

  public checkCategoryBasePriceIsInActive(basePriceAttribute: any): void {
    expect(basePriceAttribute).to.have.property("isBasePrice").to.equal(false);
  }

  public checkCategoryAllBasePricesAreInActive(items: any): void {
    items.forEach((item) => {
      this.checkCategoryBasePriceIsInActive(item);
    });
  }

  public checkItemTypeStructure(data: any): void {
    const item = data.body.items[0];
    const type = item.type.id;
    const keys = Object.keys(attributeTypes[type]);

    keys.forEach((key) => {
      if (key !== "condition" && key !== "example") {
        expect(item).to.have.property(key);
      }
    });

    checkStatusCode(data);
  }

  public checkCategoryRelevanceAttribute(data: any): void {
    data.body.items.forEach((item) => {
      expect(item).to.have.property("relevance").to.be.oneOf(relevance);
    });
  }

  public checkCsvHeaderLabel(data: any): void {
    expect(data.body.items.length).equals(10);
  }

  public checkAttributesWereSortedAsc(items: any): boolean {
    const sortedIndexes = [];

    for (let i = 0; i < items.length; i++) {
      if (items[i] && items[i + 1]) {
        const prev = items[i].type.label;
        const current = items[i + 1].type.label;
        const comparing = prev
          .toLowerCase()
          .localeCompare(current.toLowerCase());

        sortedIndexes.push(comparing);
      }
    }
    return !Boolean(sortedIndexes.find((item) => item == 1));
  }

  public checkCategoryParamsIsNotEmpty(data: any, params: any): void {
    this.checkCategoryParams(data, params);

    expect(data.taxes[0].amount).equals(params.taxValue);
  }

  public checkCategoryParamsIsEmpty(data: any, params: any): void {
    this.checkCategoryParams(data, params);

    expect(data.taxes.length).equals(0);
  }

  public checkItemTypeParamsByMarketIsValid(
    data: any,
    params: any,
    market: string
  ): void {
    data.translations.forEach((item) => {
      if (item.lang === market.toLowerCase()) {
        expect(item.properties.name).equals(params.name[item.lang]);
      }
    });

    expect(data.transactionFee).equals(params.transactionFee);
    expect(data.taxes[0].amount).equals(params.taxesAmount);
    expect(data.metroReportingCategory).equals(params.metroReportingCategory);

    checkStatusCode(data);
  }

  public checkItemTypeParamsByMarketIsInvalid(
    data: any,
    params: any,
  ): void {
    // Name
    const notFindTranslation = (item) =>
      item.properties.name != params.name[item.lang];
    const notFindTranslationResult = data.translations.some(notFindTranslation);
    expect(notFindTranslationResult).equals(true);

    // Taxes
    const emptyTax = data.taxes.length === 0;
    const invalidTax = data.taxes[0]?.amount !== params.taxesAmount;
    expect(emptyTax || invalidTax).to.be.true;
  }
}

export const categoryTreePage = new CategoryTreePage();
