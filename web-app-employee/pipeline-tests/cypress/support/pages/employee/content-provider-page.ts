import { apiEndpoints } from "../../endpoints";
import { contentProviderLocators } from "../../locators/employee/content-provider-locators";
import {
  CONTENT_PROVIDER_MIN_RANK_ERROR,
  CONTENT_PROVIDER_MAX_RANK_ERROR_DE,
  CONTENT_PROVIDER_MAX_RANK_ERROR,
  CONTENT_PROVIDER_MIN_RANK_ERROR_DE,
  cpFilterMapper,
  cpLocatorMapper,
} from "../../constants/content-provider-constants";
import { ContentProviderFilter } from "../../constants/pim-enums";

export class ContentProviderPage {
  public goToContentProviderDetails(id: string): void {
    cy.get(contentProviderLocators.editButton(id)).click();
  }

  public filterBy(filterType: string, filterValue: string): void {
    cy.get(contentProviderLocators.filterSelect).then((selects) => {
      let select = selects[0];
      cy.wrap(select)
        .click()
        .get("ng-dropdown-panel")
        .get(".ng-option")
        .contains("span", cpFilterMapper[filterType])
        .should("be.visible")
        .click({ force: true });
    });
    cy.get(contentProviderLocators.searchInput).type(filterValue);
    cy.get(contentProviderLocators.searchButton).click();
  }

  public filterByMarket(filterValue: string): void {
    const filterType = ContentProviderFilter.MARKET;

    cy.get(contentProviderLocators.filterSelect).then((selects) => {
      let select = selects[0];
      cy.wrap(select)
        .click()
        .get("ng-dropdown-panel")
        .get(".ng-option")
        .contains("span", cpFilterMapper[filterType])
        .scrollIntoView()
        .should("be.visible")
        .click({ force: true });
    });

    cy.get(contentProviderLocators.marketPicker).then((selects) => {
      let select = selects[0];
      cy.wrap(select).click().get(`[test-target="${filterValue}"]`).click();
    });
  }

  public editContentProviderRank(value: number): void {
    cy.get(contentProviderLocators.editRankButton).click();
    cy.get(contentProviderLocators.editRankInput).focus().clear();
    cy.get(contentProviderLocators.editRankInput).type(value.toString());
    cy.get(contentProviderLocators.submitRankButton).click();
  }

  public editContentProviderNote(value: string): void {
    cy.get(contentProviderLocators.editNoteButton).click();
    cy.get(contentProviderLocators.editNoteInput).focus().clear();
    cy.get(contentProviderLocators.editNoteInput).type(value);
    cy.get(contentProviderLocators.submitNoteButton).click();
  }

  public downloadContentProviderList(): void {
    cy.get(contentProviderLocators.downloadListButton).click();
  }

  public interceptContentProviderList(): void {
    cy.intercept("GET", `**/${apiEndpoints.contentProviderList}`).as(
      "getContentProviderList"
    );
  }

  public interceptContentProviderListFilterBy(
    filterType: string,
    filterValue: string
  ): void {
    const filter = `filter[${filterType}]`;
    const query = `?offset=0&${encodeURI(filter)}=${encodeURI(filterValue)}`;
    cy.intercept("GET", `**/${apiEndpoints.contentProviderList}${query}`).as(
      `getCPListFilteredBy${filterType}`
    );
    cy.log(`getCPListFilteredBy${filterType}`);
    cy.log(`**/${apiEndpoints.contentProviderList}${query}`);
  }

  public interceptContentProviderDetails(id: string): void {
    cy.intercept("GET", `**/${apiEndpoints.contentProvider(id)}`).as(
      "getContentProvider"
    );
  }

  public interceptContentProviderRank(id: string): void {
    cy.intercept("PATCH", `**/${apiEndpoints.contentProviderRank(id)}`).as(
      "patchContentProviderRank"
    );
  }

  public interceptContentProviderNote(id: string): void {
    cy.intercept("PATCH", `**/${apiEndpoints.contentProviderNote(id)}`).as(
      "patchContentProviderNote"
    );
  }

  public interceptContentProviderHistoryRank(id: string): void {
    cy.intercept(
      "GET",
      `**/${apiEndpoints.contentProviderHistoryRank(id)}*`
    ).as("getContentProviderHistoryRank");
  }

  public interceptDownloadContentProviderList(): void {
    cy.intercept("GET", `**/${apiEndpoints.downloadContentProviderList}*`).as(
      "downloadContentProviderList"
    );
  }

  public checkContentProviderListStructure(data: any): void {
    expect(data).to.have.property("totalCount");
    expect(data).to.have.property("items");
    assert.isArray(data.items);

    data.items.forEach((contentProvider) => {
      expect(contentProvider).to.have.property("id");
      expect(contentProvider).to.have.property("organizationId");
      expect(contentProvider).to.have.property("organizationName");
      expect(contentProvider).to.have.property("shopName");
      expect(contentProvider).to.have.property("source");
      expect(contentProvider).to.have.property("sourceName");
      expect(contentProvider).to.have.property("type");
      expect(contentProvider).to.have.property("updatedAt");
      expect(contentProvider).to.have.property("createdAt");
    });
  }

  public checkContentProviderFilteredList(
    filterType: string,
    filterValue: string,
    data: any
  ): void {
    expect(data).to.have.property("totalCount");
    expect(data).to.have.property("items");
    assert.isArray(data.items);

    data.items.forEach((contentProvider) => {
      expect(contentProvider).to.have.property(filterType, filterValue);
    });

    cy.get(cpLocatorMapper[filterType])
      .first()
      .invoke("text")
      .then((txt) => {
        expect(txt).to.contain(filterValue);
      });
  }

  public checkContentProviderListFilteredByMarket(
    filterValue: string,
    data: any
  ): void {
    const filterType = ContentProviderFilter.MARKET;
    data.items.forEach((contentProvider) => {
      expect(contentProvider).to.have.property("markets");
      expect(contentProvider["markets"].includes(filterValue)).to.eq(true);
    });
  }

  public checkContentProviderDetails(contentProvider: any, data: any): void {
    expect(data).to.have.property("id", contentProvider.id);
    expect(data)
      .to.have.property("organization")
      .property("id", contentProvider.organizationId);
    expect(data)
      .to.have.property("organization")
      .property("name", contentProvider.organizationName);
    expect(data)
      .to.have.property("organization")
      .property("shopName", contentProvider.shopName);
    expect(data).to.have.property("note");
    expect(data).to.have.property("markets");
    expect(data).to.have.property("updatedAt");
    expect(data).to.have.property("createdAt");
    expect(data).to.have.property("weight");
    expect(data["markets"]).includes(contentProvider.market);
  }

  public checkContentProviderHistoryRankStructure(data: any): void {
    expect(data).to.have.property("totalCount");
    expect(data).to.have.property("items");
    assert.isArray(data.items);

    data.items.forEach((contentProvider) => {
      expect(contentProvider).to.have.property("updatedAt");
      expect(contentProvider).to.have.property("updatedBy");
      expect(contentProvider).to.have.property("weight");
    });
  }

  public checkContentProviderRank(updatedRank: number): void {
    cy.get(contentProviderLocators.rankValue)
      .invoke("text")
      .then((txt) => {
        expect(txt).to.contain(updatedRank);
      });
  }

  public checkContentProviderNote(note: string): void {
    cy.get(contentProviderLocators.noteValue)
      .invoke("text")
      .then((txt) => {
        expect(txt).to.contain(note);
      });
  }

  public checkContentProviderRankMINErrorMessage(): void {
    this.checkContentProviderRankErrorMessage(
      `${CONTENT_PROVIDER_MIN_RANK_ERROR}${CONTENT_PROVIDER_MIN_RANK_ERROR_DE}`
    );
  }

  public checkContentProviderRankMAXErrorMessage(): void {
    this.checkContentProviderRankErrorMessage(
      `${CONTENT_PROVIDER_MAX_RANK_ERROR}${CONTENT_PROVIDER_MAX_RANK_ERROR_DE}`
    );
  }

  private checkContentProviderRankErrorMessage(errorMessage: string) {
    cy.get(contentProviderLocators.invalidRankValue)
      .find("div")
      .invoke("text")
      .then((txt) => {
        expect(errorMessage).to.contain(txt.trim());
      });
  }

  checkDownloadedList(response: any) {
    expect(response.statusMessage).to.eq("OK");
    expect(response.statusCode).to.eq(200);
  }
}

export const contentProviderPage = new ContentProviderPage();
