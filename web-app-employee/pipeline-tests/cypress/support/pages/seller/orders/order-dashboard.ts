import { checkOrderNumber } from "../../../common";
import { orderDetailsLocators } from "../../../locators/seller/order-details";

export class OrderDashboard {


  checkOrder = (orderNumber: string): void => {
    checkOrderNumber(orderNumber);
  }

  checkIfOrderIsVisible = (orderNumber: string): void => {
    cy.getByTestId('order-number-link').should('be.visible').contains(orderNumber).click();
  }

  confirmStatusSelection = (): void => {
    cy.getByTestId(orderDetailsLocators.statusSelect).click();
    cy.getByTestId(orderDetailsLocators.statusConfirm).click();
  }

  submitOrderSelection = (): void => {
    cy.getByTestId(orderDetailsLocators.submitOrder).click();
  }

  assertAdditionalSelectedOrderDetails = (): void => {
    cy.getByTestId(orderDetailsLocators.confirmTitle).should('be.visible');
    cy.get(orderDetailsLocators.orderDetailsLoading).should('not.exist');
  }

}

export const orderDashboardPage = new OrderDashboard();
