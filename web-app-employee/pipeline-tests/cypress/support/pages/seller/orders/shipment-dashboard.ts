import { orderDetailsLocators } from "../../../locators/seller/order-details";
import { orderShipmentDetailsLocators } from "../../../locators/seller/order-shipment-details";

const shippingData = { trackingId: '3xPTrGoSMCorWjJ', carrier: 'DHL Paket' };

export class ShipmentDashboardPage {

  confirmShippedOrderSelection = (): void => {
    cy.getByTestId(orderDetailsLocators.statusSelect).click();
    cy.getByTestId(orderShipmentDetailsLocators.shippedStatusConfirm);
  }

  assertForCarrierAndContinue = (): void => {
    cy.getByTestId(orderShipmentDetailsLocators.deliveryCarrierInput).contains(shippingData.carrier).click();
  }

  enterTrackingId = (): void => {
    cy.getByTestId(orderShipmentDetailsLocators.trackingIdInput).type(shippingData.trackingId);
  }

  submitShipmentSelection = (): void => {
    cy.getByTestId(orderDetailsLocators.submitOrder).click();
  }

  assertForShippedTitle = (): void => {
    cy.getByTestId(orderShipmentDetailsLocators.shippedTitle).should('be.visible');
  }

}

export const shipmentDashboardPage = new ShipmentDashboardPage();
