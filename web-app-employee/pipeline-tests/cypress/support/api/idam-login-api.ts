import { API_SELLER_LOGIN_URL } from "../constants/endpoints";
import { employeeSettings } from "../constants/employee-settings";
import { BUYER_TOKEN_KEY, SELLER_TOKEN_KEY, EMPLOYEE_TOKEN_KEY  } from "../constants/local-storage-constants";

export class IdamLoginApi {
  buyerLogin(user: string): void {
    let credentials = Cypress.env('users');

    cy.request({
      method: 'POST',
      url: Cypress.env('idamUrl'),
      form: true,
      body: {
        grant_type: 'password',
        client_id: 'MMARKET_DE',
        user_type: 'CUST',
        client_secret: Cypress.env('clientSecretIdamTokenBuyer'),
        username: credentials[user].username,
        password: credentials[user].password
      }
    }).then(response => {
      cy.setLocalStorage(BUYER_TOKEN_KEY, response.body.access_token);
      cy.saveLocalStorage();
      cy.cacheToken();
    });
  }

  sellerLogin(user: string): void {
    let credentials = Cypress.env('users');

    cy.request(
      'POST',
      API_SELLER_LOGIN_URL,
      {
        username: credentials[user].username,
        password: credentials[user].password
      }
    ).then(response => {
      cy.setLocalStorage(SELLER_TOKEN_KEY, JSON.stringify(response.body['access_token']).replace(/['"]+/g, ''));
      cy.saveLocalStorage();
      cy.cacheToken();
    });
  }

  employeeLogin(user: string, visitUrl: string = null): void {
    let credentials = Cypress.env('users');
    cy.request({
      method: 'POST',
      url: Cypress.env('imsLoginUrl'),
      body: {
        username: credentials[user].username,
        password: credentials[user].password,
      }
    }).then(response => {
      cy.setLocalStorage(EMPLOYEE_TOKEN_KEY, response.body.access_token);
      cy.saveLocalStorage();
      cy.cacheToken();
    });

    if(visitUrl) {
      cy.visit(visitUrl);
    }
  }
}

export const loginApi = new IdamLoginApi();
