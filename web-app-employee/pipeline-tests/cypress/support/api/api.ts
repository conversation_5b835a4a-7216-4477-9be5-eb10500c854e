import 'cypress-localstorage-commands';
import {API_SELLER_ORDERS_URL} from '../constants/endpoints';
import {getTokenAuthorization} from "../helpers";

export class ApiRequests {
  clearCart(): void {
    cy.request({
      url: Cypress.env('apiCart'),
      method: 'GET',
      headers: {
        'Country-Code': 'de',
        Authorization: getTokenAuthorization(),
        'x-correlation-id': 'cypress-checkout-clearCart'
      }
    }).then(response => {
      const itemsInCart = response.body?.items[0]?.items;

      this.perfromItemsDeletion(itemsInCart);
    })
  }

  perfromItemsDeletion = (itemsInCart): void => {
    if (!!itemsInCart) {
      itemsInCart.forEach((item, index) => {
        cy.request({
          url: `${Cypress.env('apiCart')}/${itemsInCart[index].offer}`,
          method: 'DELETE',
          headers: {
            'Country-Code': 'de',
            'Authorization': getTokenAuthorization(),
            'x-correlation-id': 'cypress-checkout-clearCart'
          }
        })
      })
    }
  }

  getSellerOrders(): void {
    cy.request({
      url: `${API_SELLER_ORDERS_URL}?limit=50&offset=0&sort[createdAt]=desc`,
      method: 'GET',
      headers: {
        Authorization: getTokenAuthorization()
      }
    }).then(response => {
      cy.task('setSellerOrders', response.body.items);
    });
  }

  addOfferToCart( offer: string, quantity = 1): void {
    cy.request({
      url: Cypress.env('apiCart'),
      method: 'POST',
        headers: {
        'Country-Code': 'de',
        Authorization: getTokenAuthorization(),
        'x-correlation-id': 'add-offer-employee'
      },
      body: { offer, quantity }
    });
  }
}

export const apiRequests = new ApiRequests();
