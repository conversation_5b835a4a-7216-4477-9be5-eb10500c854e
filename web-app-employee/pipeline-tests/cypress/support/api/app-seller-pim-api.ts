import "cypress-localstorage-commands";
import { getTokenAuthorization } from "../helpers";
import { apiEndpoints } from "../endpoints";
import {
  EMPLOYEE_TOKEN_KEY,
  SELLER_TOKEN_KEY,
} from "../constants/local-storage-constants";
import { germanMarketCode } from "../constants/pim-constants";
import { UploadStatusFilter } from "../constants/pim-enums";

export class AppSellerPimApi {
  setAutoReview(organizationId: string, review = true): void {
    cy.request({
      method: "POST",
      body: { autoReview: review },
      url: `${
        Cypress.env("pimBaseUrl") + apiEndpoints.reviewStatus(organizationId)
      }`,
      headers: {
        Authorization: getTokenAuthorization(EMPLOYEE_TOKEN_KEY),
      },
    }).as("setAutoReview");
  }
  uploadFile(fileUrl: string): void {
    cy.fixture(fileUrl, "binary")
      .then((fileContent) => Cypress.Blob.binaryStringToBlob(fileContent))
      .then((blob) => {
        const formData = new FormData();
        formData.append("file", blob, fileUrl);
        cy.request({
          method: "POST",
          body: formData,
          url: `${Cypress.env("pimBaseUrl") + apiEndpoints.uploadFile}`,
          headers: {
            Authorization: getTokenAuthorization(SELLER_TOKEN_KEY),
            "Content-Type": "multipart/form-data;",
          },
        }).as("uploadFile");
      });
  }

  getUploadFileList(
    uploadStatusFilter: UploadStatusFilter,
    organizationId: string
  ): void {
    const params = `limit=500&offset=0&filter[organizationId]=${organizationId}&filter[uploadStatus]=${uploadStatusFilter}&filter[showOnlyWithOneRow]=0`;

    const url = `${
      Cypress.env("pimBaseUrl") +
      apiEndpoints.productUploads +
      "?" +
      encodeURI(params)
    }`;

    cy.request({
      method: "GET",
      url: url,
      headers: {
        Authorization: getTokenAuthorization(EMPLOYEE_TOKEN_KEY),
      },
    }).as("getFileList");
  }

  getProductList(search): void {
    const params = `?filter%5Bsearch%5D=${search}`;
    const url = `${
      Cypress.env("pimBaseUrl") + apiEndpoints.productList + params
    }`;

    cy.request({
      method: "GET",
      url: url,
      headers: {
        Authorization: getTokenAuthorization(EMPLOYEE_TOKEN_KEY),
      },
    }).as("getProductList");
  }

  getProductInfo(search): void {
    const params = `?identity=${search}`;
    const url = `${
      Cypress.env("pimBaseUrl") + apiEndpoints.productInfo + params
    }`;
    cy.request({
      method: "GET",
      url: url,
      headers: {
        Authorization: getTokenAuthorization(EMPLOYEE_TOKEN_KEY),
      },
    }).as("getProductInfo");
  }

  getProduct(id): void {
    const url = `${
      Cypress.env("pimBaseUrl") + apiEndpoints.product(germanMarketCode, id)
    }`;
    cy.request({
      method: "GET",
      url: url,
      headers: {
        Authorization: getTokenAuthorization(EMPLOYEE_TOKEN_KEY),
      },
    }).as("getProduct");
  }
  getFileReport(uploadedFileId): void {
    const url = `${
      Cypress.env("pimBaseUrl") + apiEndpoints.downloadReport(uploadedFileId)
    }`;
    cy.request({
      method: "GET",
      url: url,
      headers: {
        Authorization: getTokenAuthorization(EMPLOYEE_TOKEN_KEY),
      },
    }).as("getFileReport");
  }
}

export const appSellerPimApi = new AppSellerPimApi();
