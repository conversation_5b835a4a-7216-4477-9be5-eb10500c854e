import { getTokenAuthorization } from '../helpers';

export class DisputeManagementRequests {
  getDisputeDetails(disputeId: string) {
    cy.request({
      method: 'GET',
      url: `${Cypress.env('disputesBaseUrl')}/api/v1/paypal/disputes/${disputeId}`,
      headers: {
        Authorization: getTokenAuthorization(),
      },
      failOnStatusCode: false
    }).as('disputeDetails');
  }

  getDisputeList(params: any) {
    cy.request({
      method: 'GET',
      url: `${Cypress.env('disputesBaseUrl')}/api/v1/paypal/disputes`,
      qs: params,
      headers: {
        Authorization: getTokenAuthorization(),
      },
      failOnStatusCode: false
    }).as('disputeList');
  }
}

export const disputeApi = new DisputeManagementRequests();
