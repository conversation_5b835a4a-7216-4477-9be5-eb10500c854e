import { getTokenAuthorization } from '../helpers';

export class DiscountCodeApi {
  getDiscountList() {
    cy.request({
      method: 'GET',
      url: `${Cypress.env('discountBaseUrl')}/api/v1/discount-code`,
      headers: {
        Authorization: getTokenAuthorization(),
      },
      failOnStatusCode: false
    }).as('discountList');
  }

  getDiscountRedemptionList() {
    cy.request({
      method: 'GET',
      url: `${Cypress.env('discountBaseUrl')}/api/v1/redemption-history`,
      headers: {
        Authorization: getTokenAuthorization(),
      },
      failOnStatusCode: false
    }).as('discountRedemptionList');
  }

  getDiscountCodeDetails(codeId) {
    cy.request({
      method: 'GET',
      url: `${Cypress.env('discountBaseUrl')}/api/v1/discount-code/${codeId}`,
      headers: {
        Authorization: getTokenAuthorization(),
      },
      failOnStatusCode: false
    }).as('discountCodeDetails');
  }
}

export const discountApi = new DiscountCodeApi();
