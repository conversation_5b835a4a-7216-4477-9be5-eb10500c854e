import { apiEndpoints } from "../endpoints";
import { getCanaryHeaders, getTokenAuthorization } from "../helpers";
import { Attribute } from "../interfaces/attribute-model";
import { categoryName, germanMarketCode } from "../constants/pim-constants";
import Chainable = Cypress.Chainable;

export namespace ServiceCategoryApi {
  export class MarketApi {
    //#region "Get Market list Endpoint"
    getAvailableMarkets(): void {
      cy.request({
        method: "GET",
        url: `${Cypress.env("categoryBaseUrl") + apiEndpoints.markets}`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("getAvailableMarkets");
    }

    //#endregion "Get market list Endpoint"
  }

  export class BrandApi {
    //#region "GET Brand List & Brand Details Endpoints"
    getBrands(queryParams: string): void {
      cy.request({
        method: "GET",
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.brands
        }?${queryParams}`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("getBrandsFiltered");
    }

    getBrandById(queryParams: string, id: string): void {
      cy.request({
        method: "GET",
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.brandDetails(id)
        }?${queryParams}`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
        failOnStatusCode: false,
      }).as("getBrandByIdFiltered");
    }

    //#endregion "GET Brand List & Brand Details Endpoints"

    //#region "Update a Brand Endpoint"
    update(itemId: string, body: any): any {
      cy.request({
        failOnStatusCode: false,
        method: "PUT",
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.brandDetails(itemId)
        }`,
        form: true,
        body: JSON.stringify(body),
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("updateBrand");
    }

    //#endregion "Update a Brand Endpoint"

    //#region "Create a new Brand Endpoint"
    create(body: any): any {
      cy.request({
        failOnStatusCode: false,
        method: "POST",
        url: `${Cypress.env("categoryBaseUrl") + apiEndpoints.brands}`,
        form: true,
        body: JSON.stringify(body),
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("createBrand");
    }

    //#endregion "Create a new Brand Endpoint"
  }

  export class MeasureUnitApi {
    //#region "Get Measure Unit list Endpoint"
    getMeasureUnits(queryParams?: string): void {
      cy.request({
        method: "GET",
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.measureUnits
        }?${queryParams!!}`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("getMeasureUnitsFiltered");
    }

    //#endregion "Get Measure Unit list Endpoint"
  }

  export class LovApi {
    //#region "Get lov list per market"
    getLovsByMarket(market: string, queryParams?: string): void {
      cy.request({
        method: "GET",
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.lovsByMarket(market)
        }?${queryParams!!}`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
        failOnStatusCode: false,
      }).as("getLovsByMarketFiltered");
    }

    //#endregion "Get lov list per market"

    //#region "Get LOV detail"
    getLovDetails(id: string): void {
      cy.request({
        method: "GET",
        url: `${Cypress.env("categoryBaseUrl") + apiEndpoints.lovDetails(id)}`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
        failOnStatusCode: false,
      }).as("getLovDetails");
    }

    //#endregion "Get LOV detail"

    //#region "Update LOV"
    updateLov(id: string, body: any): any {
      cy.request({
        failOnStatusCode: false,
        method: "PUT",
        url: `${Cypress.env("categoryBaseUrl") + apiEndpoints.lovDetails(id)}`,
        form: true,
        body: body,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("updateLov");
    }

    //#endregion "Update LOV"

    //#region "Create a new LOV"
    createLov(body: any): any {
      cy.request({
        failOnStatusCode: false,
        method: "POST",
        url: `${Cypress.env("categoryBaseUrl") + apiEndpoints.lov}`,
        form: true,
        body: JSON.stringify(body),
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("createLovFiltered");
    }

    //#endregion "Create a new LOV"
  }

  export class CategoryApi {
    //#region "List of root categories & child categories"
    getRootCategories(acceptLanguage: string = null): void {
      cy.request({
        method: "GET",
        url: `${Cypress.env("categoryBaseUrl") + apiEndpoints.categories}`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
          "accept-language": acceptLanguage,
        },
      }).as("getRootCategories");
    }

    getChildCategories(id: string): any {
      cy.request({
        failOnStatusCode: false,
        method: "GET",
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.childCategory(id)
        }`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("getChildCategories");
    }

    //#endregion "List of root categories & child categories"

    //#region "Get Category details"
    getCategoryDetail(categoryId): void {
      cy.request({
        method: "GET",
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.category(categoryId)
        }`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("getCategoryDetail");
    }

    getCategoryDetailByMarket(market: string, categoryId: string): void {
      cy.request({
        method: "GET",
        url: `${
          Cypress.env("categoryBaseUrl") +
          apiEndpoints.categoryDetailByMarket(market, categoryId)
        }`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("getCategoryDetailByMarket");
    }

    //#endregion "Get Category details"

    //#region "update existing category"
    updateCategoryDetail(market: string, id: string, body: any): void {
      cy.request({
        failOnStatusCode: false,
        method: "PUT",
        url: `${
          Cypress.env("categoryBaseUrl") +
          apiEndpoints.categoryDetails(market, id)
        }`,
        body: body,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("updateCategoryDetail");
    }

    updateCategoryState(body: any, market: string, id: string): any {
      cy.request({
        failOnStatusCode: false,
        method: "PUT",
        url: `${
          Cypress.env("categoryBaseUrl") +
          apiEndpoints.categoryStateByMarket(market, id)
        }`,
        form: true,
        body: JSON.stringify(body),
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("updateCategoryState");
    }

    //#endregion "update existing category"

    //#region "Create root category, child category and itemType"
    createRootCategory(body: any): any {
      cy.request({
        failOnStatusCode: false,
        method: "POST",
        url: `${Cypress.env("categoryBaseUrl") + apiEndpoints.categories}`,
        form: true,
        body: JSON.stringify(body),
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("createRootCategory");
    }

    createChildCategory(body: any, id: string): any {
      cy.request({
        failOnStatusCode: false,
        method: "POST",
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.childCategory(id)
        }`,
        form: true,
        body: JSON.stringify(body),
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("createChildCategory");
    }

    createItemType(market: string, id: string, body: any): any {
      cy.request({
        failOnStatusCode: false,
        method: "POST",
        body: JSON.stringify(body),
        url: `${
          Cypress.env("categoryBaseUrl") +
          apiEndpoints.createCategoryItemTypeForMarket(market, id)
        }`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("createItemType");
    }

    public getItemTypeId = (): Chainable<string> => {
      const searchTerm = categoryName;
      const market = germanMarketCode;
      const params = `?limit=50&offset=0&filter[search]=${encodeURIComponent(
        searchTerm
      )}`;
      const baseUrl = Cypress.env("categoryBaseUrl");
      const url = `${baseUrl}${apiEndpoints.categorySearchByName(
        market
      )}${params}`;
      const authHeader = {
        Authorization: getTokenAuthorization(),
        ...getCanaryHeaders(),
        "Accept-Language": "en",
      };

      return cy
        .request({
          method: "GET",
          url: url,
          headers: authHeader,
        })
        .then((response) => {
          const categories = response.body.items;
          const itemType = categories.find(
            (cat) => cat.isActive && cat.isItemType
          );

          return itemType.id;
        });
    };

    //#endregion "Create root category, child category and itemType"
  }

  export class CategoryAttributesApi {
    //#region "get list of assigned/ unassigned attributes to category"
    getCategoryAttributes(id: string, queryParams?: string): any {
      cy.request({
        method: "GET",
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.categoryAttributes(id)
        }?${queryParams!!}`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
        failOnStatusCode: false,
      }).as("getCategoryAttributes");
    }

    getCategoryAssignedAttributes(market: string, id: string): any {
      cy.request({
        method: "GET",
        url: `${
          Cypress.env("categoryBaseUrl") +
          apiEndpoints.categoryAssignedAttributes(market, id)
        }`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
        failOnStatusCode: false,
      }).as("getCategoryAssignedAttributes");
    }

    getUnassignedCategoryAttributes(id): any {
      cy.request({
        method: "GET",
        url: `${
          Cypress.env("categoryBaseUrl") +
          apiEndpoints.unassignedCategoryAttributes(id)
        }`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
        failOnStatusCode: false,
      }).as("getUnassignedCategoryAttributes");
    }

    //#endregion "get list of assigned/ unassigned attributes to category"

    //#region "Assign/Unassign attributes to category"
    addCategoryAttributes(body: any, id: string): any {
      cy.request({
        method: "POST",
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.categoryAttributes(id)
        }`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
        body: JSON.stringify(body),
        failOnStatusCode: false,
      }).as("addCategoryAttributes");
    }

    unassignAttributesFromCategory(body: any, id: string): any {
      cy.request({
        method: "POST",
        url: `${
          Cypress.env("categoryBaseUrl") +
          apiEndpoints.unassignCategoryAttributes(id)
        }`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
        body: JSON.stringify(body),
        failOnStatusCode: false,
      }).as("unassignAttributesFromCategory");
    }

    //#endregion "Assign/Unassign attributes to category"

    //#region "Set base price attribute for category"
    addBasePriseToCategory(data: any, id: string): any {
      cy.request({
        failOnStatusCode: false,
        method: "POST",
        body: data,
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.categoryBasePrise(id)
        }`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("addBasePriseToCategory");
    }

    //#endregion "Set base price attribute for category"
  }

  export class AttributeApi {
    //#region "GET Attribute List & Attribute Details Endpoints"
    getAttributes(market: string, params = ""): void {
      let url = `${
        Cypress.env("categoryBaseUrl") + apiEndpoints.attributes(market)
      }${params}`;
      cy.request({
        method: "GET",
        url: url,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
        failOnStatusCode: false,
      }).as("getAttributes");
    }

    getAttribute(id: string) {
      let url = `${
        Cypress.env("categoryBaseUrl") + apiEndpoints.attributesById(id)
      }`;
      cy.request({
        method: "GET",
        url: url,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
        failOnStatusCode: false,
      }).as("getAttributeById");
    }

    getAttributeByMarket(market: string, id: string, params = ""): void {
      let url = `${
        Cypress.env("categoryBaseUrl") +
        apiEndpoints.attributesByMarket(market, id)
      }${params}`;
      cy.request({
        method: "GET",
        url: url,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
        failOnStatusCode: false,
      }).as("getAttributeByMarket");
    }

    //#endregion "GET Attribute List & Attribute Details Endpoints"

    //#region "Update exist attribute"
    updateAttribute(data: Attribute, market: string, attributeId: string): any {
      cy.request({
        failOnStatusCode: false,
        method: "PUT",
        body: data,
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.attributes(market)
        }/${attributeId}`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("updateAttribute");
    }

    //#endregion "Update exist attribute"

    //#region "Create a new attribute"
    createAttribute(data: Attribute, market: string): any {
      cy.request({
        failOnStatusCode: false,
        method: "POST",
        body: data,
        url: `${
          Cypress.env("categoryBaseUrl") + apiEndpoints.attributes(market)
        }`,
        headers: {
          Authorization: getTokenAuthorization(),
          ...getCanaryHeaders(),
        },
      }).as("createAttribute");
    }

    //#endregion "Create a new attribute"
  }
}

export const serviceCategoryApi = {
  market: new ServiceCategoryApi.MarketApi(),
  brand: new ServiceCategoryApi.BrandApi(),
  measureUnit: new ServiceCategoryApi.MeasureUnitApi(),
  lov: new ServiceCategoryApi.LovApi(),
  category: new ServiceCategoryApi.CategoryApi(),
  categoryAttributes: new ServiceCategoryApi.CategoryAttributesApi(),
  attribute: new ServiceCategoryApi.AttributeApi(),
};
