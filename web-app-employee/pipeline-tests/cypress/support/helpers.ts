import { ACCESS_TOKEN_KEY } from "./constants/local-storage-constants";
import { serviceCategoryApi } from "./api/service-category-api";
import { categoryTreePage } from "../support/pages/employee/category-tree-page";

enum statusCodes {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
}

function getTokenAuthorization(accessToken = ACCESS_TOKEN_KEY): string {
  return "Bearer " + localStorage.getItem(accessToken);
}

function getCanaryHeaders(): Record<string, string> {
  const envToken = Cypress.env('CANARY_ENV_ACCESS_TOKEN');
  const canaryAccess = Cypress.env('CANARY_ACCESS');

  const canaryHeaders: Record<string, string> = {};
  if (envToken) {
    canaryHeaders['env-access-token'] = envToken;
  }

  if (canaryAccess) {
    canaryHeaders['canary-access'] = canaryAccess;
  }

  return canaryHeaders;
}

function getRandomItem(items) {
  if (!items.length) {
    throw new RangeError(`getRandomItem(): given array is empty`);
  }

  const randomID = Math.floor(Math.random() * items.length);

  return items[randomID];
}

function getLOVByName(items, name) {
  if (!items.length) {
    throw new RangeError(`getLOVgetLOVByNameById(): given array is empty`);
  }
  const item = items.filter((item) => item.name === name);
  return item[0].id;
}

function getRandomNumber(count) {
  return Math.floor(Math.random() * count);
}

function getTranslation(data: any, lang: string = "de") {
  return data.filter((item) => item.lang === lang)[0].properties.name;
}

function checkStatusCode(response: any, code: number = statusCodes.OK): void {
  if (response.status) {
    expect(response).property("status").to.equal(code);
  }

  if (response.statusCode) {
    expect(response).property("statusCode").to.equal(code);
  }
}

function getRandomString(
  length: number,
  opts?: { numbers?: boolean; lettersAndNumbers?: boolean }
) {
  opts = opts || {};

  const l = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
  const n = "01234567890";
  const ln = l + n;

  if (opts.numbers) {
    return Array.from({ length })
      .map(() => n.charAt(Math.floor(Math.random() * n.length)))
      .join("");
  }
  if (opts.lettersAndNumbers) {
    return Array.from({ length })
      .map(() => ln.charAt(Math.floor(Math.random() * ln.length)))
      .join("");
  }

  return Array.from({ length })
    .map(() => l.charAt(Math.floor(Math.random() * l.length)))
    .join("");
}

async function getChildCategory(itemWithChild): Promise<any> {
  if (itemWithChild) {
    serviceCategoryApi.category.getChildCategories(itemWithChild.id);

    cy.get("@getChildCategories").then((interception: any) => {
      const items = interception.body.items;
      let itemWithChild = categoryTreePage.getItemWithChild(items);

      if (!itemWithChild) {
        const child = items[0];
        cy.wrap(child).as("firstChildCategory");
      }
      getChildCategory(itemWithChild);
    });
  }
}

function getFileName(fullPath: string): string {
  return fullPath.replace(/^.*[\\\/]/, "");
}

function generateRandomChars() {
  return Math.random().toString(36).substring(2, 7);
}

function generateGtin(): string {
  const packageLevel = 0;
  const gs1Prefix = [0, 3];

  const gtinArray = [
    packageLevel,
    ...gs1Prefix,
    ...Array.from({ length: 10 }, () => Math.floor(Math.random() * 10)),
  ];
  const checkDigit = calculateGtinCheckDigit(gtinArray);
  gtinArray.push(checkDigit);

  return gtinArray.join("");
}

function calculateGtinCheckDigit(gtinArray) {
  const weightPattern = [3, 1];
  const sum = gtinArray.reduce(
    (acc, curr, index) => acc + curr * weightPattern[index % 2],
    0
  );
  const checkDigit = (10 - (sum % 10)) % 10;
  return checkDigit;
}

export function generateUploadFile(fileUrl: string): string {
  const filename = `updated_${getRandomString(5)}_${fileUrl.substring(
    fileUrl.lastIndexOf("/") + 1
  )}`;
  const newFileUrl = `../fixtures/${filename}`;

  cy.fixture(fileUrl).then((productData) => {
    let csvContent = productData;
    csvContent = csvContent.replace(/GTINPLACEHOLDER/g, () => generateGtin());
    csvContent = csvContent.replace(/PRODUCTNAMEPLACEHOLDER/g, () => {
      return `TEST PRODUCT (WARNING) DON'T TOUCH IT. ${generateRandomChars()}`;
    });
    cy.writeFile(`cypress/fixtures/${filename}`, csvContent, "utf8");
  });

  return newFileUrl;
}

export {
  statusCodes,
  getTokenAuthorization,
  getCanaryHeaders,
  getRandomItem,
  getLOVByName,
  getRandomNumber,
  getTranslation,
  checkStatusCode,
  getRandomString,
  getChildCategory,
  getFileName,
  generateRandomChars,
  generateGtin,
};
