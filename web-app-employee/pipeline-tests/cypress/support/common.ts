import { apiRequests } from "./api/api";

export const checkOrderNumber = (orderNumber: string, attempt = 0) => {
  if (attempt > 30) {
    throw "Error";
  }
  apiRequests.getSellerOrders();
  cy.task("getSellerOrders").then((orders) => {
    if (orders[0]?.orderNumber !== orderNumber) {
      cy.wait(10000);
      checkOrderNumber(orderNumber, attempt + 1);
      cy.reload();
    }
  });
};

export const checkResponseStatus404 = (response: any): void => {
  expect(response.status).to.eq(404);
  expect(response.body.title).to.eq("Not found");
};

export const checkIfDataHasMainProperties = (data: any): void => {
  expect(data).to.have.property("totalCount");
  expect(data).to.have.property("items");
};

export const checkIfArrayNotEmpty = (object: any): void => {
  assert.isArray(object);
  expect(object).not.to.be.empty;
};

export const checkInvalidMarketResponse = (response: any): void => {
  const detailMessage = "Market not found";
  expect(response.body.detail).to.be.eq(detailMessage);
  checkResponseStatus404(response);
};
