import { getRandomNumber, getTranslation } from "../support/helpers";
import { count } from "../support/constants/pim-constants";
import { ILov } from "../support/interfaces/lov-model";
import { translationsBuilder } from "../builders/pim-translations-builder";
import { optionsBuilder } from "./pim-lov-option-builder";
import {
  Languages,
  LOV_SUFFIXES,
} from "../support/constants/pim-lov-constants";

export function createLovBuilder(): ILov {
  const randomNumber = getRandomNumber(count);
  const name = `test_prop_en_${randomNumber}`;

  return {
    options: optionsBuilder(3),
    translations: [
      {
        lang: Languages.EN,
        properties: {
          name,
        },
      },
    ],
    translatable: true,
  };
}
export function createLovWithEmptyNamesBuilder(): ILov {
  return {
    options: optionsBuilder(3),
    translations: [],
    translatable: true,
  };
}
export function createNonUniqueLovBuilder(): ILov {
  const randomNumber = getRandomNumber(count);
  const uniqOptionEn = `unique_option_en_${randomNumber}`;
  const uniqOptionDe = `unique_option_de_${randomNumber}`;
  const nonUniqTranslationEn = "existing_name_en";
  const nonUniqTranslationDe = "existing_name_de";

  return {
    options: [
      {
        translations: translationsBuilder(null, uniqOptionEn, uniqOptionDe),
      },
    ],
    translations: translationsBuilder(
      null,
      nonUniqTranslationEn,
      nonUniqTranslationDe
    ),
  };
}

export function createLovWithoutOptionsBuilder(): ILov {
  const randomNumber = getRandomNumber(count);
  const uniqTranslationEn = `test_prop_en_${randomNumber}`;
  const uniqTranslationDe = `test_prop_de_${randomNumber}`;

  return {
    options: [],
    translations: translationsBuilder(
      null,
      uniqTranslationEn,
      uniqTranslationDe
    ),
  };
}

export function createLovWithEmptyOptionsBuilder(): ILov {
  const randomNumber = getRandomNumber(count);
  const uniqTranslationEn = `test_prop_en_${randomNumber}`;
  const uniqTranslationDe = `test_prop_de_${randomNumber}`;

  return {
    options: [
      {
        translations: translationsBuilder(null, "", ""),
      },
    ],
    translations: translationsBuilder(
      null,
      uniqTranslationEn,
      uniqTranslationDe
    ),
  };
}

export function createLovWithRepeatedOptionsBuilder(): ILov {
  const randomNumber = getRandomNumber(count);
  const uniqTranslationEn = `test_prop_en_${randomNumber}`;
  const uniqTranslationDe = `test_prop_de_${randomNumber}`;

  return {
    options: [
      {
        translations: translationsBuilder(
          null,
          uniqTranslationEn,
          uniqTranslationDe
        ),
      },
      {
        translations: translationsBuilder(
          null,
          uniqTranslationEn,
          uniqTranslationDe
        ),
      },
    ],
    translations: translationsBuilder(
      null,
      uniqTranslationEn,
      uniqTranslationDe
    ),
  };
}

export function updateLovBuilder(data: ILov): ILov {
  const updatedOptions = data.options.map((option) => {
    option.translations.forEach((translation) => {
      translation.properties.name = `${translation.properties.name}_${LOV_SUFFIXES.UPDATED}`;
    });
    option.mappings.forEach((mapping) => {
      mapping.name = `${mapping.name}_${LOV_SUFFIXES.UPDATED}`;
    });

    return option;
  });

  const updatedTranslations = data.translations
    .filter((translation) => translation.lang === Languages.EN)
    .map((translation) => {
      translation.properties.name = `${translation.properties.name}_${LOV_SUFFIXES.UPDATED}`;
      return translation;
    });

  return {
    translatable: data.translatable,
    options: updatedOptions,
    translations: updatedTranslations,
  };
}

export function updateLovWithNonUniqueNameBuilder(data, propNames): ILov {
  const optionTranslationEn = getTranslation(
    data.options[0].translations,
    "en"
  );
  const optionTranslationDe = getTranslation(
    data.options[0].translations,
    "de"
  );

  return {
    options: [
      {
        id: null,
        translations: translationsBuilder(
          null,
          optionTranslationEn,
          optionTranslationDe
        ),
      },
    ],
    translations: translationsBuilder(null, propNames["en"], propNames["de"]),
  };
}

export function updateLovWithEmptyNameBuilder(): ILov {
  return {
    translations: translationsBuilder(),
  };
}

export function updateLovWithoutOptionsBuilder(): ILov {
  return {
    options: [
      {
        translations: translationsBuilder(),
      },
    ],
  };
}

export function updateLovWithEmptyOptionsBuilder(): ILov {
  return {
    options: [
      {
        id: null,
        name: "",
      },
    ],
  };
}

export function updateLovWithRepeatingOptionsBuilder(): ILov {
  const randomNumber = getRandomNumber(count);
  const uniqOptionEn = `unique_option_en_${randomNumber}`;
  const uniqOptionDe = `unique_option_de_${randomNumber}`;

  return {
    options: [
      {
        id: null,
        name: uniqOptionEn,
      },
      {
        id: null,
        name: uniqOptionDe,
      },
    ],
  };
}

export function updateLovByNonExistingIdBuilder(): ILov {
  const randomNumber = getRandomNumber(count);
  const uniqOptionEn = `unique_option_en_${randomNumber}`;

  return {
    options: [
      {
        id: null,
        name: uniqOptionEn,
      },
    ],
  };
}
