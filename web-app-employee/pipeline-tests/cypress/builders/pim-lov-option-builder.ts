import { ILovOption } from "../support/interfaces/lov-model";
import { getRandomNumber } from "../support/helpers";
import { mappingsBuilder } from "./pim-mappings-builder";
import {
  Languages,
  LOV_NUMBER_OF_TRANSFORMATIONS,
} from "../support/constants/pim-lov-constants";
import { ITranslation } from "../support/interfaces/translation-model";

export function optionsBuilder(count: number): ILovOption[] {
  const options: ILovOption[] = [];
  const optionPrefix = "test_option";

  for (let i = 0; i < count; i++) {
    const translations: ITranslation[] = [];
    const randomNumber = getRandomNumber(10000);

    Object.values(Languages).forEach((language) => {
      translations.push({
        lang: language,
        properties: {
          name: `${optionPrefix}_${i}_${language}_${randomNumber}`,
        },
      });
    });

    options.push({
      mappings: mappingsBuilder(LOV_NUMBER_OF_TRANSFORMATIONS),
      translations: translations,
    });
  }

  return options;
}
