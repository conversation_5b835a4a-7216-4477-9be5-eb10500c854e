import { ILovMappings } from "../support/interfaces/lov-model";
import { getRandomNumber } from "../support/helpers";

export function mappingsBuilder(count: number): ILovMappings[] {
  const mappings = [];
  const randomNumber = getRandomNumber(10000);
  const mappingPrefix = "test_mapping";

  for (let i = 0; i < count; i++) {
    mappings.push({
      name: `${mappingPrefix}_${i}_${randomNumber}`,
    });
  }

  return mappings;
}
