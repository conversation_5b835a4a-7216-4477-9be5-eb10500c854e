import { getRandomNumber } from '../support/helpers';
import { count } from '../support/constants/pim-constants';
import { ICategory, ICategoryTax } from '../support/interfaces/category-model';
import { translationsBuilder } from '../builders/pim-translations-builder';

function categoryDetailBuilder(params): ICategory {
  const category: ICategory = {
    transactionFee: params.transactionFee,
    metroReportingCategory: params.metroReportingCategory,
    taxes: taxesBuilder(params.taxesAmount),
    translations: translationsBuilder(params),
  };

  return category;
}

function categoryWithTaxesBuilder(params): ICategory {
  const category: ICategory = {
    taxes: taxesBuilder(params.taxValue),
    translations: translationsBuilder(params)
  };

  return category;
}

function categoryWithoutTaxesBuilder(params): ICategory {
  const category: ICategory = {
    translations: translationsBuilder(params)
  };

  return category;
}

function itemTypeBuider(params): ICategory {
  const randomNumber = getRandomNumber(count);
  const uniqueNameEn = `unique_name_en_${randomNumber}`;
  const uniqueNameDe = `unique_name_de_${randomNumber}`;
  const metroReportingCategory = `unique_metro_reporting_category_${randomNumber}`;

  const category: ICategory = {
    transactionFee: params.transactionFee,
    metroReportingCategory: metroReportingCategory,
    taxes: taxesBuilder(params.taxValue),
    translations: translationsBuilder(params, uniqueNameEn, uniqueNameDe),
  };

  return category;
}

function taxesBuilder(taxesValue: number): ICategoryTax[] {
  return [{
    type: "VAT",
    amount: taxesValue
  }]
}

export {
  categoryDetailBuilder,
  categoryWithTaxesBuilder,
  categoryWithoutTaxesBuilder,
  itemTypeBuider,
}
