import { acceptLanguages, count } from '../support/constants/pim-constants';
import {
  Attribute,
  AttributeCondition,
  AttributeDetails,
  AttributeTypeDetails,
  AttributeUnit
} from '../support/interfaces/attribute-model';
import { getRandomNumber } from '../support/helpers';
import { AttributeType } from '../support/constants/pim-enums';

const attributeFixturesByType: AttributeTypeDetails[] = [];

function getAttributeFixture(attributeType: AttributeType): AttributeTypeDetails {
  return attributeFixturesByType.find((attribute) => attribute.type === attributeType);
}

function attributeListBuilder(items, relevanceArray) {
  const attributes = [];

  for (let i = 0; i < relevanceArray.length; i++) {
    attributes.push({
      attributeId: items[i].id,
      relevance: relevanceArray[i]
    });
  }
  return attributes;
}

function mapAttribute(
  attribute: any,
  translationName: string,
  condition: AttributeCondition = {
  unique: false,
  filterable: false,
  multiple: false,
},
  unit: AttributeUnit = null,
  LOVId: string = null,
  frontEndFlag = "2"
): Attribute {
  attribute.lovId = LOVId;
  attribute.isLocalizable = false;
  attribute.frontend = frontEndFlag;
  attribute.translations = testTranslations(translationName);
  attribute.unit = unit;
  attribute.condition = condition;

  return attribute;
}

function attributeBuilder({
  type = null,
  unit = null,
  lovId = null,
  frontEndFlag = '1',
  translationFirstLang = acceptLanguages[1],
  name = null,
}: AttributeDetails) {
  const translationSecondLang = acceptLanguages[0];
  const attributeDescription = name ? name : 'cypress test attribute';
  let attributeName = name ? name : `CYTest${getRandomNumber(count)}`;
  // For empty mandatory fields test set name as null if type is also null
  attributeName = type ? attributeName : null;

  return new Attribute(
    attributeName,
    { unique: false, filterable: false, multiple: false },
    false,
    [
      {
        lang: translationFirstLang,
        properties: {
          name: attributeName,
          example: '',
          description: attributeDescription,
        },
      },
      {
        lang: translationSecondLang,
        properties: {
          name: attributeName,
          example: '',
          description: attributeDescription,
        },
      },
    ],
    type,
    unit,
    frontEndFlag,
    lovId
  );
}

function testTranslations(translationName: string) {
  return [{
    lang: acceptLanguages[1],
    properties: {
      name: translationName,
      example: translationName,
      description: translationName,
    },
  },
    {
      lang: acceptLanguages[0],
      properties: {
        name: translationName,
        example: translationName,
        description: translationName,
      },
    },
  ];
}

export {
  attributeFixturesByType,
  getAttributeFixture,
  attributeListBuilder,
  mapAttribute,
  attributeBuilder,
}
