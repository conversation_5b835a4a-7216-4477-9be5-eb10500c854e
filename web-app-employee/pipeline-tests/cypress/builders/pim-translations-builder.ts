import { ITranslation } from '../support/interfaces/translation-model';

export function translationsBuilder(params = null, translationEn = null, translationDe = null): ITranslation[] {
  return [{
    lang: 'de',
    properties: {
      name: params && (params.name && params.name.de || params.uniqueNameDe) || translationDe
    }
  },
    {
      lang: 'en',
      properties: {
        name: params && (params.name && params.name.en || params.uniqueNameEn) || translationEn
      }
    }]
}
