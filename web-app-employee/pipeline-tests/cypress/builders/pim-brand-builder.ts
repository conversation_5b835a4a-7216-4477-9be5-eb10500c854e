import { IBrand }  from '../support/interfaces/brand-model';

export function brandBuilder(params = null) {
  const mappings = [];
  let brand: IBrand = null;

  if (!params) {
    brand = {
      name: null,
      mappings,
    };

    return brand;
  }

  if (params.mappings) {
    for (let i = 0; i < params.mappings.length; i++) {
      mappings.push({
        name: params.mappings[i].name,
      });
    };
  }

  brand = {
    name: params.name,
    mappings,
  };

  return brand;
}
