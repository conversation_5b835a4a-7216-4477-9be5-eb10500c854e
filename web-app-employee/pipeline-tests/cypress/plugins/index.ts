/* eslint-disable @typescript-eslint/no-var-requires */
/// <reference types="cypress" />
// ***********************************************************
// This example plugins/index.js can be used to load plugins
//
// You can change the location of this file or turn off loading
// the plugins file with the 'pluginsFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/plugins-guide
// ***********************************************************

// This function is called when a project is opened or re-opened (e.g. due to
// the project's config changing)

/**
 * @type {Cypress.PluginConfig}
 */
// eslint-disable-next-line no-unused-vars

let orderNumber: string;
let sellerOrders: string;
let discountCodeId: string;
const fs = require("fs");
const path = require("path");

module.exports = (on) => {
  on("task", {
    setOrderNumber: (val: string) => (orderNumber = val),
    getOrderNumber: () => orderNumber,
    setSellerOrders: (val: string) => (sellerOrders = val),
    getSellerOrders: () => sellerOrders,
    setDiscountCodeId: (val: string) => (discountCodeId = val),
    getDiscountCodeId: () => discountCodeId,
    deleteFile(fileName) {
      const filePath = path.resolve("cypress/downloads", fileName);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      return null;
    },
  });

  on("before:browser:launch", (browser, launchOptions) => {
    if (browser.name === "chrome" && browser.isHeadless) {
      launchOptions.args = launchOptions.args.map((arg) => {
        if (arg === "--headless") {
          return "--headless=new";
        }
        return arg;
      });
    }
    return launchOptions;
  });
};
