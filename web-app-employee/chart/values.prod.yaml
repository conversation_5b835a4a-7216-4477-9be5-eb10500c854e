global:
  envName: prod
  envType: static

web:
  replicaCount: 3
  nodeSelector: null

  hpa:
    enabled: true
    maxReplicas: 10

  ingress:
    whitelistMap:
      intranet:
        - path: "/"
          host: "backoffice.de.metro-marketplace.cloud"

  extraAllowlist:
    intranet:
      - *************/32     # Central RTD (primary) # COPS-11502
      - ***************/32   # Iberia(backup) # COPS-11502
      - ************/32  # additional IPs # COPS-11574
      - *************/32  # additional IPs # COPS-11574
      - ***************/27 # additional IPs # COPS-13656
      - *************/26 # additional IPs # COPS-13656

  resources:
    requests:
      memory: "256Mi"
      cpu: "50m"
    limits:
      memory: "1Gi"
      cpu: "1"

  config:
    CAPTCHA_SITE_KEY: "6Le21rMUAAAAABfUETJmm8d1P3JLpXRKTOCHz627"
    CONFIGCAT_SDK_KEY: "DVPXCI0if81SA9_CiLstKA/SeTBC6KLdE27ChoFlFADig"
    FF_TEMP_ATTRIBUTE_MAPPING_SERVICE: "0"
    FF_TEMP_USE_NEW_PAYMENT_SERVICE: "1"
    FF_TEMP_MPGD9059_NEW_TRANSACTION_LIST: "1"
    FF_TEMP_MPGD9353_NEW_TRANSACTION_REFUND_REQUEST: "1"
    FF_TEMP_MPGD_10266_MANUFACTURER_NAME_VALIDATION: "1"
    FF_TEMP_ORDERLINES_ACCEPT_RETURN_MPGD_10802: "1"
    GOOGLE_RECAPTCHA_SECRET: "6Le21rMUAAAAALWCgBGMc4hLAlSy0tdtvsqj-OtR"
    SENTRY_ENABLED: "true"
    SENTRY_DSN: "https://<EMAIL>/74"
    SVC_CATEGORY_BASE_URL: "https://service-category.prod.de.metro-marketplace.cloud"
    SVC_PAYMENT_BASE_URL: "https://service-payment.prod.de.metro-marketplace.cloud"
    SVC_VO_PORTAL_URL: "https://www.metro-vendoroffice.com/"
    SHARED_DB_USER: sa_web-app-employee
    FF_ANONYMOUS_EMAILS: "1"
    SVC_ACCOUNTING_BASE_URL: "https://service-payment-provider.prod.de.metro-marketplace.cloud"
    SVC_DISCOUNT_BASE_URL: "https://service-discount.prod.de.metro-marketplace.cloud"
    ICECAT_CONFIG: "270"
    DISCOUNT_CODE_SELLER_ID: "118ede85-fd10-42aa-8ee5-6fbc2553de02"
    MM_URL_DE_MAIN: "https://www.metro.de/marktplatz"
    MM_URL_ES_MAIN: "https://www.makro.es/marketplace"
    MM_URL_IT_MAIN: "https://www.metro.it/marketplace"
    MM_URL_PT_MAIN: "https://www.makro.pt/marketplace"
    MM_URL_NL_MAIN: "https://www.makro.nl/marketplace"
    MM_URL_FR_MAIN: "https://www.metro.fr/marketplace"
    SVC_FINANCIAL_REPORTS_BASE_URL: "https://service-financial-reports.prod.de.metro-marketplace.cloud"
    SVC_DISPUTES_SERVICE_BASE_URL: "https://service-dispute-management.prod.de.metro-marketplace.cloud"
    SVC_MODIFIED_INVOICING_URL: "https://service-payment-reports.prod.de.metro-marketplace.cloud"
    SVC_SERVICE_PIM_ROW_REVIEW_BASE_URL: "https://service-pim-row-review.prod.de.metro-marketplace.cloud"
    SVC_SERVICE_PIM_GOLDEN_RECORD_MERGE_BASE_URL: "https://service-pim-golden-record-merge.prod.de.metro-marketplace.cloud"
    SVC_SERVICE_MMC_BASE_URL: "https://service-multimarket-central.prod.de.metro-marketplace.cloud"
    GA_GTM_ID: "GTM-WGRNT9X"
    SVC_DUNNING_SERVICE_URL: "https://service-dunning.prod.de.metro-marketplace.cloud"
    SVC_MESSAGE_CENTER_SERVICE_BASE_URL: "https://service-message-center.prod.de.metro-marketplace.cloud"
    SVC_BUYER_CATEGORY_BASE_URL: "https://app-buyer-categories.prod.de.metro-marketplace.cloud"
