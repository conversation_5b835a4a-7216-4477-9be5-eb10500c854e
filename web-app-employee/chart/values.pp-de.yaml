global:
  envName: pp-de
  envType: static

web:
  replicaCount: 1
  nodeSelector: null

  hpa:
    enabled: false

  component: web

  ingress:
    whitelistMap:
      intranet:
        - path: "/"
          host: "web-app-employee.pp-de.metro-marketplace.cloud"
        - path: "/"
          host: "web-app-employee.de.pp-de.metro-marketplace.cloud"
        - path: "/"
          host: "web-app-employee.es.pp-de.metro-marketplace.cloud"
        - path: "/"
          host: "web-app-employee.it.pp-de.metro-marketplace.cloud"
        - path: "/"
          host: "web-app-employee.pt.pp-de.metro-marketplace.cloud"
        - path: "/"
          host: "web-app-employee.nl.pp-de.metro-marketplace.cloud"
        - path: "/"
          host: "web-app-employee.fr.pp-de.metro-marketplace.cloud"
      public:
        - path: "/"
          host: "web-app-employee.cdn.${ENV_DOMAIN}.${DOMAIN}"

  config:
    CONFIGCAT_SDK_KEY: "DVPXCI0if81SA9_CiLstKA/YI0tv30tjk-PIXxthjS07w"
    FF_TEMP_ATTRIBUTE_MAPPING_SERVICE: "1"
    SVC_VO_PORTAL_URL: "https://www.pp.metro-vendorcentral.com"
    FF_ANONYMOUS_EMAILS: "1"
    SVC_ACCOUNTING_BASE_URL: "https://service-payment-provider.pp-de.metro-marketplace.cloud"
    SVC_DISCOUNT_BASE_URL: "https://service-discount.pp-de.metro-marketplace.cloud"
    ICECAT_CONFIG: "127"
    DISCOUNT_CODE_SELLER_ID: "b4b309e0-9f53-4c9b-b639-3913b7131996"
    MM_URL_DE_MAIN: "https://marketplace-pp.metro.de/marktplatz"
    MM_URL_ES_MAIN: "https://marketplace-pp.makro.es/marketplace"
    MM_URL_IT_MAIN: "https://marketplace-pp.metro.it/marketplace"
    MM_URL_PT_MAIN: "https://marketplace-pp.makro.pt/marketplace"
    MM_URL_NL_MAIN: "https://marketplace-pp.makro.nl/marketplace"
    MM_URL_FR_MAIN: "https://marketplace-pp.metro.fr/marketplace"
    SVC_FINANCIAL_REPORTS_BASE_URL: "https://service-financial-reports.pp-de.metro-marketplace.cloud"
    SVC_DISPUTES_SERVICE_BASE_URL: "https://service-dispute-management.pp-de.metro-marketplace.cloud"
    SVC_MODIFIED_INVOICING_URL: "https://service-payment-reports.pp-de.metro-marketplace.cloud"
    SVC_SERVICE_PIM_ROW_REVIEW_BASE_URL: "https://service-pim-row-review.pp-de.metro-marketplace.cloud"
    SVC_SERVICE_PIM_GOLDEN_RECORD_MERGE_BASE_URL: "https://service-pim-golden-record-merge.pp-de.metro-marketplace.cloud"
    SVC_SERVICE_MMC_BASE_URL: "https://service-multimarket-central.pp-de.metro-marketplace.cloud"
    GA_GTM_ID: "GTM-WGRNT9X"
    SVC_DUNNING_SERVICE_URL: "https://service-dunning.pp-de.metro-marketplace.cloud"
    SVC_MESSAGE_CENTER_SERVICE_BASE_URL: "https://service-message-center.pp-de.metro-marketplace.cloud"
    SVC_BUYER_CATEGORY_BASE_URL: "https://app-buyer-categories.pp-de.metro-marketplace.cloud"
