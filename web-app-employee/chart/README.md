# web-app-employee

![Version: 0.1.25](https://img.shields.io/badge/Version-0.1.25-informational?style=flat-square) ![Type: application](https://img.shields.io/badge/Type-application-informational?style=flat-square) ![AppVersion: 0.1.0](https://img.shields.io/badge/AppVersion-0.1.0-informational?style=flat-square)

A Helm chart for web-app-employee

## Requirements

| Repository | Name | Version |
|------------|------|---------|
| alias:harbor_mp | web | 3.1.5 |
| alias:metro-markets-cps | common-cloud-chart | 1.0.0 |

## Values

| Key | Type | Default | Description |
|-----|------|---------|-------------|
| global.application | string | `"web-app-employee"` |  |
| global.envName | string | `"dev"` |  |
| global.envType | string | `"static"` |  |
| global.labels.component | string | `"frontend"` |  |
| global.labels.shared | string | `"true"` |  |
| global.labels.state | string | `"active"` |  |
| global.labels.team | string | `"pdt_sll_devs"` |  |
| global.serviceEnv | string | `"web-app-employee-env"` |  |
| web.config.APP_BUYER_ACCOUNT_BASE_URL | string | `"${PROTOCOL}://app-buyer-account.${ENV}.${DOMAIN}"` |  |
| web.config.APP_NAME | string | `"${APPLICATION}"` |  |
| web.config.APP_PREFIX | string | `"${APPLICATION}.${ENV_DOMAIN}.${DOMAIN}"` |  |
| web.config.APP_URL | string | `"${PROTOCOL}://${APPLICATION}.${ENV_DOMAIN}.${DOMAIN}"` |  |
| web.config.APP_VERSION_FILE_PATH | string | `"/usr/src/web-app/version"` |  |
| web.config.CAPTCHA_SITE_KEY | string | `"6LcKsKUUAAAAABA7YHZYA37YivJkp0DJ-vLPjyAz"` |  |
| web.config.CONFIGCAT_SDK_KEY | string | `"DVPXCI0if81SA9_CiLstKA/9MC45q3WrEmbs6ljI4g2dA"` |  |
| web.config.FF_TEMP_MPGD7759_SELLER_ACCOUNTS | string | `"1"` |  |
| web.config.FF_TEMP_MPGD8851_SELLER_BACKUP_CAPTURE_QUEUE | string | `"1"` |  |
| web.config.FF_TEMP_MPGD9059_NEW_TRANSACTION_LIST | string | `"1"` |  |
| web.config.FF_TEMP_MPGD9353_NEW_TRANSACTION_REFUND_REQUEST | string | `"1"` |  |
| web.config.GA_GTM_ID | string | `"GTM-MXM4BSZ"` |  |
| web.config.GC_PRIVATE_APP_ENV_NAME | string | `"${ENV}-${APPLICATION}"` |  |
| web.config.GOOGLE_RECAPTCHA_SECRET | string | `"6LcKsKUUAAAAAJVOdy_lGJ3gg_oep6HF59glrXjV"` |  |
| web.config.SVC_AFTERSALES_BASE_URL | string | `"${PROTOCOL}://service-aftersales-v2.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_ANONYMOUS_EMAILS_BASE_URL | string | `"${PROTOCOL}://service-anonymous-emails.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_BASE_URL | string | `"${PROTOCOL}://${APPLICATION}.${ENV_DOMAIN}.${DOMAIN}"` |  |
| web.config.SVC_CATEGORY_BASE_URL | string | `"${PROTOCOL}://service-category.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_CHECKOUT_BASE_URL | string | `"${PROTOCOL}://app-checkout.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_CMS_BASE_URL | string | `"${PROTOCOL}://service-cms-v2.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_DATA_ACCESS_BASE_URL | string | `"${PROTOCOL}://service-data-access.${ENV_DOMAIN}.${DOMAIN}"` |  |
| web.config.SVC_DISCOUNT_BASE_URL | string | `"${PROTOCOL}://service-discount.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_DISPUTES_SERVICE_BASE_URL | string | `"${PROTOCOL}://service-dispute-management.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_DUNNING_SERVICE_URL | string | `"${PROTOCOL}://service-dunning.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_EMPLOYEE_BASE_URL | string | `"${PROTOCOL}://service-accounting.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_FILE_BASE_URL | string | `"${PROTOCOL}://service-file.${ENV_DOMAIN}.${DOMAIN}"` |  |
| web.config.SVC_FRAUD_BASE_URL | string | `"${PROTOCOL}://service-fraud.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_IMS_BASE_URL | string | `"${PROTOCOL}://service-ims-v2.${ENV_DOMAIN}.${DOMAIN}"` |  |
| web.config.SVC_IMS_URL | string | `"${PROTOCOL}://service-ims-v2.${ENV_DOMAIN}.${DOMAIN}"` |  |
| web.config.SVC_MM_CENTRAL_BASE_URL | string | `"${PROTOCOL}://service-multimarket-central.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_MODIFIED_INVOICING_URL | string | `"${PROTOCOL}://service-payment-reports.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_NOTIFICATION_BASE_URL | string | `"${PROTOCOL}://service-notification.${ENV_DOMAIN}.${DOMAIN}"` |  |
| web.config.SVC_ORDER_MANAGEMENT_BASE_URL | string | `"${PROTOCOL}://app-order-management.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_PAYMENT_BASE_URL | string | `"${PROTOCOL}://service-payment.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_PLATFORM_BASE_URL | string | `"${PROTOCOL}://platform.${ENV_DOMAIN}.${DOMAIN}"` |  |
| web.config.SVC_REFUND_REQUESTS_BASE_URL | string | `"${PROTOCOL}://service-refund-requests.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_SELLER_INVENTORY_BASE_URL | string | `"${PROTOCOL}://app-seller-inventory.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_SELLER_OFFICE_BASE_URL | string | `"${PROTOCOL}://app-seller-office.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_SELLER_PIM_BASE_URL | string | `"${PROTOCOL}://app-seller-pim.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_SERVICE_MMC_BASE_URL | string | `"${PROTOCOL}://service-multimarket-central.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_SERVICE_PIM_GOLDEN_RECORD_MERGE_BASE_URL | string | `"${PROTOCOL}://service-pim-golden-record-merge.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_SERVICE_PIM_PARSING_URL | string | `"${PROTOCOL}://service-pim-parsing.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_SERVICE_PIM_ROW_REVIEW_BASE_URL | string | `"${PROTOCOL}://service-pim-row-review.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_STOREFRONT_BASE_URL | string | `"${PROTOCOL}://app-storefront.${ENV}.${DOMAIN}"` |  |
| web.config.SVC_USER_ACCOUNT_BASE_URL | string | `"${PROTOCOL}://app-user-account.${ENV}.${DOMAIN}"` |  |
| web.config.VERSION_HOLDER | string | `"/usr/src/web-app/version"` |  |
| web.fullnameOverride | string | `"web-app-employee"` |  |
| web.helmTest.enabled | bool | `false` |  |
| web.hpa.enabled | bool | `false` |  |
| web.ingress.whitelistMap.intranet[0].path | string | `"/"` |  |
| web.nameOverride | string | `"web-app-employee"` |  |

----------------------------------------------
Autogenerated from chart metadata using [helm-docs v1.14.2](https://github.com/norwoodj/helm-docs/releases/v1.14.2)
