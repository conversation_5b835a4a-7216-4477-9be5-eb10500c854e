global:
  application: web-app-employee
  serviceEnv: web-app-employee-env
  labels:
    team: pdt_sll_devs
    state: active
    component: frontend
    shared: "true"
  envType: static
  envName: dev

web:
  nameOverride: web-app-employee
  fullnameOverride: web-app-employee

  hpa:
    enabled: false

  ingress:
    whitelistMap:
      intranet:
        - path: "/"

  helmTest:
    enabled: false

  config:
    APP_URL: ${PROTOCOL}://${APPLICATION}.${ENV_DOMAIN}.${DOMAIN}
    APP_PREFIX: ${APPLICATION}.${ENV_DOMAIN}.${DOMAIN}
    APP_NAME: ${APPLICATION}
    # GAE_VERSION: ${APPLICATION}
    GC_PRIVATE_APP_ENV_NAME: ${ENV}-${APPLICATION}
    CAPTCHA_SITE_KEY: "6LcKsKUUAAAAABA7YHZYA37YivJkp0DJ-vLPjyAz"
    GOOGLE_RECAPTCHA_SECRET: "6LcKsKUUAAAAAJVOdy_lGJ3gg_oep6HF59glrXjV"
    GA_GTM_ID: "GTM-MXM4BSZ"
    SVC_BASE_URL: "${PROTOCOL}://${APPLICATION}.${ENV_DOMAIN}.${DOMAIN}"
    SVC_PLATFORM_BASE_URL: "${PROTOCOL}://platform.${ENV_DOMAIN}.${DOMAIN}"
    SVC_IMS_BASE_URL: "${PROTOCOL}://service-ims-v2.${ENV_DOMAIN}.${DOMAIN}"
    SVC_IMS_URL: "${PROTOCOL}://service-ims-v2.${ENV_DOMAIN}.${DOMAIN}"
    SVC_FILE_BASE_URL: "${PROTOCOL}://service-file.${ENV_DOMAIN}.${DOMAIN}"
    SVC_NOTIFICATION_BASE_URL: "${PROTOCOL}://service-notification.${ENV_DOMAIN}.${DOMAIN}"
    SVC_DATA_ACCESS_BASE_URL: "${PROTOCOL}://service-data-access.${ENV_DOMAIN}.${DOMAIN}"
    SVC_SELLER_OFFICE_BASE_URL: "${PROTOCOL}://app-seller-office.${ENV_DOMAIN}.${DOMAIN}"
    SVC_SELLER_PIM_BASE_URL: "${PROTOCOL}://app-seller-pim.${ENV_DOMAIN}.${DOMAIN}"
    SVC_SELLER_INVENTORY_BASE_URL: "${PROTOCOL}://app-seller-inventory.${ENV_DOMAIN}.${DOMAIN}"
    SVC_USER_ACCOUNT_BASE_URL: "${PROTOCOL}://app-user-account.${ENV_DOMAIN}.${DOMAIN}"
    SVC_CHECKOUT_BASE_URL: "${PROTOCOL}://app-checkout.${ENV_DOMAIN}.${DOMAIN}"
    SVC_STOREFRONT_BASE_URL: "${PROTOCOL}://app-storefront.${ENV_DOMAIN}.${DOMAIN}"
    SVC_ORDER_MANAGEMENT_BASE_URL: "${PROTOCOL}://app-order-management.${ENV_DOMAIN}.${DOMAIN}"
    SVC_AFTERSALES_BASE_URL: "${PROTOCOL}://service-aftersales-v2.${ENV_DOMAIN}.${DOMAIN}"
    SVC_ANONYMOUS_EMAILS_BASE_URL: "${PROTOCOL}://service-anonymous-emails.${ENV_DOMAIN}.${DOMAIN}"
    SVC_REFUND_REQUESTS_BASE_URL: "${PROTOCOL}://service-refund-requests.${ENV_DOMAIN}.${DOMAIN}"
    SVC_EMPLOYEE_BASE_URL: "${PROTOCOL}://service-accounting.${ENV_DOMAIN}.${DOMAIN}"
    SVC_PAYMENT_BASE_URL: "${PROTOCOL}://service-payment.${ENV_DOMAIN}.${DOMAIN}"
    SVC_CATEGORY_BASE_URL: "${PROTOCOL}://service-category.${ENV_DOMAIN}.${DOMAIN}"
    SVC_SERVICE_PIM_ROW_REVIEW_BASE_URL: "${PROTOCOL}://service-pim-row-review.${ENV_DOMAIN}.${DOMAIN}"
    SVC_SERVICE_PIM_GOLDEN_RECORD_MERGE_BASE_URL: "${PROTOCOL}://service-pim-golden-record-merge.${ENV_DOMAIN}.${DOMAIN}"
    SVC_SERVICE_MMC_BASE_URL: "${PROTOCOL}://service-multimarket-central.${ENV_DOMAIN}.${DOMAIN}"
    SVC_SERVICE_PIM_PARSING_URL: "${PROTOCOL}://service-pim-parsing.${ENV_DOMAIN}.${DOMAIN}"
    SVC_DISCOUNT_BASE_URL: "${PROTOCOL}://service-discount.${ENV_DOMAIN}.${DOMAIN}"
    SVC_CMS_BASE_URL: "${PROTOCOL}://service-cms-v2.${ENV_DOMAIN}.${DOMAIN}"
    FF_TEMP_MPGD8851_SELLER_BACKUP_CAPTURE_QUEUE: "1"
    FF_TEMP_MPGD7759_SELLER_ACCOUNTS: "1"
    FF_TEMP_MPGD9059_NEW_TRANSACTION_LIST: "1"
    FF_TEMP_MPGD9353_NEW_TRANSACTION_REFUND_REQUEST: "1"
    CONFIGCAT_SDK_KEY: "DVPXCI0if81SA9_CiLstKA/9MC45q3WrEmbs6ljI4g2dA"
    APP_VERSION_FILE_PATH: "/usr/src/web-app/version"
    VERSION_HOLDER: "/usr/src/web-app/version"
    SVC_MM_CENTRAL_BASE_URL: "${PROTOCOL}://service-multimarket-central.${ENV_DOMAIN}.${DOMAIN}"
    APP_BUYER_ACCOUNT_BASE_URL: "${PROTOCOL}://app-buyer-account.${ENV_DOMAIN}.${DOMAIN}"
    SVC_DISPUTES_SERVICE_BASE_URL: "${PROTOCOL}://service-dispute-management.${ENV_DOMAIN}.${DOMAIN}"
    SVC_FRAUD_BASE_URL: "${PROTOCOL}://service-fraud.${ENV_DOMAIN}.${DOMAIN}"
    SVC_MODIFIED_INVOICING_URL: "${PROTOCOL}://service-payment-reports.${ENV_DOMAIN}.${DOMAIN}"
    SVC_DUNNING_SERVICE_URL: "${PROTOCOL}://service-dunning.${ENV_DOMAIN}.${DOMAIN}"
    SVC_MESSAGE_CENTER_SERVICE_BASE_URL: "${PROTOCOL}://service-message-center.${ENV_DOMAIN}.${DOMAIN}"
    SVC_BUYER_CATEGORY_BASE_URL: "${PROTOCOL}://app-buyer-categories.${ENV_DOMAIN}.${DOMAIN}"
